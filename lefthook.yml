source_dir: "./node_modules/@fincloud/lib-git-hooks/.lefthook"
extends:
  - "./node_modules/@fincloud/lib-git-hooks/lefthook.yml"

pre-commit:
  piped: true
  scripts:
    "branch-type-validator.sh":
      priority: 1
      runner: bash
      skip:
        - merge
        - rebase
  commands:
    eslint-fix:
      priority: 2
      glob: "*.{ts,html}"
      run: npx eslint --fix --quiet --rule "@nx/select-style:0" {staged_files}
      stage_fixed: true
    eslint:
      priority: 3
      glob: "*.{ts,html}"
      run: npx eslint --quiet {staged_files}
