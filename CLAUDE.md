# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Fincloud UI library built with Angular 17 and Nx monorepo. The project contains:
- **libs/ui**: Main UI component library with 40+ components (buttons, modals, forms, etc.)
- **libs/utils**: Utility functions and Angular helpers
- **apps/fincloud-ui**: Demo application for the UI library

## Architecture

The library is structured as individual Angular modules for each component:
- Each component has its own folder under `libs/ui/`
- Components follow Angular best practices with OnPush change detection
- Uses Angular Material, PrimeNG, and custom styling with Tailwind CSS
- Storybook for component documentation and development
- Jest for unit testing

## Common Commands

### Development
```bash
# Build the UI library
npm run build
# or
nx run ui:prepare-publish

# Lint code
npm run lint
# or
nx run-many --target=lint --projects=ui,utils

# Fix linting issues
npm run lint:fix

# Run tests
nx test ui
nx test utils

# Start Storybook
nx storybook ui

# Build Storybook
npm run build:storybook
```

### Individual Component Development
```bash
# Test a specific component
nx test ui --testNamePattern="ComponentName"

# Lint specific projects
nx lint ui
nx lint utils

# Build specific library
nx build ui
nx build utils
```

## Key Technical Details

- **Prefix**: Components use `fin-` prefix (e.g., `fin-button`)
- **Styling**: Uses Tailwind CSS with custom theme configuration
- **Testing**: Jest with Angular testing utilities
- **Package Management**: npm with Nx workspace
- **Build System**: Nx with Angular CLI builders
- **Component Stories**: Each component should have corresponding `.stories.ts` files

## Development Workflow

1. Components are developed in isolation using Storybook
2. Each component is a standalone Angular module
3. Components export through `src/index.ts` for library consumption
4. Use `nx run ui:prepare-publish` to build the library for publishing
5. The library is published to private npm registry at `https://nexus-v2.neo.loan/repository/npm/`

## Testing

- Unit tests use Jest with Angular testing utilities
- Tests are located alongside components (`.spec.ts` files)
- Storybook tests can be run with `nx test-storybook ui`
- Use `nx test ui --watch` for development testing

## MCP Integration

The project uses MCP (Model Context Protocol) servers for enhanced development:
- **nx-mcp**: Local server at `http://localhost:9009/mcp` for Nx workspace operations
- **context7**: External service at `https://mcp.context7.com/mcp` for enhanced context

## Important Notes

- Always run lint and tests before committing changes
- Follow existing component patterns and naming conventions
- Use the existing utility functions from `libs/utils` where applicable
- Components should be self-contained with their own modules
- Maintain backward compatibility when making changes to existing components