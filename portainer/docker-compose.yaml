version: '3.8'

services:
  caddy:
    image: docker.nexus.neoshare.dev/devops/containers/tools/caddy-cloudflare:latest
    ports:
      - 80:80
      - 443:443
      - 443:443/udp
    networks:
      - lib-ui
    environment:
      - CLOUDFLARE_API_TOKEN=$CLOUDFLARE_API_TOKEN
      - CADDY_SITE_DOMAIN=$CADDY_SITE_DOMAIN
      - CADDY_HOST_NAME=lib-ui
      - CADDY_HOST_PORT=80
    restart: always

  lib-ui:
    image: docker-v2.neo.loan/fincloud/lib-ui:${DOCKER_TAG:-latest}
    ports:
      - "8080:80"
    networks:
      - lib-ui
    restart: unless-stopped

networks:
  lib-ui:
