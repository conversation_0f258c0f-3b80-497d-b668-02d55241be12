include:
  - project: fincloud/cfg-ci-cd
    ref: master
    file:
      - generic-nest-service.gitlab-ci.yml

variables:
  DOCKER_REPO: docker-v2.neo.loan/fincloud
  HELM_CHART_DIR: helm-chart
  YARN_ENABLE_GLOBAL_CACHE: 'false'
  SERVICE_BASENAME: lib-ui

deploy-master:
  extends: .portainer_deploy_webhook
  needs: [version, docker-build-and-push, publish-helm-chart]
  stage: deploy
  environment:
    name: master
    url: https://lib-ui.neoshare.de
    # kubernetes:
    #   namespace: lib-ui-master
  variables:
    # KUBE_CONTEXT: fincloud/cfg-ci-cd:non-production-development-green
    # HELM_RELEASE_NAME: lib-ui-master
    # HELM_CHART_VALUES:
    # HELM_CHART_VALUES_FILES: -f helm-chart/values-master.yaml
    # HELM_NAMESPACE: lib-ui-master
    PORTAINER_WEBHOOK_ENVS: 'DOCKER_TAG=$VERSION'
  rules:
    - if: $CI_COMMIT_REF_NAME == "master"
    - if: $CI_COMMIT_TAG

deploy-development:
  extends: .portainer_deploy_webhook
  needs: [version, docker-build-and-push, publish-helm-chart]
  stage: deploy
  environment:
    name: development
    url: https://lib-ui.neoshare.dev
    # kubernetes:
    #   namespace: lib-ui-master
  variables:
    # KUBE_CONTEXT: fincloud/cfg-ci-cd:non-production-development-green
    # HELM_RELEASE_NAME: lib-ui-development
    # HELM_CHART_VALUES:
    # HELM_CHART_VALUES_FILES: -f helm-chart/values-development.yaml
    # HELM_NAMESPACE: lib-ui-development
    PORTAINER_WEBHOOK_ENVS: 'DOCKER_TAG=$VERSION'
  rules:
    - if: $CI_COMMIT_REF_NAME == "development"

publish_npm_package:
  stage: release
  image: docker-proxy-v2.neo.loan/node:18
  needs: [docker-build-and-push]
  before_script:
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "CICD"
    - echo -e "machine git.neo.loan\nlogin gitlab-ci-token\npassword ${PROJECT_AUTOMATION_PUSH_TOKEN}" > ~/.netrc
    - chmod 600 ~/.netrc
  script:
    - git remote add project-origin https://oauth2:${PROJECT_AUTOMATION_PUSH_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}
    - git checkout $CI_COMMIT_BRANCH
    - git branch
    - npm install
    - git config pull.rebase false
    - git pull
    - npx nx release patch --skip-publish
    - npm run build
    - git pull
    - npx nx release publish
    - git push project-origin $CI_COMMIT_BRANCH -o ci.skip
    - git push --tags project-origin -o ci.skip
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: always

tag:
  stage: .post
  script:
    - echo "This job is intentionally left empty"
