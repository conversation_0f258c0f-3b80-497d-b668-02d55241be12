{"root": true, "ignorePatterns": ["**/*"], "plugins": ["@nx", "@fincloud/ns"], "overrides": [{"files": ["*.ts"], "excludedFiles": ["**/.storybook/main.ts"], "parserOptions": {"project": ["tsconfig.base.json", "libs/ui/.storybook/tsconfig.json"]}, "rules": {"@nx/enforce-module-boundaries": ["error", {"enforceBuildableLibDependency": true, "allow": [], "depConstraints": [{"sourceTag": "*", "onlyDependOnLibsWithTags": ["*"]}]}], "multiline-ternary": ["warn", "never"], "no-nested-ternary": "warn", "@typescript-eslint/naming-convention": ["error", {"selector": "enum", "format": ["PascalCase"]}, {"selector": "enumMember", "format": ["UPPER_CASE"]}], "no-restricted-syntax": ["error", {"selector": "Decorator[expression.callee.name='<PERSON><PERSON><PERSON><PERSON>']", "message": "The @UntilDestroy decorator is not allowed, use RxJS pipe takeUntilDestroy instead."}], "@fincloud/ns/no-missing-subject-suffix": "error"}}, {"files": ["*.ts"], "extends": ["plugin:@nx/typescript"], "rules": {}}, {"files": ["**/utils/*.ts", "**/utils/**/*.ts"], "rules": {"@typescript-eslint/naming-convention": ["error", {"selector": "variable", "modifiers": ["const", "exported"], "format": ["UPPER_CASE"]}, {"selector": "variable", "modifiers": ["const", "exported"], "types": ["function"], "format": ["camelCase"]}]}}, {"files": ["**/*.stories.ts"], "extends": ["plugin:storybook/recommended"]}, {"files": ["**/models/*.ts", "**/models/**/*.ts", "**/enums/*.ts", "**/enums/**/*.ts", "**/utils/*.ts", "**/utils/**/*.ts"], "excludedFiles": ["*.stories.ts"], "extends": ["plugin:@nx/typescript"], "rules": {"@fincloud/ns/no-multiple-exports": "error"}}, {"files": ["*.ts"], "excludedFiles": ["**/models/*.ts", "**/models/**/*.ts", "*.stories.ts"], "extends": ["plugin:@nx/typescript"], "rules": {"@fincloud/ns/no-export-interface": "error", "@fincloud/ns/no-declare-interface": "error", "@fincloud/ns/no-export-type": "error", "@fincloud/ns/no-declare-type": "error"}}, {"files": ["*.ts"], "excludedFiles": ["**/enums/*.ts", "**/enums/**/*.ts", "*.stories.ts"], "extends": ["plugin:@nx/typescript"], "rules": {"@fincloud/ns/no-export-enum": "error", "@fincloud/ns/no-declare-enum": "error"}}, {"files": ["*.ts"], "extends": ["plugin:@nx/typescript"], "rules": {"@fincloud/ns/no-missing-fin-prefix": ["error", {"decorators": ["Component", "Directive", "NgModule", "Injectable"]}]}}, {"files": ["*.ts", "*.stories.ts"], "excludedFiles": ["**/actions/*.ts", "**/environments/*.ts", "**/reducers/*.ts", "**/selectors/*.ts", "**/*.selectors.ts", "**/*.module.ts", "**/*.reducer.ts", "**/*.reducers.ts", "**/*.actions.ts", "**/index.ts", "**/utils/*.ts", "**/utils/**/*.ts", "**/locales/*.ts", "*.stories.ts", "**/.storybook/*"], "extends": ["plugin:@nx/typescript"], "rules": {"@fincloud/ns/no-export-constant": "error", "@fincloud/ns/no-declare-constant": "error"}}, {"files": ["*.js", "*.jsx"], "extends": ["plugin:@nx/javascript"], "rules": {}}, {"files": ["*.spec.ts", "*.spec.tsx", "*.spec.js", "*.spec.jsx"], "env": {"jest": true}, "rules": {}}]}