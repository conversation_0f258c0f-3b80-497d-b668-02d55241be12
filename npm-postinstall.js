const { exec } = require('child_process');

exec('git --version', (err, stdout, stderr) => {
  if (err) {
    // git is not installed
    console.log('git is not installed');
  } else {
    // git is installed, execute "lefthook install"
    exec('npx lefthook install', (err, stdout, stderr) => {
      if (err) {
        // lefthook install failed
        console.log('lefthook install failed:', err);
      } else {
        // lefthook install succeeded
        console.log('lefthook install succeeded');
        exec('npx patch-package', (patchError) => {
          if (patchError) {
            console.log('patch failed:', patchError);
          } else {
            console.log('node modules patch successful');
          }
        });
      }
    });
  }
});
