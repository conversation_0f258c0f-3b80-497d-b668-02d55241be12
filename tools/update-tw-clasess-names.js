import { promises as fs } from 'fs';
import { glob } from 'glob';
import path from 'path';

// Collect files using glob patterns into a single array.
// Accepts an array of glob patterns.
async function getFilesUsingGlob(patterns = []) {
  let files = [];
  for (const pattern of patterns) {
    try {
      const matches = await glob(pattern, { nodir: true });
      files.push(...matches);
    } catch (err) {
      console.error(`Error with glob pattern ${pattern}: ${err}`);
    }
  }
  return files;
}

// Process files in the provided file list with the given mapping.
async function processFiles(mapping, files) {
  for (const file of files) {
    try {
      const content = await fs.readFile(file, 'utf-8');
      let updatedContent = content;
      let fileChanged = false;

      // Iterate over every mapping object. Each object contains one key-value pair.
      for (const obj of mapping) {
        const keys = Object.keys(obj);
        if (keys.length !== 1) continue;
        const oldString = keys[0];
        const newString = obj[oldString];

        // Escape special regex characters in oldString.
        const escapedOldString = oldString.replace(
          /[.*+?^${}()|[\]\\]/g,
          '\\$&',
        );
        const regex = new RegExp(escapedOldString, 'g');

        if (regex.test(updatedContent)) {
          updatedContent = updatedContent.replace(regex, newString);
          fileChanged = true;
        }
      }

      if (fileChanged) {
        console.log(`Updating file: ${file}`);
        await fs.writeFile(file, updatedContent, 'utf-8');
      }
    } catch (err) {
      console.error(`Error processing file ${file}: ${err}`);
    }
  }
}

async function main() {
  try {
    // Adjust the path to file3.json if necessary.
    const jsonPath = path.join(process.cwd(), './tools/file3.json');
    const jsonData = await fs.readFile(jsonPath, 'utf-8');
    const mapping = JSON.parse(jsonData);

    // Define glob patterns for directories to search.
    const patterns = [
      path.join(process.cwd(), 'apps/fincloud-ui/src/app/**/*.*'),
      path.join(process.cwd(), 'libs/**/*.*'),
    ];

    const files = await getFilesUsingGlob(patterns);
    await processFiles(mapping, files);

    console.log('Update complete.');
  } catch (err) {
    console.error(`Error in main: ${err}`);
  }
}

main();
