import { glob } from 'glob';
import { Project } from 'ts-morph';

// Component descriptions
const componentDescriptions = {
  'badge-icon':
    'A badge component that displays an icon to represent a status or notification.',
  'badge-indicator':
    'A small indicator badge used to highlight counts or alerts on an item.',
  'badge-invitation':
    'A badge indicating invitations or pending actions requiring user attention.',
  'badge-status':
    'A badge that represents the status of an item, such as success, warning, or error.',
  'area-chart':
    'A chart that represents quantitative data with filled areas under a line graph.',
  'bar-chart':
    'A chart that displays data with rectangular bars proportional to the values they represent.',
  'doughnut-chart':
    'A circular chart similar to a pie chart but with a hollow center, used to show proportional data.',
};

// Initialize ts-morph project
const project = new Project();

// Base path where your component files are located
const basePath = './libs/ui'; // Adjust this path as needed

// Iterate over each component
for (const [componentName, description] of Object.entries(
  componentDescriptions,
)) {
  // Create the link
  const link = `https://lib-ui.neoshare.dev/?path=/docs/components-${componentName}--docs`;

  // Find all matching files for the component
  const pattern = `${basePath}/**/${componentName}.component.ts`;
  const filePaths = glob.sync(pattern);

  filePaths.forEach((filePath) => {
    // Add source file to the project
    const sourceFile = project.addSourceFileAtPath(filePath);

    // Find the class declaration
    const classDeclaration = sourceFile.getClasses()[0];

    if (classDeclaration) {
      // Create the JSDoc comment using proper @link syntax without 'See'
      const jsDocComment = `
${description}

{@link ${link} Storybook Reference}
`;

      // Add JSDoc to the class declaration
      classDeclaration.addJsDoc(jsDocComment.trim());

      // Save the updated file
      sourceFile.saveSync();
      console.log(`Updated ${filePath}`);
    } else {
      console.warn(`No class declaration found in ${filePath}`);
    }
  });
}
