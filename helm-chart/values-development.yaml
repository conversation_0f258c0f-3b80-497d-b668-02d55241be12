ingress:
  enabled: false
  className: kong-private
  annotations:
    cert-manager.io/cluster-issuer: default-letsencrypt-prod-dns
  hosts:
    - host: ui.dev.libs.neoshare.dev
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls:
    - secretName: lib-ui-development-cert
      hosts:
        - ui.dev.libs.neoshare.dev

gateway:
  enabled: true
  parentRefs:
    name: "internal"
    namespace: "istio-system"
    # sectionName: "https-lib-neoshare"
  hostnames:
    - lib-ui-dev.neoshare.dev
