ingress:
  enabled: true
  className: nginx
  annotations:
    cert-manager.io/cluster-issuer: default-letsencrypt-prod-dns
  hosts:
    - host: lib-ui.neoshare.dev
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls:
    - secretName: lib-ui-master-cert
      hosts:
        - lib-ui.neoshare.dev

gateway:
  enabled: false
  parentRefs:
    name: "internal"
    namespace: "istio-system"
    # sectionName: "https-lib-neoshare"
  hostnames:
    - lib-ui.neoshare.dev
