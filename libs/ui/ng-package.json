{"$schema": "../../node_modules/ng-packagr/ng-package.schema.json", "dest": "../../dist/libs/ui", "lib": {"entryFile": "src/index.ts"}, "assets": [{"input": "./styles/src/lib", "glob": "**/*.scss", "output": "styles"}, {"input": "./snippets/src/lib", "glob": "**/*", "output": "snippets"}, {"input": "./", "glob": "postinstall.js", "output": "."}, {"input": "./assets", "glob": "**/*.*", "ignore": ["storybook"], "output": "assets"}], "keepLifecycleScripts": true, "allowedNonPeerDependencies": ["@fincloud/utils", "@fincloud/lib-ui-tokens"], "deleteDestPath": false}