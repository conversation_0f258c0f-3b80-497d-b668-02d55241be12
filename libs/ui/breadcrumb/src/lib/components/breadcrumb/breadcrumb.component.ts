import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  NgZone,
  OnChanges,
  OnDestroy,
  Output,
  QueryList,
  Renderer2,
  ViewChild,
  ViewChildren,
} from '@angular/core';
import { MatMenuModule } from '@angular/material/menu';
import { RouterModule } from '@angular/router';
import { FinActionsMenuModule } from '@fincloud/ui/actions-menu';
import { FinButtonAppearance, FinButtonModule } from '@fincloud/ui/button';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinMenuItemModule } from '@fincloud/ui/menu-item';
import { FinTooltipDirective, FinTooltipModule } from '@fincloud/ui/tooltip';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinSize } from '@fincloud/ui/types';
import { pxToRem } from '@fincloud/utils/functions';
import { FinBreadcrumbItem } from '../../models/fin-breadcrumb-item';

@Component({
  selector: 'fin-breadcrumb',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatMenuModule,
    FinIconModule,
    FinActionsMenuModule,
    FinMenuItemModule,
    FinButtonModule,
    FinTooltipModule,
    FinTruncateTextModule,
  ],
  templateUrl: './breadcrumb.component.html',
  styleUrl: './breadcrumb.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinBreadcrumbComponent
  implements AfterViewInit, OnChanges, OnDestroy
{
  /** An array of breadcrumb items. */
  @Input({ required: true }) items: FinBreadcrumbItem[] = [];
  /** Event emitted when the back button is clicked */
  @Output() backButtonClicked = new EventEmitter<void>();
  /** Event emitted when the breadcrumb item is clicked */
  @Output() breadcrumbClicked = new EventEmitter<FinBreadcrumbItem>();

  @ViewChild('container', { static: true })
  private containerRef!: ElementRef<HTMLElement>;

  @ViewChildren('separator')
  private separatorRefs!: QueryList<ElementRef<HTMLElement>>;

  @ViewChildren('breadcrumb') private breadcrumbRefs!: QueryList<
    ElementRef<HTMLElement>
  >;

  @ViewChildren(FinTooltipDirective)
  private breadcrumbTooltips!: QueryList<FinTooltipDirective>;

  private resizeObs?: ResizeObserver;
  private minPercentWidth = 0.4;
  protected finSize = FinSize;
  protected appearance = FinButtonAppearance;

  constructor(
    private ngZone: NgZone,
    private elRef: ElementRef<HTMLElement>,
    private renderer: Renderer2,
  ) {}

  ngAfterViewInit() {
    this.ngZone.runOutsideAngular(() => {
      this.resizeObs = new ResizeObserver(() => this.applyWidths());
      this.resizeObs.observe(this.containerRef.nativeElement);
      queueMicrotask(() => this.applyWidths());
    });
  }
  ngOnChanges() {
    queueMicrotask(() => this.applyWidths());
  }

  private applyWidths() {
    this.resetWidths();
    const host = this.elRef.nativeElement.parentElement;
    if (!host) return;

    const breadcrumbItems = this.breadcrumbRefs
      .toArray()
      .map((breadcrumbItem) => breadcrumbItem.nativeElement);

    if (!breadcrumbItems.length) return;

    const parentContainerWidth = host.clientWidth;
    if (!parentContainerWidth) return;

    if (!this.startTruncation(host, parentContainerWidth)) {
      return;
    }
    const lastBreadcrumbItem = breadcrumbItems.slice(-1)[0]; // last breadcrumb

    const firstBreadcrumbItems = breadcrumbItems.slice(0, -1); // exclude last breadcrumb
    const totalFirstBreadcrumbTruncation =
      this.minPercentWidth * parentContainerWidth;

    const minWidth =
      totalFirstBreadcrumbTruncation / firstBreadcrumbItems.length;

    const containersDiff = this.totalWidth(host) - parentContainerWidth;

    const breadcrumbWidth =
      (this.totalFirstItemsWidth(firstBreadcrumbItems) - containersDiff) /
      firstBreadcrumbItems.length;

    firstBreadcrumbItems.forEach((el, index) => {
      if (
        breadcrumbWidth > 0 &&
        el.scrollWidth > breadcrumbWidth &&
        el.scrollWidth >= minWidth
      ) {
        el.style.width = pxToRem(breadcrumbWidth);
        el.style.minWidth = pxToRem(minWidth);
      }

      if (breadcrumbWidth <= 0 && el.clientWidth >= minWidth) {
        el.style.width = pxToRem(minWidth);
      }

      const containersDiff =
        this.totalWidth(host, 'clientWidth') - parentContainerWidth;
      const siblingBreadcrumb =
        firstBreadcrumbItems[index - 1] || firstBreadcrumbItems[index + 1];

      if (siblingBreadcrumb?.clientWidth <= minWidth && containersDiff < 0) {
        el.style.width = pxToRem(el.clientWidth + containersDiff * -1);
      }

      if (el.style.width && el.style.width !== 'unset') {
        el.classList.add('breadcrumb-ellipsis');
      }
      this.toggleBreadcrumbTooltip(index, el);
    });

    const lastBreadcrumbItemIndex = breadcrumbItems.length - 1;
    const breadcrumbTooltip = this.breadcrumbTooltips.get(
      lastBreadcrumbItemIndex,
    );

    this.updateLastBreadcrumb(
      host,
      parentContainerWidth,
      breadcrumbTooltip,
      lastBreadcrumbItem,
    );
  }

  private updateLastBreadcrumb(
    host: HTMLElement,
    parentContainerWidth: number,
    breadcrumbTooltip: FinTooltipDirective | undefined,
    lastBreadcrumbItem: HTMLElement,
  ) {
    if (this.totalWidth(host) - parentContainerWidth > 0) {
      if (breadcrumbTooltip && breadcrumbTooltip.disableTooltip !== false) {
        breadcrumbTooltip.updateState({ disableTooltip: false });
      }
      lastBreadcrumbItem.classList.add('breadcrumb-ellipsis');
      lastBreadcrumbItem.style.width = pxToRem(
        lastBreadcrumbItem.scrollWidth -
          (this.totalWidth(host, 'clientWidth') - parentContainerWidth),
      );
    } else {
      if (breadcrumbTooltip && breadcrumbTooltip.disableTooltip === true) {
        breadcrumbTooltip.updateState({ disableTooltip: true });
      }
      if (lastBreadcrumbItem.classList.contains('breadcrumb-ellipsis')) {
        lastBreadcrumbItem.classList.remove('breadcrumb-ellipsis');
      }
    }
  }

  private toggleBreadcrumbTooltip(index: number, el: HTMLElement) {
    const breadcrumbTooltip = this.breadcrumbTooltips.get(index);
    if (
      (!el.style.width || el.style.width === 'unset') &&
      breadcrumbTooltip &&
      breadcrumbTooltip.disableTooltip === true
    ) {
      breadcrumbTooltip.updateState({ disableTooltip: true });
    } else if (breadcrumbTooltip) {
      breadcrumbTooltip.updateState({ disableTooltip: false });
    }
  }

  private totalFirstItemsWidth(
    firstBreadcrumbItems: HTMLElement[],
    widthType: 'scrollWidth' | 'clientWidth' = 'scrollWidth',
  ) {
    return firstBreadcrumbItems.reduce(
      (prevWidth, element) => prevWidth + element[widthType],
      0,
    );
  }

  private totalWidth(
    host: HTMLElement,
    widthType: 'scrollWidth' | 'clientWidth' = 'scrollWidth',
  ) {
    return Array.from(this.containerRef.nativeElement.children).reduce(
      (prevWidth, element) => prevWidth + element[widthType],
      0,
    );
  }

  private startTruncation(host: HTMLElement, parentContainerWidth: number) {
    return Math.round(this.totalWidth(host)) > parentContainerWidth + 1;
  }

  protected onBreadcrumbClick(menuItem: FinBreadcrumbItem) {
    this.breadcrumbClicked.emit(menuItem);
  }

  protected onBackButtonClick(): void {
    this.backButtonClicked.emit();
  }

  private resetWidths() {
    (
      Array.from(this.containerRef.nativeElement.children) as HTMLElement[]
    ).forEach((element: HTMLElement) => {
      this.renderer.setStyle(element, 'width', 'unset');
      this.renderer.setStyle(element, 'minWidth', 'unset');
      this.renderer.removeClass(element, 'breadcrumb-ellipsis');
    });

    const breadcrumbItems = this.breadcrumbRefs
      ?.toArray()
      .map((breadcrumbItem) => breadcrumbItem.nativeElement);
    const lastBreadcrumbItem = breadcrumbItems.slice(-1)[0]; // last breadcrumb
    this.renderer.setStyle(lastBreadcrumbItem, 'width', 'unset');
    this.renderer.setStyle(lastBreadcrumbItem, 'minWidth', 'unset');
    this.renderer.removeClass(lastBreadcrumbItem, 'breadcrumb-ellipsis');
  }

  ngOnDestroy() {
    this.resizeObs?.disconnect();
  }
}
