:host {
  display: flex;
  align-items: center;
  min-height: 2.4rem;
}
%breadcrumb-ellipsis {
  flex: 0 1 auto; // allow shrinking
  min-width: 0; // allow ellipsis to work when flex‑shrink happens
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.breadcrumb-container {
  display: flex;
  align-items: center;
  flex-wrap: nowrap; // keep everything on one line
  width: 100%;
  overflow: hidden; // prevent accidental wrapping
  white-space: nowrap;

  .breadcrumb-ellipsis {
    @extend %breadcrumb-ellipsis;
    > a {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
