<nav class="fin-w-full">
  <ol class="breadcrumb-container" #container>
    @if (items.length > 1 && items.length <= 4) {
      @for (item of items; track item.label; let last = $last) {
        <li
          #breadcrumb
          [ngClass]="
            last
              ? 'fin-text-body-2-strong fin-text-color-text-primary'
              : 'fin-text-body-2-moderate fin-text-color-text-interactive'
          "
        >
          @if (!last) {
            <a
              (click)="onBreadcrumbClick(item)"
              [routerLink]="item.routerPath"
              finTooltip
              [disableTooltip]="true"
              [content]="item.label"
              class="fin-text-body-2-moderate fin-text-color-text-interactive hover:fin-underline fin-cursor-pointer"
            >
              {{ item.label }}
            </a>
          } @else {
            <div
              finTooltip
              [disableTooltip]="true"
              [content]="item.label"
              class="breadcrumb-ellipsis"
            >
              {{ item.label }}
            </div>
          }
        </li>
        @if (!last) {
          <li #separator class="fin-px-2">
            <ng-container [ngTemplateOutlet]="separatorTemplate"></ng-container>
          </li>
        }
      }
    } @else if (items.length === 1) {
      <!-- show back button -->
      <li>
        <button
          fin-button
          [size]="finSize.S"
          [appearance]="appearance.SECONDARY"
          class="fin-me-3"
          (click)="onBackButtonClick()"
        >
          <fin-icon name="keyboard_arrow_left"></fin-icon>
          Back
        </button>
      </li>
      <li
        #breadcrumb
        class="fin-text-body-2-strong fin-text-color-text-primary fin-self-baseline"
      >
        {{ items[0].label }}
      </li>
    } @else {
      <!-- first item -->
      <li #breadcrumb>
        <a
          finTooltip
          [disableTooltip]="true"
          [content]="items[0].label"
          (click)="onBreadcrumbClick(items[0])"
          [routerLink]="items[0].routerPath"
          class="fin-text-body-2-moderate fin-text-color-text-interactive hover:fin-underline fin-cursor-pointer"
        >
          {{ items[0].label }}
        </a>
      </li>
      <li #separator class="fin-px-2">
        <ng-container [ngTemplateOutlet]="separatorTemplate"></ng-container>
      </li>

      <!-- collapsed middle section -->
      <li class="-fin-mx-2">
        <button
          type="button"
          class="breadcrumb-ellipsis"
          fin-button-icon
          [size]="finSize.S"
          [appearance]="appearance.STEALTH"
          [finActionMenuTrigger]="finMenu.panel"
        >
          <fin-icon name="more_horiz"></fin-icon>
        </button>
      </li>

      <li #separator class="fin-px-2">
        <ng-container [ngTemplateOutlet]="separatorTemplate"></ng-container>
      </li>

      <!-- almost last item -->
      <li #breadcrumb>
        <a
          finTooltip
          [disableTooltip]="true"
          [content]="items[items.length - 2].label"
          (click)="onBreadcrumbClick(items[items.length - 2])"
          [routerLink]="items[items.length - 2].routerPath"
          class="fin-text-body-2-moderate fin-text-color-text-interactive hover:fin-underline fin-cursor-pointer"
        >
          {{ items[items.length - 2].label }}
        </a>
      </li>
      <li #separator class="fin-px-2">
        <ng-container [ngTemplateOutlet]="separatorTemplate"></ng-container>
      </li>
      <!-- last item -->
      <li
        #breadcrumb
        finTooltip
        [disableTooltip]="true"
        [content]="items[items.length - 1].label"
        class="fin-text-body-2-strong fin-text-color-text-primary"
      >
        {{ items[items.length - 1].label }}
      </li>
    }
  </ol>
</nav>

<!-- Not visible breadcrumb items menu -->
<fin-actions-menu #finMenu="finActionMenu" [xPosition]="'after'">
  @for (item of items.slice(1, -2); track item.label) {
    <button fin-menu-item [size]="finSize.M" (click)="onBreadcrumbClick(item)">
      <ng-container finMenuItemTitle>
        <div finTruncateText class="fin-max-w-[24rem]">
          {{ item.label }}
        </div>
      </ng-container>
    </button>
  }
</fin-actions-menu>

<!-- Re‑usable breadcrumb separator -->
<ng-template #separatorTemplate>
  <!-- <li class="fin-px-2"> -->
  <fin-icon
    [size]="finSize.S"
    name="keyboard_arrow_right"
    class="fin-text-color-icons-tertiary fin-w-[1.6rem]"
  ></fin-icon>
  <!-- </li> -->
</ng-template>
