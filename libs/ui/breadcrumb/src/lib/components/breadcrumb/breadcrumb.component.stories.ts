import { RouterModule, provideRouter } from '@angular/router';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import {
  applicationConfig,
  moduleMetadata,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { FinBreadcrumbModule } from '../../breadcrumb.module';
import { FinBreadcrumbComponent } from './breadcrumb.component';

const meta: Meta<FinBreadcrumbComponent> = {
  component: FinBreadcrumbComponent,
  title: 'Components/Breadcrumb',
  decorators: [
    moduleMetadata({
      imports: [
        RouterModule,
        FinBreadcrumbModule,
        FinBreadcrumbComponent,
        FinTruncateTextModule,
      ],
    }),
    applicationConfig({
      providers: [
        provideRouter([{ path: '**', redirectTo: '', pathMatch: 'full' }]),
      ],
    }),
  ],
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=5255-5816&m=dev',
    },
    docs: {
      description: {
        component:
          '`import { FinBreadcrumbModule } from "@fincloud/ui/breadcrumb"`',
      },
    },
  },
};

export default meta;
type Story = StoryObj<FinBreadcrumbComponent>;

export const Primary: Story = {
  argTypes: {
    backButtonClicked: {
      control: false,
    },
  },
  args: {
    items: [
      { label: 'first', routerPath: '/first' },
      { label: 'second', routerPath: '/first/second' },
      { label: 'third', routerPath: '/first/second/third' },
    ],
  },
};

export const Multiple: Story = {
  argTypes: {
    backButtonClicked: {
      control: false,
    },
  },
  args: {
    items: [
      { label: 'first', routerPath: '/first' },
      { label: 'second', routerPath: '/first/second' },
      { label: 'third', routerPath: '/first/second/third' },
      { label: 'fourth', routerPath: '/first/second/third/fourth' },
      { label: 'fifth', routerPath: '/first/second/third/fourth/fifth' },
    ],
  },
};

export const MultipleWithLongerNames: Story = {
  argTypes: {
    backButtonClicked: {
      control: false,
    },
  },
  args: {
    items: [
      { label: 'First page with a longer name', routerPath: '/first' },
      { label: 'Second', routerPath: '/first/second' },
      {
        label: 'Third page with a longer name',
        routerPath: '/first/second/third',
      },
      { label: 'Fourth', routerPath: '/first/second/third/fourth' },
      {
        label: 'Fifth page with a longer name',
        routerPath: '/first/second/third/fourth/fifth',
      },
    ],
  },
};

export const Simple: Story = {
  args: {
    items: [{ label: 'single', routerPath: '' }],
  },
};
