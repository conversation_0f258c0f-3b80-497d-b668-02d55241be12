import { FormControl } from '@angular/forms';
import type { Meta, StoryObj } from '@storybook/angular';
import { FinSwitchToggleComponent } from './switch-toggle.component';

const meta: Meta<FinSwitchToggleComponent> = {
  component: FinSwitchToggleComponent,
  title: 'Fields/Switch Toggle',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinSwitchToggleModule } from "@fincloud/ui/switch-toggle"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=19920-3360&m=dev',
    },
  },
};
export default meta;
type Story = StoryObj<FinSwitchToggleComponent>;

export const Default: Story = {
  args: {
    options: [
      { label: 'Label 1', value: 'Label 1' },
      { label: 'Label 2', value: 'Label 2' },
    ],
    stretched: false,
  },
  argTypes: {
    switchChange: {
      control: false,
    },
  },
  render: (args) => ({
    props: { ...args, formControl: new FormControl('Label 1') },
  }),
};

export const Disabled: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: {
      ...args,
      formControl: new FormControl({ value: 'Label 1', disabled: true }),
    },
  }),
};

export const Stretched: Story = {
  args: {
    ...Default.args,
    stretched: true,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: { ...args, formControl: new FormControl('Label 1') },
    template: `
      <div class="fin-w-[40rem]">
        <fin-switch-toggle
          [formControl]="formControl"
          [options]="options"
          [stretched]="true"
        ></fin-switch-toggle>
      </div>
    `,
  }),
};

export const WithMoreOptions: Story = {
  args: {
    ...Default.args,
    options: [
      { label: 'Label 1', value: 'Label 1' },
      { label: 'Label 2', value: 'Label 2' },
      { label: 'Label 3', value: 'Label 3' },
    ],
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: { ...args, formControl: new FormControl('Label 1') },
  }),
};

export const WithIcons: Story = {
  args: {
    ...Default.args,
    options: [
      { value: 'value1', iconName: 'grid_view', tooltip: 'Grid view' },
      { value: 'value2', iconName: 'list_alt', tooltip: 'List view' },
    ],
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: {
      ...args,
      formControl: new FormControl('value1'),
    },
  }),
};

export const OnDarkBackground: Story = {
  parameters: {
    backgrounds: {
      default: 'dark',
      values: [{ name: 'dark', value: '#26283E' }],
    },
  },
  args: {
    options: [
      { label: 'Label 1', value: 'Label 1' },
      { label: 'Label 2', value: 'Label 2' },
    ],
    stretched: false,
  },
  argTypes: {
    switchChange: {
      control: false,
    },
  },
  render: (args) => ({
    props: { ...args, formControl: new FormControl('Label 1') },
  }),
};
