<mat-button-toggle-group
  [ngClass]="{
    'fin-w-full fin-flex fin-justify-between': stretched,
  }"
  hideSingleSelectionIndicator
  [formControl]="control"
  (change)="onSwitchChange($event)"
>
  @for (option of options; track $index) {
    <mat-button-toggle
      class="fin-flex fin-items-center"
      [value]="option.value"
      [class.fin-flex-1]="stretched"
      finTooltip
      [openDelay]="0"
      [disableTooltip]="!option.tooltip"
      [content]="option.tooltip"
    >
      @if (option.iconName || option.iconSrc) {
        <fin-icon
          [size]="sizes.M"
          [name]="option.iconName ?? ''"
          [src]="option.iconSrc ?? ''"
        ></fin-icon>
      } @else {
        {{ option.label }}
      }
    </mat-button-toggle>
  }
</mat-button-toggle-group>
