::ng-deep .fin-switch-toggle .mat-button-toggle-group {
  --mat-standard-button-toggle-divider-color: transparent;
  --mat-standard-button-toggle-background-color: theme('colors.white');
  --mat-standard-button-toggle-hover-state-layer-opacity: 0;
  --mat-standard-button-toggle-focus-state-layer-opacity: 0;
  --mat-standard-button-toggle-height: 3.2rem;
  --mat-standard-button-toggle-selected-state-background-color: theme(
    'colors.color-background-tertiary-minimal'
  );
  --mat-standard-button-toggle-selected-state-text-color: theme(
    'colors.color-text-primary'
  );
  --mat-standard-button-toggle-text-color: theme('colors.color-text-primary');

  border-radius: 9999px;
  height: 3.2rem;
  border: 0.1rem solid theme('colors.color-background-tertiary-minimal');
  background-color: theme('colors.white');

  &:hover:not(:has(.mat-button-toggle-disabled)) {
    background-color: theme('colors.color-hover-tertiary');

    .mat-button-toggle:not(.mat-button-toggle-checked):not(
        .mat-button-toggle-disabled
      ) {
      background-color: theme('colors.color-hover-tertiary');
    }
  }

  .mat-button-toggle-checked.mat-button-toggle-disabled {
    @apply fin-bg-color-background-tertiary-minimal;
  }

  .mat-button-toggle {
    border-radius: 9999px;

    &-label-content {
      padding: 0;
      @apply fin-text-body-3-moderate;
    }

    &-button {
      @apply fin-flex;
      @apply fin-justify-center;
      padding-left: 2.4rem;
      padding-right: 2.4rem;
    }

    &-checked {
      .mat-button-toggle-label-content {
        @apply fin-text-body-3-strong;
      }
    }
  }
}
