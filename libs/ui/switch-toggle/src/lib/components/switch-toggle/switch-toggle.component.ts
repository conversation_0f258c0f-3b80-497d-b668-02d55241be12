import { CommonModule } from '@angular/common';
import {
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  forwardRef,
  Input,
  Output,
} from '@angular/core';
import { NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import {
  MatButtonToggleChange,
  MatButtonToggleModule,
} from '@angular/material/button-toggle';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';
import { FinAngularMaterialModule } from '@fincloud/utils/angular-material';
import { FinControlValueAccessor } from '@fincloud/utils/control-value-accessor';
import { FinSwitchToggleOption } from '../../models/fin-switch-toggle-option';
import { FinTooltipModule } from "@fincloud/ui/tooltip";

/**
 * A versatile switch toggle component for creating interactive toggle groups with customizable options.
 *
 * For detailed documentation visit
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-switch-toggle--docs https://lib-ui.neoshare.dev/?path=/docs/components-switch-toggle--docs}.
 */

/**
 * A toggle switch used to turn options on or off.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-switch-toggle--docs Storybook Reference}
 */
@Component({
  selector: 'fin-switch-toggle',
  standalone: true,
  imports: [
    CommonModule,
    FinAngularMaterialModule,
    MatButtonToggleModule,
    ReactiveFormsModule,
    FinIconModule,
    FinTooltipModule,
  ],
  templateUrl: './switch-toggle.component.html',
  styleUrl: './switch-toggle.component.scss',
  host: {
    class: 'fin-switch-toggle',
  },
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FinSwitchToggleComponent),
      multi: true,
    },
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinSwitchToggleComponent extends FinControlValueAccessor {
  /** All of the defined tabs options. **Required**. */
  @Input({ required: true }) options: FinSwitchToggleOption[] = [];

  /** Whether the toggle should be stretched to fill the container. */
  @Input({ transform: booleanAttribute }) stretched = false;

  /** Event emitted when the group's value changes. */
  @Output() switchChange = new EventEmitter<string>();

  protected onSwitchChange(event: MatButtonToggleChange) {
    this.switchChange.emit(event.value);
  }

  protected sizes = FinSize;
}
