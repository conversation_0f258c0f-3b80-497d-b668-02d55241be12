# Component Name

Brief description of the component's purpose and primary use cases.

## Installation

```bash
npm install @fincloud/ui
```

```typescript
import { ComponentModule } from '@fincloud/ui/component-name';

@Component({
  imports: [ComponentModule],
  // ...
})
export class YourComponent {}
```

## Basic Usage

### Simple Example

```html
<component-selector>
  Basic content
</component-selector>
```

```typescript
import { Component } from '@angular/core';

@Component({
  selector: 'app-example',
  template: `
    <component-selector>
      Basic content
    </component-selector>
  `
})
export class ExampleComponent {}
```

## API Reference

### Component: ComponentName

#### Selector
- `component-selector`

#### Inputs

| Name | Type | Default | Description |
|------|------|---------|-------------|
| `inputName` | `string` | `''` | Description of the input property |
| `booleanInput` | `boolean` | `false` | Description of boolean input |
| `enumInput` | `EnumType` | `EnumType.DEFAULT` | Description of enum input |

#### Outputs

| Name | Type | Description |
|------|------|-------------|
| `eventName` | `EventEmitter<Type>` | Description of the output event |

#### Methods

| Name | Parameters | Return Type | Description |
|------|------------|-------------|-------------|
| `methodName()` | `param: Type` | `void` | Description of the public method |

#### Content Projection

| Slot | Description |
|------|-------------|
| Default | Main content area |
| `ng-template #slotName` | Named template slot |

## Configuration Options

### Enums

#### EnumName

| Value | Description |
|-------|-------------|
| `VALUE_ONE` | Description of first value |
| `VALUE_TWO` | Description of second value |

### Interfaces

#### InterfaceName

```typescript
interface InterfaceName {
  property: string;
  optionalProperty?: number;
}
```

## Advanced Examples

### Example with All Options

```html
<component-selector
  [inputName]="value"
  [booleanInput]="true"
  [enumInput]="enumType.VALUE_ONE"
  (eventName)="handleEvent($event)">
  
  <ng-template #slotName>
    Custom template content
  </ng-template>
  
  Main content
</component-selector>
```

```typescript
import { Component } from '@angular/core';
import { EnumType } from '@fincloud/ui/component-name';

@Component({
  selector: 'app-advanced-example',
  template: `
    <component-selector
      [inputName]="inputValue"
      [booleanInput]="isEnabled"
      [enumInput]="selectedType"
      (eventName)="onEvent($event)">
      
      <ng-template #slotName>
        <p>Custom template content</p>
      </ng-template>
      
      <p>Main content goes here</p>
    </component-selector>
  `
})
export class AdvancedExampleComponent {
  inputValue = 'example value';
  isEnabled = true;
  selectedType = EnumType.VALUE_ONE;

  onEvent(event: EventType): void {
    console.log('Event received:', event);
  }
}
```

### Reactive Forms Integration

```typescript
import { Component } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-form-example',
  imports: [ReactiveFormsModule, ComponentModule],
  template: `
    <form [formGroup]="form">
      <component-selector formControlName="fieldName">
        Form content
      </component-selector>
    </form>
  `
})
export class FormExampleComponent {
  form = new FormGroup({
    fieldName: new FormControl('')
  });
}
```

## Best Practices

### Do's
- ✅ Use semantic HTML elements when possible
- ✅ Provide meaningful ARIA labels for accessibility
- ✅ Follow Angular OnPush change detection strategy
- ✅ Use reactive forms for form controls
- ✅ Handle loading and error states appropriately

### Don'ts
- ❌ Don't manipulate DOM directly
- ❌ Don't forget to unsubscribe from observables
- ❌ Don't use deprecated Angular features
- ❌ Don't ignore accessibility requirements

### Performance Tips
- Use OnPush change detection strategy
- Implement trackBy functions for *ngFor loops
- Lazy load components when appropriate
- Use async pipe for observables

### Accessibility Guidelines
- Ensure proper ARIA attributes are set
- Support keyboard navigation
- Provide sufficient color contrast
- Include screen reader friendly descriptions

## Troubleshooting

### Common Issues

#### Issue: Component not displaying correctly
**Solution:** Ensure the component module is imported in your module or component.

#### Issue: Styles not applying
**Solution:** Check that the component's CSS is properly imported and not overridden.

#### Issue: Events not firing
**Solution:** Verify event binding syntax and ensure the component is not disabled.

## Related Components

- [Related Component 1](../related-component-1/README.md)
- [Related Component 2](../related-component-2/README.md)

## Changelog

See [CHANGELOG.md](../../CHANGELOG.md) for version history and breaking changes.

## Contributing

See [CONTRIBUTING.md](../../CONTRIBUTING.md) for guidelines on contributing to this component.

## License

This component is part of the @fincloud/ui library and is licensed under [MIT License](../../LICENSE).
