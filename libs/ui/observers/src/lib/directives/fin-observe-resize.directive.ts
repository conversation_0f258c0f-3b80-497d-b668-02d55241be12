import { NumberInput } from '@angular/cdk/coercion';
import { SharedResizeObserver } from '@angular/cdk/observers/private';
import {
  AfterContentInit,
  booleanAttribute,
  Directive,
  ElementRef,
  EventEmitter,
  Input,
  NgZone,
  numberAttribute,
  OnDestroy,
  Output,
} from '@angular/core';
import { debounceTime, map, Subscription } from 'rxjs';

@Directive({
  selector: '[finObserveResize]',
  standalone: true,
})
export class FinObserveResizeDirective implements AfterContentInit, OnDestroy {
  /**
   * Whether observing content is disabled. This option can be used
   * to disconnect the underlying ResizeObserver until it is needed.
   */
  @Input({ transform: booleanAttribute })
  get finObserveResizeDisabled(): boolean {
    return this._disabled;
  }
  set finObserveResizeDisabled(value: boolean) {
    this._disabled = value;
    this._disabled ? this.unsubscribe() : this.subscribe();
  }
  private _disabled = false;

  /** Debounce interval for emitting the changes. */
  @Input()
  get finObserveResizeDebounce(): number {
    return this._debounce;
  }
  set finObserveResizeDebounce(value: NumberInput) {
    this._debounce = numberAttribute(value);
    this.subscribe();
  }
  private _debounce!: number;

  /** Event emitted for each change in the element's size. */
  @Output() finObserveResize = new EventEmitter<ResizeObserverEntry>();

  private sharedResizeSubscription: Subscription | null = null;

  constructor(
    private elementRef: ElementRef,
    private sharedResizeObserver: SharedResizeObserver,
    private ngZone: NgZone,
  ) {}

  ngAfterContentInit(): void {
    if (!this.sharedResizeSubscription && !this.finObserveResizeDisabled) {
      this.subscribe();
    }
  }

  private subscribe() {
    this.unsubscribe();
    const stream$ = this.sharedResizeObserver.observe(
      this.elementRef.nativeElement,
    );

    this.ngZone.runOutsideAngular(() => {
      this.sharedResizeSubscription = stream$
        .pipe(
          debounceTime(this.finObserveResizeDebounce),
          map(
            (elements) =>
              elements.filter(
                (element) => element.target === this.elementRef.nativeElement,
              )[0],
          ),
        )
        .subscribe(this.finObserveResize);
    });
  }

  private unsubscribe() {
    this.sharedResizeSubscription?.unsubscribe();
  }

  ngOnDestroy(): void {
    this.unsubscribe();
  }
}
