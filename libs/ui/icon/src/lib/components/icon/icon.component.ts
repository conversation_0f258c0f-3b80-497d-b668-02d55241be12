import { CommonModule } from '@angular/common';
import {
  Attribute,
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  Input,
  OnInit,
} from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { FinSize } from '@fincloud/ui/types';
import { FinAngularMaterialModule } from '@fincloud/utils/angular-material';
import { AngularSvgIconModule } from 'angular-svg-icon';
/**
 * A graphical representation of an object or action.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-icon--docs Storybook Reference}
 */
@Component({
  selector: 'fin-icon',
  standalone: true,
  imports: [
    CommonModule,
    FinAngularMaterialModule,
    MatIconModule,
    AngularSvgIconModule,
  ],

  templateUrl: './icon.component.html',
  styleUrl: './icon.component.scss',
  host: {
    '[class]': "'fin-size-'+size",
    class: 'fin-icon',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinIconComponent implements OnInit {
  /** Name of an icon within a [Material Icons](https://fonts.google.com/icons) font set. */
  @Input() name = '';

  /** URL pointing to the icon source. */
  @Input() src = '';

  /** Size of the icon. */
  @Input()
  size:
    | FinSize.XXL
    | FinSize.XL
    | FinSize.L
    | FinSize.M
    | FinSize.S
    | FinSize.XS = FinSize.M;

  /** Whether the Material icon is outlined. */
  @Input({ transform: booleanAttribute }) matIconOutlined = true;

  protected cssClass = '';

  constructor(@Attribute('class') private attrClass: string) {}

  ngOnInit(): void {
    this.cssClass = this.attrClass || '';
  }
}
