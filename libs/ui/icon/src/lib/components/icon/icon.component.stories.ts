import { moduleMetadata, type Meta, type StoryObj } from '@storybook/angular';
import { FinIconComponent } from './icon.component';

import { FinSize } from '@fincloud/ui/types';
import { FinIconModule } from '../../icon.module';

const meta: Meta<FinIconComponent> = {
  component: FinIconComponent,
  title: 'Components/Icon',
  parameters: {
    docs: {
      description: {
        component: '`import { FinIconModule } from "@fincloud/ui/icon"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=18555-19463&m=dev',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [FinIconModule],
    }),
  ],
};
export default meta;
type Story = StoryObj<FinIconComponent>;

export const MaterialIcon: Story = {
  argTypes: {
    size: {
      options: Object.values(FinSize),
      control: { type: 'select' },
    },
  },
  args: {
    name: 'home',
    size: FinSize.XXL,
  },
};

export const SvgIcon: Story = {
  argTypes: {
    ...MaterialIcon.argTypes,
  },
  args: {
    src: 'assets/storybook/icons/svgAIDocument.svg',
    size: FinSize.XXL,
  },
};
