:host {
  @apply fin-flex;
  @apply fin-justify-start;
  @apply fin-items-center;

  svg-icon {
    display: inline-block;
    width: 1em;
    height: 1em;
    svg {
      display: block;

      height: 100%;
      width: 100%;
    }
  }

  &.fin-size {
    &-xs {
      font-size: theme('fontSize.text-body-3-size');
      font-weight: 700;
    }

    &-s {
      font-size: theme('fontSize.text-body-1-size');
      font-weight: theme('fontWeight.text-body-2-strong-weight');
    }

    &-m {
      font-size: theme('fontSize.text-heading-3-size');
      font-weight: theme('fontWeight.text-body-2-moderate-weight');
    }

    &-l {
      font-size: theme('fontSize.text-heading-2-size');
      font-weight: theme('fontWeight.text-body-2-moderate-weight');
    }

    &-xl {
      font-size: 3.2rem;
      font-weight: theme('fontWeight.text-body-2-moderate-weight');
    }

    &-xxl {
      font-size: theme('fontSize.text-display-1-size');
      font-weight: theme('fontWeight.text-body-2-moderate-weight');
    }
  }
}

.mat-icon {
  width: unset !important;
  height: unset !important;
  font-size: inherit;
  // inherit the context color
  --mat-icon-color: currentColor;
}
