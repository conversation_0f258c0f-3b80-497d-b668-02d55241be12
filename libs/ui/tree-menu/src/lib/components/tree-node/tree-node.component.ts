import { CommonModule } from '@angular/common';
import {
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  numberAttribute,
  OnChanges,
  Output,
  SimpleChanges,
} from '@angular/core';
import { MatTreeNodeToggle } from '@angular/material/tree';
import { FinDragDropModule } from '@fincloud/ui/drag-drop';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';
import { pxToRem } from '@fincloud/utils/functions';
import { FinTreeNodeAppearance } from '../../enums/tree-node-appearance';
import { FinTreeNode } from '../../models/fin-tree-node';
import { FinTreeNodeStructure } from '../../models/fin-tree-node-structure';
import { FinTreeNodeToggle } from '../../models/fin-tree-node-toggle';
@Component({
  selector: 'fin-tree-node',
  standalone: true,
  imports: [CommonModule, FinIconModule, MatTreeNodeToggle, FinDragDropModule],
  host: {
    '[class]': 'treeNodeCssClasses',
  },
  templateUrl: './tree-node.component.html',
  styleUrl: './tree-node.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinTreeNodeComponent<T> implements OnChanges {
  @Input({ required: true }) node!: T;
  @Input() expandable = false;
  @Input() expanded = false;
  @Input() active = false;
  @Input({ required: false }) appearance: FinTreeNodeAppearance =
    FinTreeNodeAppearance.PRIMARY;
  @Input({ required: false, transform: booleanAttribute }) dragHandle = false;
  @Output() private finTreeNodeToggle = new EventEmitter<
    FinTreeNodeToggle<T>
  >();
  @Input({ required: false, transform: booleanAttribute }) hoverHighlight =
    true;
  @Input({ required: false, transform: booleanAttribute }) nodeSeparator = true;
  @Input({ required: false, transform: numberAttribute }) baseOffset = 16;
  @Input({ required: false, transform: numberAttribute }) levelOffset = 4;
  @Input() nodeClick: ((event: MouseEvent, node: T) => void) | undefined;
  @Input() hideExpandButton = false;

  @Output() private finDraggableChange = new EventEmitter<{
    templateName: string;
    draggable: boolean;
  }>();

  protected treeNodeLeftOffset = '0';

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes['dragHandle'] &&
      changes['dragHandle'].currentValue !== changes['dragHandle'].previousValue
    ) {
      this.finDraggableChange.emit({
        templateName:
          (this.node as FinTreeNodeStructure<FinTreeNode<T>>).templateName ??
          '',
        draggable: changes['dragHandle'].currentValue,
      });
    }

    this.treeNodeLeftOffset = this.calculateLeftOffset();
  }

  protected finTreeNodeAppearance = FinTreeNodeAppearance;
  protected finSize = FinSize;

  protected click(event: any): void {
    if (!this.nodeClick) {
      return;
    }

    this.nodeClick(event, this.node);
  }

  protected stopPropagation(event: MouseEvent): void {
    event.stopPropagation();
  }

  protected getParentCount(): number {
    const currentNode = this.node as FinTreeNodeStructure<FinTreeNode<T>>;
    return currentNode._node?.level ?? 0;
  }

  protected calculateLeftOffset(): string {
    const offset =
      this.baseOffset + (this.getParentCount() - 1) * this.levelOffset; // Subtract 1 since we start from level 1

    return pxToRem(offset);
  }

  protected toggle(event: MouseEvent, node: T): void {
    event.stopPropagation();

    this.finTreeNodeToggle.emit({
      node,
      expand: !this.expanded,
    });
  }

  private get treeNodeCssClasses(): string {
    const baseClass = `fin-tree-node fin-tree-node-${this.appearance.toLowerCase()}`;

    if (this.expanded) {
      return `${baseClass} fin-tree-node-expanded`;
    }

    return `${baseClass} fin-tree-node-collapsed`;
  }
}
