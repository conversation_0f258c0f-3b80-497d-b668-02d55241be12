<div
  class="fin-tree-node-content"
  [class.fin-tree-node-hover-highlight]="hoverHighlight"
  [class.fin-tree-node-node-separator]="nodeSeparator"
  [class.fin-bg-color-background-tertiary-minimal]="active"
  [class.fin-cursor-pointer]="!!nodeClick"
  [style.padding-inline-start]="treeNodeLeftOffset"
  (click)="click($event)"
>
  <fin-icon
    [name]="expanded ? 'keyboard_arrow_up' : 'keyboard_arrow_down'"
    [size]="finSize.S"
    matTreeNodeToggle
    class="fin-cursor-pointer"
    [class.fin-invisible]="!expandable || hideExpandButton"
    (click)="toggle($event, node)"
  ></fin-icon>
  <ng-content></ng-content>
  @if (dragHandle) {
    <fin-icon
      (click)="stopPropagation($event)"
      finDragHandle
      name="drag_indicator"
      [size]="finSize.S"
      class="tw-text-color-text-tertiary fin-cursor-grabbing fin-tree-node-hover-block fin-hidden"
    />
  }
</div>
<div
  class="fin-h-16 fin-w-full fin-bg-color-transparency-secondary-minimal fin-rounded-[0.4rem] fin-border-color-border-default-interactive fin-border-solid fin-border-2"
  *finDragPlaceholder
></div>
