import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FinTreeNode } from '../../models/fin-tree-node';
import { FinTreeNodeStructure } from '../../models/fin-tree-node-structure';

@Component({
  selector: 'fin-tree-node-counter',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './tree-node-counter.component.html',
  styleUrl: './tree-node-counter.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinTreeNodeCounterComponent<T> {
  @Input({ required: true }) node!: T;

  protected getNodeNumber(): string {
    const currentNode = this.node as FinTreeNodeStructure<FinTreeNode<T>>;
    return currentNode._node?.levelLabel.join('.') ?? '';
  }
}
