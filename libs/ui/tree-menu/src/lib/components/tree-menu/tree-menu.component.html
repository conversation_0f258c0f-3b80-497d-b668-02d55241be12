<mat-tree [dataSource]="dataSource" [treeControl]="treeControl">
  <mat-tree-node
    *matTreeNodeDef="let node"
    finDropList
    [finDropListCustomPlaceholder]="true"
    [finDropListId]="node._node?.levelLabelString"
    [finDropListConnectedTo]="
      nodesTemplateData[node._node?.levelLabelString].connectedTo
    "
    (finDropListDropped)="drop($event, node)"
    (finDropListExited)="dropListExited($event)"
    (finDropListEntered)="dropListEntered($event)"
    class="fin-flex-col"
  >
    @if (
      nodesTemplateData[node._node?.levelLabelString].template;
      as template
    ) {
      @if (template!.draggable$ | async) {
        <fin-tree-node
          [node]="node"
          [active]="node.active"
          [appearance]="template?.appearance ?? finTreeNodeAppearance.PRIMARY"
          [hoverHighlight]="template.hoverHighlight"
          [nodeSeparator]="template.nodeSeparator"
          [baseOffset]="template.baseOffset"
          [levelOffset]="template.levelOffset"
          [nodeClick]="template.nodeClick"
          [hideExpandButton]="template.hideExpandButton"
          (finTreeNodeToggle)="toggle($event)"
          finDrag
          [dragHandle]="true"
          [finDragDisabled]="false"
          (finDragStarted)="dragStart(node)"
          (finDraggableChange)="draggableChange($event)"
        >
          <ng-container
            *ngTemplateOutlet="template!.template; context: { $implicit: node }"
          ></ng-container>
        </fin-tree-node>
      } @else {
        <fin-tree-node
          [node]="node"
          [active]="node.active"
          [appearance]="template?.appearance ?? finTreeNodeAppearance.PRIMARY"
          [hoverHighlight]="template.hoverHighlight"
          [nodeSeparator]="template.nodeSeparator"
          [baseOffset]="template.baseOffset"
          [levelOffset]="template.levelOffset"
          [nodeClick]="template.nodeClick"
          [hideExpandButton]="template.hideExpandButton"
          (finTreeNodeToggle)="toggle($event)"
          [dragHandle]="false"
          (finDraggableChange)="draggableChange($event)"
        >
          <ng-container
            *ngTemplateOutlet="template!.template; context: { $implicit: node }"
          ></ng-container>
        </fin-tree-node>
      }
    }
  </mat-tree-node>

  <mat-nested-tree-node
    *matTreeNodeDef="let node; when: hasChild"
    finDropList
    [finDropListCustomPlaceholder]="true"
    [finDropListId]="node._node?.levelLabelString"
    [finDropListConnectedTo]="
      nodesTemplateData[node._node?.levelLabelString].connectedTo
    "
    (finDropListDropped)="drop($event, node)"
    (finDropListExited)="dropListExited($event)"
    (finDropListEntered)="dropListEntered($event)"
    class="fin-flex-col"
  >
    @if (
      nodesTemplateData[node._node?.levelLabelString].template;
      as template
    ) {
      @if (template!.draggable$ | async) {
        <fin-tree-node
          [node]="node"
          [expandable]="node?.children && node?.children?.length > 0"
          [expanded]="treeControl.isExpanded(node)"
          [active]="node.active"
          [appearance]="template?.appearance ?? finTreeNodeAppearance.PRIMARY"
          [hoverHighlight]="template.hoverHighlight"
          [nodeSeparator]="template.nodeSeparator"
          [baseOffset]="template.baseOffset"
          [levelOffset]="template.levelOffset"
          [nodeClick]="template.nodeClick"
          [hideExpandButton]="template.hideExpandButton"
          [dragHandle]="true"
          (finTreeNodeToggle)="toggle($event)"
          finDrag
          (finDragStarted)="dragStart(node)"
          (finDraggableChange)="draggableChange($event)"
        >
          <ng-container
            *ngTemplateOutlet="template!.template; context: { $implicit: node }"
          ></ng-container>
        </fin-tree-node>
      } @else {
        <fin-tree-node
          [node]="node"
          [expandable]="node?.children && node?.children?.length > 0"
          [expanded]="treeControl.isExpanded(node)"
          [active]="node.active"
          [appearance]="template?.appearance ?? finTreeNodeAppearance.PRIMARY"
          [hoverHighlight]="template.hoverHighlight"
          [nodeSeparator]="template.nodeSeparator"
          [baseOffset]="template.baseOffset"
          [levelOffset]="template.levelOffset"
          [dragHandle]="false"
          [nodeClick]="template.nodeClick"
          [hideExpandButton]="template.hideExpandButton"
          (finTreeNodeToggle)="toggle($event)"
        >
          <ng-container
            *ngTemplateOutlet="template!.template; context: { $implicit: node }"
          ></ng-container>
        </fin-tree-node>
      }
      <div
        [ngClass]="{ 'fin-hidden': !treeControl.isExpanded(node) }"
        role="group"
      >
        <ng-container matTreeNodeOutlet></ng-container>
      </div>
    }
  </mat-nested-tree-node>
</mat-tree>
