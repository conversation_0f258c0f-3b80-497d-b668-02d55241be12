import { NestedTreeControl } from '@angular/cdk/tree';
import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ContentChildren,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  QueryList,
  SimpleChanges,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import {
  MatTreeModule,
  MatTreeNestedDataSource,
  MatTreeNodeOutlet,
} from '@angular/material/tree';
import {
  FinDragDrop,
  FinDragDropModule,
  FinDragEnter,
  FinDragExit,
} from '@fincloud/ui/drag-drop';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';
import { cloneDeep } from 'lodash-es';
import { FinTreeNodeDirective } from '../../directives/tree-node.directive';
import { FinTreeNodeAppearance } from '../../enums/tree-node-appearance';
import { FinTreeNode } from '../../models/fin-tree-node';
import { FinTreeNodeDrop } from '../../models/fin-tree-node-drop';
import { FinTreeNodeStructure } from '../../models/fin-tree-node-structure';
import { FinTreeNodeTemplateData } from '../../models/fin-tree-node-template-data';
import { FinTreeNodeToggle } from '../../models/fin-tree-node-toggle';
import { FinTreeNodeComponent } from '../tree-node/tree-node.component';
@Component({
  selector: 'fin-tree-menu',
  imports: [
    CommonModule,
    MatTreeModule,
    MatButtonModule,
    MatIconModule,
    MatTreeNodeOutlet,
    FinTreeNodeComponent,
    FinIconModule,
    FinDragDropModule,
  ],
  standalone: true,
  templateUrl: './tree-menu.component.html',
  styleUrl: './tree-menu.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinTreeMenuComponent<T> implements AfterViewInit, OnChanges {
  /** The data needed to render the tree. */
  @Input({ required: true }) data: FinTreeNode<T>[] = [];

  /** The default appearance of the tree component if it is not specified in the template. */
  @Input({ required: false }) appearance: FinTreeNodeAppearance =
    FinTreeNodeAppearance.PRIMARY;

  /** Whether to expand all nodes when a node is dragged. It will return the state of expansion after dropping the node. */
  @Input() expandOnDrop = false;

  /** All nodes will be expanded initially. */
  @Input({ required: false }) alwaysExpand = false;

  /** When the dropped element is dropped. */
  @Output() finDropListDropped = new EventEmitter<FinTreeNodeDrop<T>>();

  /** When the dropped element is exited. */
  @Output()
  private finDropListExited = new EventEmitter<FinDragExit<T>>();

  /** When the dropped element is entered. */
  @Output()
  private finDropListEntered = new EventEmitter<FinDragEnter<T>>();

  /** When the node is toggled. */
  @Output() private finTreeNodeToggle = new EventEmitter<
    FinTreeNodeToggle<T>
  >();

  @ContentChildren(FinTreeNodeDirective)
  treeNodeTemplates!: QueryList<FinTreeNodeDirective<FinTreeNode<T>>>;

  protected nodesTemplateData: FinTreeNodeTemplateData<T> = {};
  protected finTreeNodeAppearance = FinTreeNodeAppearance;
  protected finSize = FinSize;
  protected dataSource = new MatTreeNestedDataSource<
    FinTreeNodeStructure<FinTreeNode<T>>
  >();

  protected treeControl = new NestedTreeControl<
    FinTreeNodeStructure<FinTreeNode<T>>
  >(
    (
      node: FinTreeNodeStructure<FinTreeNode<T>>,
    ): FinTreeNodeStructure<FinTreeNode<T>>[] => {
      return (node?.children ?? []) as FinTreeNodeStructure<FinTreeNode<T>>[];
    },
  );

  private expansionStates: Record<string, boolean> = {};
  private draggingNode: FinTreeNodeStructure<FinTreeNode<T>> | null = null;
  private draggableStates: Record<string, boolean> = {};

  constructor(private cd: ChangeDetectorRef) {}

  ngAfterViewInit() {
    const processedNodes = this.processNodes(this.data);
    this.nodesTemplateData = this.getNodesTemplateData(processedNodes);
    this.dataSource.data = processedNodes;
    this.treeControl.dataNodes = processedNodes;

    if (this.alwaysExpand) {
      this.treeControl.expandAll();
    } else {
      this.expandNodes(this.getDataExpandedNodes());
    }
    // We have to trigger change detection because when we expand the nodes manually it stays collapsed
    this.cd.detectChanges();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['data'] && !changes['data'].firstChange) {
      const processedNodes = this.processNodes(this.data);
      this.nodesTemplateData = this.getNodesTemplateData(processedNodes);
      this.dataSource.data = processedNodes;
      this.treeControl.dataNodes = processedNodes;

      if (this.alwaysExpand) {
        this.treeControl.expandAll();
      } else {
        this.expandNodes(this.getDataExpandedNodes());
      }
    }
  }

  protected draggableChange({
    templateName,
    draggable,
  }: {
    templateName: string;
    draggable: boolean;
  }) {
    this.draggableStates[templateName] = draggable;

    if (this.draggableStates[templateName] !== draggable) {
      this.nodesTemplateData = this.getNodesTemplateData(this.dataSource.data);
    }
  }

  protected dropListExited(event: FinDragExit<T>) {
    this.finDropListExited.emit(event);
  }

  protected dropListEntered(event: FinDragEnter<T>) {
    this.finDropListEntered.emit(event);
  }

  protected hasChild = (
    _: number,
    node: FinTreeNodeStructure<FinTreeNode<T>>,
  ) =>
    !!node?.children &&
    (node?.children as FinTreeNodeStructure<FinTreeNode<T>>[]).length > 0;

  protected drop(
    event: FinDragDrop<T>,
    hostNode: FinTreeNodeStructure<FinTreeNode<T>>,
  ) {
    const oldPlace = this.draggingNode?._node?.levelLabel ?? [];
    const newPlace = hostNode._node?.levelLabel ?? [];
    const moveOnEmptyGroup = hostNode.children?.length === 0 ? true : false;
    let correctPosition = true;

    // If the new place is one level above the old place, we need to add the element in the first position
    if (newPlace.length === oldPlace.length - 1) {
      const movingDirection = this.moveDirection(oldPlace, [...newPlace, 1]);

      if (event.currentIndex === 0 && movingDirection === 1) {
        newPlace[newPlace.length - 1] =
          newPlace[newPlace.length - 1] - 1 > 0
            ? newPlace[newPlace.length - 1] - 1
            : 1;
      }

      if (event.currentIndex === 0 && movingDirection === -1) {
        newPlace[newPlace.length - 1] =
          newPlace[newPlace.length - 1] - 1 > 0
            ? newPlace[newPlace.length - 1] - 1
            : 1;
      }

      newPlace.push(1);
      const newPlaceAsString = newPlace.join('-');
      const newHostNode = this.flattenNodes(this.dataSource.data).find(
        (n) => n._node?.levelLabelString === newPlaceAsString,
      );

      if (newHostNode) {
        hostNode = newHostNode;
      }

      correctPosition = false;
    }

    if (oldPlace.length === 0 || newPlace.length === 0) {
      this.draggingNode = null;
      return;
    }

    if (hostNode && !this.draggableStates[hostNode.templateName ?? '']) {
      this.draggingNode = null;
      return;
    }

    // It is not possible to drop elements on other level of the tree
    if (
      (!moveOnEmptyGroup &&
        hostNode._node?.level !== this.draggingNode?._node?.level) ||
      (moveOnEmptyGroup &&
        hostNode._node?.level !== (this.draggingNode?._node?.level ?? 0) - 1)
    ) {
      this.draggingNode = null;
      return;
    }

    const movingDirection = this.moveDirection(oldPlace, newPlace);

    // If the moving direction is 0, it means that the node is already in the new place
    if (movingDirection === 0) {
      this.draggingNode = null;
      return;
    }

    let tree = cloneDeep(this.dataSource.data);

    this.removedDraggedNode(tree, oldPlace);

    const dropList = this.getTargetLevel(tree, newPlace);

    if (dropList) {
      const indexOfHost = dropList.findIndex(
        (n) => n._node?.levelLabelString === hostNode._node?.levelLabelString,
      );

      let indexPosition = correctPosition ? 1 : 0;

      if (correctPosition) {
        if (event.currentIndex === 0 && movingDirection === 1) {
          // Correct the index of new position
          newPlace[newPlace.length - 1] =
            newPlace[newPlace.length - 1] - 1 > 0
              ? newPlace[newPlace.length - 1] - 1
              : 1;
          indexPosition = 0;
        }

        if (event.currentIndex === 0 && movingDirection === -1) {
          indexPosition = 0;
        }

        if (event.currentIndex === 1 && movingDirection === -1) {
          // Correct the index of new position
          newPlace[newPlace.length - 1] =
            newPlace[newPlace.length - 1] + 1 > dropList.length
              ? dropList.length
              : newPlace[newPlace.length - 1] + 1;
        }
      }

      dropList.splice(
        indexOfHost + indexPosition,
        0,
        this.draggingNode as FinTreeNodeStructure<FinTreeNode<T>>,
      );
    }

    const currentExpandedNodes = this.getCurrentExpandedNodes();
    tree = this.removeNodeMetadata(tree);
    const processedNodes = this.processNodes(tree);
    this.nodesTemplateData = this.getNodesTemplateData(processedNodes);
    this.dataSource.data = processedNodes;
    this.treeControl.dataNodes = processedNodes;

    const oldPositionIndex = oldPlace.join('-');
    const newPositionIndex = newPlace.join('-');

    if (this.expansionStates[oldPositionIndex] !== undefined) {
      this.expansionStates[newPositionIndex] =
        this.expansionStates[oldPositionIndex];
      delete this.expansionStates[oldPositionIndex];
    }

    this.expandNodes(currentExpandedNodes);

    this.finDropListDropped.emit({
      ...event,
      treeMenu: {
        movedNode: this.draggingNode,
        hostNode: hostNode,
        oldPositionIndex,
        newPositionIndex,
        tree,
      },
    });

    this.draggingNode = null;
  }

  protected dragStart(node: FinTreeNodeStructure<FinTreeNode<T>>) {
    // Store current expansion state
    this.expansionStates = this.getExpansionMap();
    this.draggingNode = node;

    if (this.expandOnDrop) {
      const allNodes = this.flattenNodes(this.dataSource.data);
      const sameLevelNodes = allNodes.filter(
        (n) => n._node?.level === node._node?.level,
      );

      const parentNodes = this.getAllParentNodes(sameLevelNodes);

      // Expand all parent nodes
      parentNodes.forEach((parentNode) => {
        this.treeControl.expand(parentNode);
      });

      // Expand same level nodes that have children
      sameLevelNodes
        .filter((n) => this.hasChild(0, n))
        .forEach((n) => {
          this.treeControl.collapse(n);
        });

      this.cd.detectChanges();
    } else {
      this.closeAllNodesOnSameLevel(node);
    }
  }

  protected toggle(data: FinTreeNodeToggle<T>): void {
    this.finTreeNodeToggle.emit(data);
  }

  private getTemplateByName(
    name: string | undefined,
  ): FinTreeNodeDirective<FinTreeNode<T>> {
    return (
      this.treeNodeTemplates?.find(
        (template) => template.templateName === name,
      ) ??
      this.treeNodeTemplates?.find(
        (template) => template.templateName === undefined,
      ) ??
      this.treeNodeTemplates?.first
    );
  }

  private removeNodeMetadata<T>(
    nodes: FinTreeNodeStructure<FinTreeNode<T>>[],
  ): FinTreeNode<T>[] {
    return nodes.map((node) => {
      const cleanNode = { ...node };
      delete cleanNode._node;

      if (cleanNode.children?.length) {
        cleanNode.children = this.removeNodeMetadata(
          cleanNode.children as FinTreeNodeStructure<FinTreeNode<T>>[],
        );
      }

      return cleanNode;
    });
  }

  private getDataExpandedNodes() {
    return this.flattenNodes(this.dataSource.data)
      .filter((n) => n?.expanded)
      .map((n) => n?._node?.levelLabel ?? []);
  }

  private getCurrentExpandedNodes() {
    return this.flattenNodes(this.dataSource.data)
      .filter((n) => this.treeControl.isExpanded(n))
      .map((n) => n?._node?.levelLabel ?? []);
  }

  private expandNodes(expandedNodes: number[][]) {
    const allNodes = this.flattenNodes(this.dataSource.data);

    const expandedNodesWithParents = expandedNodes.reduce(
      (acc: string[], levels) => {
        const combinations = levels.map((_, index) =>
          levels.slice(0, index + 1).join('-'),
        );
        return [...acc, ...combinations];
      },
      [],
    );

    const nodesByLabel = allNodes.reduce(
      (acc, node) => {
        if (node._node?.levelLabelString) {
          acc[node._node.levelLabelString] = node;
        }
        return acc;
      },
      {} as Record<string, FinTreeNodeStructure<FinTreeNode<T>>>,
    );

    expandedNodesWithParents.forEach((node) => {
      if (nodesByLabel[node]) {
        this.treeControl.expand(nodesByLabel[node]);
      }
    });
  }

  private removedDraggedNode(
    tree: FinTreeNodeStructure<FinTreeNode<T>>[],
    oldPlace: number[],
  ) {
    const dragList = this.getTargetLevel(tree, oldPlace);

    if (dragList && dragList.length > 0) {
      const indexForRemove = dragList.findIndex(
        (n) =>
          n._node?.levelLabelString ===
          this.draggingNode?._node?.levelLabelString,
      );

      dragList.splice(indexForRemove, 1);
    }
  }

  private moveDirection(
    oldPosition: number[],
    newPosition: number[],
  ): 1 | 0 | -1 {
    if (oldPosition.length !== newPosition.length) {
      return 0;
    }

    const oldPositionValue = this.multiplyLevels(oldPosition);
    const newPositionValue = this.multiplyLevels(newPosition);

    if (oldPositionValue === newPositionValue) {
      return 0;
    }

    return oldPositionValue < newPositionValue ? 1 : -1;
  }

  private multiplyLevels(levels: number[]): number {
    const indexMultiplier = 10;

    return [...levels].reverse().reduce((acc, level, index) => {
      return acc + level * indexMultiplier ** index;
    }, 0);
  }

  private getTargetLevel(
    tree: FinTreeNodeStructure<FinTreeNode<T>>[],
    levelLabel: number[],
  ): FinTreeNodeStructure<FinTreeNode<T>>[] {
    if (levelLabel.length === 1) {
      return tree;
    }

    const flattenedTree = this.flattenNodes(tree);
    const label = [...levelLabel];
    label.pop();
    const levelLabelAsString = label.join('-');

    const targetLevel = flattenedTree.find(
      (n) => n._node?.levelLabelString === levelLabelAsString,
    );

    return targetLevel?.children ?? [];
  }

  private closeAllNodesOnSameLevel(node: FinTreeNodeStructure<FinTreeNode<T>>) {
    const allNodes = this.flattenNodes(this.dataSource.data);
    const sameLevelNodes = allNodes.filter(
      (n) => n._node?.level === node._node?.level,
    );
    sameLevelNodes.forEach((n) => {
      this.treeControl.collapse(n);
    });
  }

  private getRelativeNodesIds(
    nodes: FinTreeNodeStructure<FinTreeNode<T>>[],
    node: FinTreeNodeStructure<FinTreeNode<T>>,
  ): string[] {
    const relativeLevelNodes = nodes.filter(
      (n) =>
        n._node?.level === node._node?.level &&
        n._node?.levelLabelString !== node._node?.levelLabelString,
    );

    let parentNodes: FinTreeNodeStructure<FinTreeNode<T>>[] = [];

    if ((node._node?.level ?? 1) - 1 > 0) {
      const parentLevelString = node._node?.levelLabelString
        .split('-')
        .slice(0, -1)
        .join('-');

      parentNodes = nodes.filter(
        (n) =>
          n._node?.level === (node._node?.level ?? 1) - 1 &&
          n._node?.levelLabelString !== node._node?.levelLabelString &&
          n._node?.levelLabelString !== parentLevelString,
      );
    }

    return [...relativeLevelNodes, ...parentNodes].map(
      (n) => `${n._node?.levelLabelString}`,
    );
  }

  private processNodes(
    nodes: FinTreeNode<T>[],
    parentLevelLabel: number[] = [],
    currentLevel = 1,
    parentNode: FinTreeNodeStructure<FinTreeNode<T>> | null = null,
  ): FinTreeNodeStructure<FinTreeNode<T>>[] {
    return nodes.map((node, index) => {
      const currentLevelLabel = [...parentLevelLabel, index + 1];

      const processedNode: FinTreeNodeStructure<FinTreeNode<T>> = {
        ...node,
        _node: {
          parent: parentNode,
          levelLabel: currentLevelLabel,
          levelLabelString: currentLevelLabel.join('-'),
          level: currentLevel,
          index: index + 1,
        },
      };

      if (node.children?.length) {
        processedNode.children = this.processNodes(
          node.children,
          currentLevelLabel,
          currentLevel + 1,
          processedNode, // Pass the current node as parent for children
        );
      }

      return processedNode;
    });
  }

  private getNodesTemplateData(
    nodes: FinTreeNodeStructure<FinTreeNode<T>>[],
  ): FinTreeNodeTemplateData<T> {
    const flattenedNodes = this.flattenNodes(nodes);

    return flattenedNodes.reduce((acc, node) => {
      const template = this.getTemplateByName(node.templateName);
      acc[node._node?.levelLabelString ?? ''] = {
        connectedTo: this.getRelativeNodesIds(flattenedNodes, node),
        template,
      };

      return acc;
    }, {} as FinTreeNodeTemplateData<T>);
  }

  private getAllParentNodes(
    nodes: FinTreeNodeStructure<FinTreeNode<T>>[],
  ): Set<FinTreeNodeStructure<FinTreeNode<T>>> {
    const parentNodes = new Set<FinTreeNodeStructure<FinTreeNode<T>>>();

    nodes.forEach((node) => {
      let currentNode = node;

      // For each node, traverse up through all parents until reaching root
      while (currentNode._node?.parent) {
        parentNodes.add(currentNode._node.parent);
        currentNode = currentNode._node.parent;
      }
    });

    return parentNodes;
  }

  private flattenNodes(
    nodes: FinTreeNodeStructure<FinTreeNode<T>>[],
    result: FinTreeNodeStructure<FinTreeNode<T>>[] = [],
  ): FinTreeNodeStructure<FinTreeNode<T>>[] {
    nodes.forEach((n) => {
      result.push(n);
      if (n.children?.length) {
        this.flattenNodes(
          n.children as FinTreeNodeStructure<FinTreeNode<T>>[],
          result,
        );
      }
    });
    return result;
  }

  private getExpansionMap(): Record<string, boolean> {
    const expansionMap: Record<string, boolean> = {};

    const processNode = (node: FinTreeNodeStructure<FinTreeNode<T>>) => {
      if (node._node?.levelLabel) {
        expansionMap[node._node.levelLabelString] =
          this.treeControl.isExpanded(node);
      }

      // Process children if they exist
      if (node.children?.length) {
        (node.children as FinTreeNodeStructure<FinTreeNode<T>>[]).forEach(
          processNode,
        );
      }
    };

    this.dataSource.data.forEach(processNode);

    return expansionMap;
  }
}
