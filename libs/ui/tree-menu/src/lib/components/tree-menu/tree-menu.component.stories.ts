import { CommonModule } from '@angular/common';
import { MatTreeModule } from '@angular/material/tree';
import { FinIconModule } from '@fincloud/ui/icon';
import {
  componentWrapperDecorator,
  moduleMetadata,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { FinTreeNodeDirective } from '../../directives/tree-node.directive';
import { FinTreeNode } from '../../models/fin-tree-node';
import { FinTreeMenuModule } from '../../tree-menu.module';
import { FinTreeMenuComponent } from './tree-menu.component';

interface BasicNode {
  label: string;
}

const meta: Meta<FinTreeMenuComponent<unknown>> = {
  component: FinTreeMenuComponent,
  title: 'Components/Tree Menu',
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        FinTreeMenuModule,
        MatTreeModule,
        FinTreeNodeDirective,
        FinIconModule,
      ],
    }),
    componentWrapperDecorator(
      (story) => `<div class="fin-w-[400px]">${story}</div>`,
    ),
  ],
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinTreeMenuModule, MatTreeModule, FinTreeNodeDirective } from "@fincloud/ui/tree-menu"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=28449-11384&m=dev',
    },
  },
};
export default meta;
type Story = StoryObj<FinTreeMenuComponent<unknown>>;

export const Primary: Story = {
  args: {
    data: [
      {
        label: 'Fruit',
        children: [
          { label: 'Apple' },
          { label: 'Banana' },
          { label: 'Fruit loops' },
        ],
      },
      {
        label: 'Vegetables',
        children: [
          {
            label: 'Green',
            children: [
              { label: 'Broccoli' },
              { label: 'Brussels sprouts', children: [{ label: 'White' }] },
            ],
          },
          {
            label: 'Orange',
            children: [{ label: 'Pumpkins' }, { label: 'Carrots' }],
          },
        ],
      },
    ] as FinTreeNode<BasicNode>[],
  },
  render: (args) => ({
    props: args,
    template: `
    <fin-tree-menu [data]="data">
      <ng-template finTreeNode let-node>
        {{ node.label }}
      </ng-template>
    </fin-tree-menu>
    `,
  }),
};

export const WithCounter: Story = {
  args: {
    data: [
      {
        label: 'Fruit',
        children: [
          { label: 'Apple' },
          { label: 'Banana' },
          { label: 'Fruit loops' },
        ],
      },
      {
        label: 'Vegetables',
        children: [
          {
            label: 'Green',
            children: [
              { label: 'Broccoli' },
              { label: 'Brussels sprouts', children: [{ label: 'White' }] },
            ],
          },
          {
            label: 'Orange',
            children: [{ label: 'Pumpkins' }, { label: 'Carrots' }],
          },
        ],
      },
    ] as FinTreeNode<BasicNode>[],
  },
  render: (args) => ({
    props: args,
    template: `
    <fin-tree-menu [data]="data">
      <ng-template finTreeNode let-node>
        <fin-tree-node-counter [node]="node"></fin-tree-node-counter>
        {{ node.label }}
      </ng-template>
    </fin-tree-menu>
    `,
  }),
};

export const FolderStructure: Story = {
  args: {
    data: [
      {
        label: 'Collateral',
        active: true,
        children: [
          { label: 'Evaluation', templateName: 'field' },
          { label: 'Evaluation', templateName: 'field' },
          {
            label: 'Documents',
            templateName: 'folderRoot',
            children: [
              {
                label: 'Property description',
                templateName: 'folder',
                children: [
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                ],
              },
              {
                label: 'Property description',
                templateName: 'folder',
                children: [
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                ],
              },
              {
                label: 'Property description',
                templateName: 'folder',
                children: [
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                  {
                    label: 'Property description',
                    templateName: 'document',
                    expanded: true,
                  },
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                ],
              },
              {
                label: 'Property description',
                templateName: 'folder',
                children: [
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                ],
              },
              {
                label: 'Property description',
                templateName: 'folder',
                children: [
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                ],
              },
              {
                label: 'Property description 11',
                templateName: 'folder',
                children: [
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                  {
                    label: 'Property description 222',
                    templateName: 'document',
                    expanded: true,
                  },
                ],
              },
              {
                label: 'Property description',
                templateName: 'folder',
                children: [
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                  { label: 'Property description', templateName: 'document' },
                ],
              },
            ],
          },
        ],
      },
    ] as FinTreeNode<BasicNode>[],
  },
  render: (args) => ({
    props: args,
    template: `
    <fin-tree-menu [data]="data">
      <ng-template finTreeNode let-node>
        {{ node.label }}
      </ng-template>
      <ng-template finTreeNode let-node templateName="field" appearance="secondary">
        {{ node.label }}
      </ng-template>
      <ng-template finTreeNode let-node templateName="folderRoot" appearance="secondary">
        <span class="fin-font-semibold">{{ node.label }}</span>
      </ng-template>
      <ng-template finTreeNode let-node templateName="folder" appearance="secondary">
        <fin-icon name="folder" size="S" />
        {{ node.label }}
      </ng-template>
      <ng-template finTreeNode let-node templateName="document" appearance="secondary">
        <fin-icon name="article" size="S" />
        {{ node.label }}
      </ng-template>
    </fin-tree-menu>
    `,
  }),
};
