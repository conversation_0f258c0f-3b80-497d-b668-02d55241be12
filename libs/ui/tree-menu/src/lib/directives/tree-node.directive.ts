import {
  booleanAttribute,
  Directive,
  Input,
  numberAttribute,
  TemplateRef,
} from '@angular/core';
import { Observable } from 'rxjs';
import { FinTreeNodeAppearance } from '../enums/tree-node-appearance';
import { FinTreeNodeContext } from '../models/fin-tree-node-context';

@Directive({
  selector: '[finTreeNode]',
  standalone: true,
})
export class FinTreeNodeDirective<T> {
  @Input() templateName: string | undefined;
  @Input() appearance: FinTreeNodeAppearance = FinTreeNodeAppearance.PRIMARY;
  @Input() draggable!: Observable<boolean>;
  @Input({ required: false, transform: booleanAttribute }) hoverHighlight =
    true;
  @Input({ required: false, transform: booleanAttribute }) nodeSeparator = true;
  @Input({ required: false, transform: numberAttribute }) baseOffset = 16;
  @Input({ required: false, transform: numberAttribute }) levelOffset = 4;
  @Input() nodeClick: ((event: MouseEvent, node: T) => void) | undefined;
  @Input() hideExpandButton = false;

  get draggable$() {
    return this.draggable;
  }

  constructor(public template: TemplateRef<FinTreeNodeContext<T>>) {}

  // Add static guard method
  static ngTemplateContextGuard<T>(
    dir: FinTreeNodeDirective<T>,
    ctx: unknown,
  ): ctx is FinTreeNodeContext<T> {
    return true;
  }
}
