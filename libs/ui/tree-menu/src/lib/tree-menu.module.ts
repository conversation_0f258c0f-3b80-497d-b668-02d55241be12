import { CdkTreeModule } from '@angular/cdk/tree';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FinTreeMenuComponent } from './components/tree-menu/tree-menu.component';
import { FinTreeNodeCounterComponent } from './components/tree-node-counter/tree-node-counter.component';
import { FinTreeNodeComponent } from './components/tree-node/tree-node.component';
import { FinTreeNodeDirective } from './directives/tree-node.directive';
@NgModule({
  imports: [
    CommonModule,
    FinTreeMenuComponent,
    FinTreeNodeComponent,
    FinTreeNodeDirective,
    FinTreeNodeCounterComponent,
  ],
  exports: [
    FinTreeMenuComponent,
    FinTreeNodeComponent,
    FinTreeNodeDirective,
    FinTreeNodeCounterComponent,
  ],
  providers: [CdkTreeModule],
})
export class FinTreeMenuModule {}
