import { FinDragDrop } from '@fincloud/ui/drag-drop';
import { FinTreeNode } from './fin-tree-node';
import { FinTreeNodeStructure } from './fin-tree-node-structure';

/** Event emitted when the user drops a draggable item inside a drop container. */
export type FinTreeNodeDrop<T = any> = FinDragDrop<T> & {
  treeMenu: {
    movedNode: FinTreeNodeStructure<FinTreeNode<T>> | null;
    hostNode: FinTreeNodeStructure<FinTreeNode<T>>;
    oldPositionIndex: string;
    newPositionIndex: string;
    tree: FinTreeNode<T>[];
  };
};
