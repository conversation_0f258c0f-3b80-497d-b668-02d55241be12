# Button Components

A comprehensive collection of button components for performing actions, navigation, and user interactions. The button module provides four distinct button types: standard buttons, action buttons, floating action buttons (FAB), and link buttons.

## Installation

```bash
npm install @fincloud/ui
```

```typescript
import { FinButtonModule } from '@fincloud/ui/button';

@Component({
  imports: [FinButtonModule],
  // ...
})
export class YourComponent {}
```

## Basic Usage

### Standard Button

```html
<button fin-button>Click me</button>
```

```typescript
import { Component } from '@angular/core';
import { FinButtonModule } from '@fincloud/ui/button';

@Component({
  selector: 'app-example',
  imports: [FinButtonModule],
  template: ` <button fin-button>Click me</button> `,
})
export class ExampleComponent {}
```

## API Reference

### FinButtonComponent

#### Selector

- `button[fin-button]` - Standard button
- `button[fin-button-icon]` - Icon-only button

#### Inputs

| Name         | Type                                                              | Default                       | Description                                 |
| ------------ | ----------------------------------------------------------------- | ----------------------------- | ------------------------------------------- |
| `size`       | `FinSize.XS \| FinSize.S \| FinSize.M \| FinSize.L \| FinSize.XL` | `FinSize.M`                   | Size of the button                          |
| `shape`      | `FinButtonShape`                                                  | `FinButtonShape.ROUND`        | Shape of the button (round or rectangle)    |
| `appearance` | `FinButtonAppearance`                                             | `FinButtonAppearance.PRIMARY` | Visual appearance style                     |
| `isActive`   | `boolean`                                                         | `false`                       | Keep the button in a hover state            |
| `attention`  | `boolean`                                                         | `false`                       | Highlight the button with attention styling |
| `showLoader` | `boolean`                                                         | `false`                       | Activate the button's loading state         |
| `elevation`  | `boolean`                                                         | `false`                       | Activate the button's elevation shadow      |

#### Content Projection

| Slot    | Description           |
| ------- | --------------------- |
| Default | Button text and icons |

### FinButtonActionComponent

#### Selector

- `button[fin-button-action]`

#### Inputs

| Name         | Type                                                                             | Default                       | Description                     |
| ------------ | -------------------------------------------------------------------------------- | ----------------------------- | ------------------------------- |
| `size`       | `FinSize.XS \| FinSize.S \| FinSize.M \| FinSize.L \| FinSize.XL \| FinSize.XXL` | `FinSize.S`                   | Size of the action button       |
| `shape`      | `FinButtonShape`                                                                 | `FinButtonShape.ROUND`        | Shape of the button             |
| `actionType` | `FinButtonActionType`                                                            | `FinButtonActionType.PRIMARY` | Type/style of the action button |

### FinButtonFabComponent

#### Selector

- `button[fin-button-fab]`

#### Inputs

| Name   | Type                     | Default     | Description                        |
| ------ | ------------------------ | ----------- | ---------------------------------- |
| `size` | `FinSize.S \| FinSize.M` | `FinSize.S` | Size of the floating action button |

### FinButtonLinkComponent

#### Selector

- `button[fin-button-link]`
- `a[fin-button-link]`

#### Inputs

No specific inputs - inherits standard button/anchor attributes.

## Configuration Options

### Enums

#### FinButtonAppearance

| Value         | Description                                     |
| ------------- | ----------------------------------------------- |
| `PRIMARY`     | Primary button style with brand colors          |
| `SECONDARY`   | Secondary button style with muted colors        |
| `INFORMATIVE` | Informative button style for neutral actions    |
| `STEALTH`     | Stealth button style with minimal visual impact |

#### FinButtonShape

| Value       | Description                              |
| ----------- | ---------------------------------------- |
| `ROUND`     | Rounded corners for modern appearance    |
| `RECTANGLE` | Sharp corners for traditional appearance |

#### FinButtonActionType

| Value         | Description                     |
| ------------- | ------------------------------- |
| `PRIMARY`     | Primary action button style     |
| `TERTIARY`    | Tertiary action button style    |
| `INFORMATIVE` | Informative action button style |

### Size Options

Available sizes from `@fincloud/ui/types`:

- `FinSize.XS` - Extra small
- `FinSize.S` - Small
- `FinSize.M` - Medium (default)
- `FinSize.L` - Large
- `FinSize.XL` - Extra large
- `FinSize.XXL` - Extra extra large (action buttons only)

## Advanced Examples

### Standard Button with All Options

```html
<button fin-button [size]="'l'" [shape]="'rectangle'" [appearance]="'primary'" [attention]="true" [showLoader]="isLoading" [elevation]="true" (click)="handleClick()">
  <fin-icon name="add"></fin-icon>
  Create New
</button>
```

```typescript
import { Component } from '@angular/core';
import { FinButtonModule, FinButtonAppearance, FinButtonShape } from '@fincloud/ui/button';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';

@Component({
  selector: 'app-advanced-button',
  imports: [FinButtonModule, FinIconModule],
  template: `
    <button fin-button [size]="buttonSize" [shape]="buttonShape" [appearance]="buttonAppearance" [attention]="hasAttention" [showLoader]="isLoading" [elevation]="hasElevation" (click)="handleClick()">
      <fin-icon name="add"></fin-icon>
      {{ buttonText }}
    </button>
  `,
})
export class AdvancedButtonComponent {
  buttonSize = FinSize.L;
  buttonShape = FinButtonShape.RECTANGLE;
  buttonAppearance = FinButtonAppearance.PRIMARY;
  hasAttention = true;
  isLoading = false;
  hasElevation = true;
  buttonText = 'Create New';

  handleClick(): void {
    this.isLoading = true;
    // Simulate async operation
    setTimeout(() => {
      this.isLoading = false;
      console.log('Action completed');
    }, 2000);
  }
}
```

### Action Button Example

```html
<button fin-button-action [size]="'xl'" [actionType]="'primary'" [shape]="'round'" (click)="performAction()">
  <fin-icon name="settings"></fin-icon>
</button>
```

### Floating Action Button (FAB)

```html
<button fin-button-fab [size]="'m'" [finActionMenuTrigger]="actionMenu.panel">
  <fin-icon src="assets/fabs/svgNeoGptFab.svg"></fin-icon>
</button>

<fin-actions-menu #actionMenu="finActionMenu" yPosition="above">
  <button fin-menu-item iconName="image" size="m">
    <ng-container finMenuItemTitle>Upload Image</ng-container>
  </button>
  <button fin-menu-item iconName="document" size="m">
    <ng-container finMenuItemTitle>Upload Document</ng-container>
  </button>
</fin-actions-menu>
```

### Link Button Example

```html
<a fin-button-link href="/help" class="fin-text-body-1-strong">
  <fin-icon name="help"></fin-icon>
  Help & Support
</a>

<button fin-button-link (click)="openModal()" class="fin-text-body-2-strong">Learn More</button>
```

### Button Variants Showcase

```html
<div class="fin-grid fin-grid-cols-4 fin-gap-4">
  <!-- Primary buttons -->
  <button fin-button [appearance]="'primary'" [shape]="'round'">
    <fin-icon name="add"></fin-icon>
    Primary
  </button>

  <!-- Secondary buttons -->
  <button fin-button [appearance]="'secondary'" [shape]="'round'">
    <fin-icon name="edit"></fin-icon>
    Secondary
  </button>

  <!-- Informative buttons -->
  <button fin-button [appearance]="'informative'" [shape]="'round'">
    <fin-icon name="info"></fin-icon>
    Informative
  </button>

  <!-- Stealth buttons -->
  <button fin-button [appearance]="'stealth'" [shape]="'round'">
    <fin-icon name="more"></fin-icon>
    Stealth
  </button>
</div>
```

## Best Practices

### Do's

- ✅ Use semantic button elements for actions and anchor elements for navigation
- ✅ Provide clear, descriptive button text that indicates the action
- ✅ Use appropriate button appearances based on action importance
- ✅ Include loading states for async operations using `showLoader`
- ✅ Use icons to enhance button meaning and improve usability
- ✅ Follow consistent sizing throughout your application
- ✅ Use attention styling sparingly for critical actions

### Don'ts

- ❌ Don't use multiple primary buttons in the same context
- ❌ Don't make buttons too small for touch interfaces
- ❌ Don't use vague button text like "Click here" or "Submit"
- ❌ Don't forget to handle disabled states appropriately
- ❌ Don't overuse attention styling as it loses effectiveness

### Accessibility Guidelines

- Ensure buttons have sufficient color contrast (4.5:1 minimum)
- Provide meaningful button text or aria-label for icon-only buttons
- Support keyboard navigation (Enter and Space keys)
- Use appropriate ARIA attributes for complex button states
- Ensure minimum touch target size of 44x44px for mobile

### Performance Tips

- Use OnPush change detection strategy (already implemented)
- Avoid frequent changes to button properties that trigger re-renders
- Use trackBy functions when rendering button lists
- Consider lazy loading for FAB action menus with many items

## Troubleshooting

### Common Issues

#### Issue: Button styles not applying correctly

**Solution:** Ensure the FinButtonModule is imported in your component or module.

```typescript
import { FinButtonModule } from '@fincloud/ui/button';

@Component({
  imports: [FinButtonModule],
  // ...
})
```

#### Issue: Icons not displaying in buttons

**Solution:** Import and include the FinIconModule alongside FinButtonModule.

```typescript
import { FinButtonModule } from '@fincloud/ui/button';
import { FinIconModule } from '@fincloud/ui/icon';

@Component({
  imports: [FinButtonModule, FinIconModule],
  // ...
})
```

#### Issue: Loading state not working

**Solution:** Ensure you're binding to the `showLoader` property correctly and that the value is a boolean.

```html
<!-- Correct -->
<button fin-button [showLoader]="isLoading">Submit</button>

<!-- Incorrect -->
<button fin-button showLoader="true">Submit</button>
```

#### Issue: Button click events not firing

**Solution:** Check that the button is not disabled and that the event handler is properly bound.

```html
<button fin-button (click)="handleClick()" [disabled]="isDisabled">Click me</button>
```

## Related Components

- [Icon](../icon/README.md) - For adding icons to buttons
- [Actions Menu](../actions-menu/README.md) - For dropdown menus with FAB buttons
- [Menu Item](../menu-item/README.md) - For menu items in action menus
- [Split Button](../split-button/README.md) - For buttons with dropdown options
- [Loader](../loader/README.md) - For loading indicators

## Storybook

View interactive examples and documentation in Storybook:

- [Button Component](https://lib-ui.neoshare.dev/?path=/docs/components-button--docs)
- [Button Action Component](https://lib-ui.neoshare.dev/?path=/docs/components-button-action--docs)
- [Button FAB Component](https://lib-ui.neoshare.dev/?path=/docs/components-button-fab--docs)
- [Button Link Component](https://lib-ui.neoshare.dev/?path=/docs/components-button-link--docs)

## Changelog

See [CHANGELOG.md](../../CHANGELOG.md) for version history and breaking changes.

## Contributing

See [CONTRIBUTING.md](../../CONTRIBUTING.md) for guidelines on contributing to this component.

## License

This component is part of the @fincloud/ui library and is licensed under [MIT License](../../LICENSE).
