import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FinButtonActionComponent } from './components/button-action/button-action.component';
import { FinButtonFabComponent } from './components/button-fab/button-fab.component';
import { FinButtonLinkComponent } from './components/button-link/button-link.component';
import { FinButtonComponent } from './components/button/button.component';

@NgModule({
  imports: [
    CommonModule,
    FinButtonComponent,
    FinButtonFabComponent,
    FinButtonLinkComponent,
    FinButtonActionComponent,
  ],
  exports: [
    FinButtonComponent,
    FinButtonFabComponent,
    FinButtonLinkComponent,
    FinButtonActionComponent,
  ],
})
export class FinButtonModule {}
