:host {
  @apply fin-flex;
  @apply fin-items-center;
  @apply fin-justify-center;
  @apply fin-leading-[1.6rem];
  @apply fin-rounded-full;

  box-shadow: 0 1.5rem 3rem 0 rgba(38, 40, 62, 0.2);
  background-color: theme('colors.color-surface-primary');

  &:hover {
    background-color: theme('colors.color-background-secondary-minimal');
  }

  &.fin-button {
    &-s {
      width: 4rem;
      height: 4rem;
      &::ng-deep {
        fin-icon {
          height: 2.1rem;
          width: 2.1rem;
          font-size: 2.1rem;
        }
      }
    }
    &-m {
      width: 5.6rem;
      height: 5.6rem;
      &::ng-deep {
        fin-icon {
          height: 3.6rem;
          width: 3.6rem;
          font-size: 3.6rem;
        }
      }
    }
  }
}
