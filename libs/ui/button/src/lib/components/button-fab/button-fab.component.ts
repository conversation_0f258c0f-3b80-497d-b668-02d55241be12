import { Component, Input } from '@angular/core';
import { FinSize } from '@fincloud/ui/types';

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'button[fin-button-fab]',
  standalone: true,
  imports: [],
  templateUrl: './button-fab.component.html',
  styleUrl: './button-fab.component.scss',
  host: {
    '[class]': 'buttonCssClasses',
  },
})
export class FinButtonFabComponent {
  /** Size of the button. */
  @Input() size: FinSize.S | FinSize.M = FinSize.S;

  protected get buttonCssClasses(): string {
    return `fin-button-fab fin-button-${this.size}`;
  }
}
