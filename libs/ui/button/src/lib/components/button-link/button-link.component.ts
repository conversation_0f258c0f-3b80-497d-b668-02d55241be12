import { HttpClientModule } from '@angular/common/http';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinAngularMaterialModule } from '@fincloud/utils/angular-material';

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: `
    button[fin-button-link],
    a[fin-button-link]
  `,
  standalone: true,
  imports: [
    MatIconModule,
    MatButtonModule,
    HttpClientModule,
    FinAngularMaterialModule,
    FinIconModule,
  ],
  templateUrl: './button-link.component.html',
  styleUrl: './button-link.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class:
      'fin-inline-flex fin-items-center fin-justify-center fin-gap-[0.8rem] fin-font-semibold',
  },
})
export class FinButtonLinkComponent {}
