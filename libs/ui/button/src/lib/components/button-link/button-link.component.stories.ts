import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { FinIconModule } from '@fincloud/ui/icon';
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { FinButtonLinkComponent } from './button-link.component';

const meta: Meta<FinButtonLinkComponent> = {
  component: FinButtonLinkComponent,
  title: 'Components/Buttons/Button Link',
  decorators: [
    moduleMetadata({
      imports: [CommonModule, MatButtonModule, FinIconModule],
    }),
  ],
  parameters: {
    docs: {
      description: {
        component: '`import { FinButtonModule } from "@fincloud/ui/button"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=21654-46558&t=FNrMxiGbIoB9D7yd-4',
    },
  },
};
export default meta;

type Story = StoryObj<FinButtonLinkComponent>;

export const AnchorLink: Story = {
  render: () => ({
    template: `
      <div class="fin-flex fin-items-center fin-gap-[3rem] fin-mb-[2rem]">
        <a fin-button-link class="fin-text-body-1-strong">Hyper Link</a>

        <a fin-button-link class="fin-text-body-2-strong">Hyper Link</a>

        <a fin-button-link class="fin-text-body-3-strong">Hyper Link</a>
      </div>

      <div class="fin-flex fin-items-center fin-gap-[3rem]">
        <a fin-button-link class="fin-text-body-1-strong">
          <fin-icon name="add"></fin-icon>
          Hyper Link
        </a>

        <a fin-button-link class="fin-text-body-2-strong">
          <fin-icon name="add" size="s"></fin-icon>
          Hyper Link
        </a>

        <a fin-button-link class="fin-text-body-3-strong">
          <fin-icon name="add" size="xs"></fin-icon>
          Hyper Link
        </a>
      </div>
    `,
  }),
};

export const ButtonLink: Story = {
  render: () => ({
    template: `
      <div class="fin-flex fin-items-center fin-gap-[3rem] fin-mb-[2rem]">
        <button fin-button-link class="fin-text-body-1-strong">Hyper Link</button>

        <button fin-button-link class="fin-text-body-2-strong">Hyper Link</button>

        <button fin-button-link class="fin-text-body-3-strong">Hyper Link</button>
      </div>

      <div class="fin-flex fin-items-center fin-gap-[3rem]">
        <button fin-button-link class="fin-text-body-1-strong">
          <fin-icon name="add"></fin-icon>
          Hyper Link
        </button>

        <button fin-button-link class="fin-text-body-2-strong">
          <fin-icon name="add" size="s"></fin-icon>
          Hyper Link
        </button>

        <button fin-button-link class="fin-text-body-3-strong">
          <fin-icon name="add" size="xs"></fin-icon>
          Hyper Link
        </button>
      </div>
    `,
  }),
};
