import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';
import {
  Args,
  moduleMetadata,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { FinButtonActionType } from '../../enums/fin-button-action-type';
import { FinButtonShape } from '../../enums/fin-button-shape';
import { FinButtonActionComponent } from './button-action.component';

const meta: Meta<FinButtonActionComponent> = {
  component: FinButtonActionComponent,
  title: 'Components/Buttons/Button Action',
  decorators: [
    moduleMetadata({
      imports: [CommonModule, MatButtonModule, FinIconModule],
    }),
  ],
  parameters: {
    docs: {
      description: {
        component: '`import { FinButtonModule } from "@fincloud/ui/button"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=26593-18280&m=dev',
    },
  },
};
export default meta;
type Story = StoryObj<FinButtonActionComponent>;

const renderTemplate = (args: Args, disabled?: boolean) => ({
  props: { ...args, disabled },
  template: `
      <button fin-button-action [size]="size" [actionType]="actionType" [shape]="shape">
        <fin-icon name="account_circle"></fin-icon>
      </button>
  `,
});

export const Primary: Story = {
  argTypes: {
    actionType: {
      options: [...Object.values(FinButtonActionType)],
      control: { type: 'select' },
    },
    size: {
      options: [...Object.values(FinSize)],
      control: { type: 'select' },
    },
    shape: {
      options: [...Object.values(FinButtonShape)],
      control: { type: 'select' },
    },
  },
  args: {
    actionType: FinButtonActionType.PRIMARY,
    size: FinSize.XL,
    shape: FinButtonShape.ROUND,
  },
  render: (args) => renderTemplate(args),
};

export const Tertiary: Story = {
  argTypes: {
    ...Primary.argTypes,
  },
  args: {
    ...Primary.args,
    actionType: FinButtonActionType.TERTIARY,
  },
  render: (args) => renderTemplate(args),
};

export const Informative: Story = {
  argTypes: {
    ...Primary.argTypes,
  },
  args: {
    ...Primary.args,
    actionType: FinButtonActionType.INFORMATIVE,
  },
  render: (args) => renderTemplate(args),
};
