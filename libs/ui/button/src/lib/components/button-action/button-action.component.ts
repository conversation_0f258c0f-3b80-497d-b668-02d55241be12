import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FinSize } from '@fincloud/ui/types';
import { FinButtonActionType } from '../../enums/fin-button-action-type';
import { FinButtonShape } from '../../enums/fin-button-shape';

/**
 * A clickable element used to perform actions.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-button-action--docs Storybook Reference}
 */

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'button[fin-button-action]',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './button-action.component.html',
  styleUrl: './button-action.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    '[class]': 'buttonCssClasses',
  },
})
export class FinButtonActionComponent {
  /** Size of the button. */
  @Input() size:
    | FinSize.XS
    | FinSize.S
    | FinSize.M
    | FinSize.L
    | FinSize.XL
    | FinSize.XXL = FinSize.S;

  /** Shape of the button. */
  @Input() shape: FinButtonShape = FinButtonShape.ROUND;

  /** Type of the button. */
  @Input() actionType: FinButtonActionType = FinButtonActionType.PRIMARY;

  protected get buttonCssClasses(): string {
    return `fin-button-action fin-button-${this.size} fin-button-action-${this.actionType} fin-button-${this.shape}`;
  }
}
