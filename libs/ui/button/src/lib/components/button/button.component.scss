:host {
  @apply fin-relative;

  &[fin-button] {
    &.fin-button-xl {
      @apply fin-text-body-1-strong;
      padding: 0 2.4rem;
      min-height: 4.8rem;
      ::ng-deep fin-icon {
        height: 2.4rem;
        width: 2.4rem;
        font-size: 2.4rem;
      }
    }
    &.fin-button-l {
      @apply fin-text-body-2-strong;
      padding: 0 1.8rem;
      min-height: 4rem;
      ::ng-deep fin-icon {
        height: 2.4rem;
        width: 2.4rem;
        font-size: 2.4rem;
      }
    }
    &.fin-button-m {
      @apply fin-text-body-2-strong;
      padding: 0 1.6rem;
      min-height: 3.2rem;
      ::ng-deep fin-icon {
        height: 2rem;
        width: 2rem;
        font-size: 2rem;
      }
    }
    &.fin-button-s {
      @apply fin-text-body-3-strong;
      padding: 0 1.6rem;
      min-height: 2.4rem;
      ::ng-deep fin-icon {
        height: 2rem;
        width: 2rem;
        font-size: 2rem;
      }
    }
    &.fin-button-xs {
      @apply fin-text-body-3-strong;
      padding: 0 1.4rem;
      min-height: 1.8rem;
      ::ng-deep fin-icon {
        height: 1.6rem;
        width: 1.6rem;
        font-size: 1.6rem;
      }
    }
  }

  &[fin-button-icon] {
    &.fin-button-xl {
      width: 4.8rem;
      height: 4.8rem;
      ::ng-deep fin-icon {
        height: 2.8rem;
        width: 2.8rem;
        font-size: 2.8rem;
      }
    }
    &.fin-button-l {
      width: 4rem;
      height: 4rem;
      ::ng-deep fin-icon {
        height: 2.4rem;
        width: 2.4rem;
        font-size: 2.4rem;
      }
    }
    &.fin-button-m {
      width: 3.2rem;
      height: 3.2rem;
      ::ng-deep fin-icon {
        height: 2rem;
        width: 2rem;
        font-size: 2rem;
      }
    }
    &.fin-button-s {
      width: 2.4rem;
      height: 2.4rem;
      ::ng-deep fin-icon {
        height: 1.6rem;
        width: 1.6rem;
        font-size: 1.6rem;
      }
    }
    &.fin-button-xs {
      width: 1.8rem;
      height: 1.8rem;
      ::ng-deep fin-icon {
        height: 1.2rem;
        width: 1.2rem;
        font-size: 1.2rem;
      }
    }
  }

  &[fin-button],
  &[fin-button-icon] {
    &.fin-button-primary {
      @apply fin-bg-buttons-main-set-primary-color-background-default;
      @apply fin-text-buttons-main-set-primary-color-label-default;

      &:hover,
      &.fin-button-active {
        @apply fin-bg-buttons-main-set-primary-color-background-hover;
        @apply fin-text-buttons-main-set-primary-color-label-hover;
      }

      &.fin-button-attention {
        @apply fin-bg-buttons-main-set-primary-color-background-attention;
        @apply fin-text-buttons-main-set-primary-color-label-attention;

        &:hover,
        &.fin-button-active {
          @apply fin-bg-buttons-main-set-primary-color-background-attention-hover;
        }
        &:disabled {
          @apply fin-bg-buttons-main-set-primary-color-background-disabled;
          @apply fin-text-buttons-main-set-primary-color-label-disabled;
        }
      }

      &:disabled {
        @apply fin-bg-buttons-main-set-primary-color-background-disabled;
        @apply fin-text-buttons-main-set-primary-color-label-disabled;
        @apply fin-cursor-not-allowed;
      }
    }

    &.fin-button-secondary {
      @apply fin-bg-transparent;
      border: 0.1rem solid;
      @apply fin-text-buttons-main-set-secondary-color-label-default;
      @apply fin-border-buttons-main-set-secondary-color-border-default;

      &:hover,
      &.fin-button-active {
        @apply fin-bg-buttons-main-set-secondary-color-background-hover;
        @apply fin-text-buttons-main-set-secondary-color-label-hover;
      }
      &.fin-button-attention {
        border: 0.1rem solid;
        @apply fin-border-buttons-main-set-secondary-color-border-attention;
        @apply fin-text-buttons-main-set-secondary-color-label-attention;
        &:hover,
        &.fin-button-active {
          @apply fin-bg-buttons-main-set-secondary-color-background-attention-hover;
          @apply fin-text-buttons-main-set-secondary-color-label-attention-hover;
        }
        &:disabled {
          @apply fin-bg-transparent;
          @apply fin-text-buttons-main-set-secondary-color-label-disabled;
        }
      }
      &:disabled {
        @apply fin-bg-transparent;
        @apply fin-text-buttons-main-set-secondary-color-label-disabled;
        border: 0.1rem solid;
        @apply fin-border-buttons-main-set-secondary-color-border-disabled;
        @apply fin-cursor-not-allowed;
      }
    }

    &.fin-button-informative {
      @apply fin-bg-buttons-main-set-informative-color-background-default;
      @apply fin-text-buttons-main-set-informative-color-label-default;

      &:hover,
      &.fin-button-active {
        @apply fin-bg-buttons-main-set-informative-color-background-hover;
        @apply fin-text-buttons-main-set-informative-color-label-hover;
      }
      &.fin-button-attention {
        @apply fin-bg-buttons-main-set-informative-color-background-attention;
        @apply fin-text-buttons-main-set-informative-color-label-attention;

        &:hover {
          @apply fin-bg-buttons-main-set-informative-color-background-attention-hover;
          @apply fin-text-buttons-main-set-informative-color-label-attention-hover;
        }

        &:disabled {
          @apply fin-text-buttons-main-set-informative-color-label-disabled;
        }
      }
      &:disabled {
        @apply fin-bg-buttons-main-set-informative-color-background-disabled;
        @apply fin-text-buttons-main-set-informative-color-label-disabled;
        @apply fin-cursor-not-allowed;
      }
    }

    &.fin-button-stealth {
      @apply fin-bg-transparent;
      @apply fin-text-buttons-main-set-stealth-color-label-default;
      &:hover,
      &.fin-button-active {
        @apply fin-bg-buttons-main-set-stealth-color-background-hover;
        @apply fin-text-buttons-main-set-stealth-color-label-hover;
      }

      &.fin-button-attention {
        @apply fin-text-buttons-main-set-stealth-color-label-attention;
        &:hover,
        &.fin-button-active {
          @apply fin-text-buttons-main-set-stealth-color-label-attention-hover;
          @apply fin-bg-buttons-main-set-stealth-color-background-attention-hover;
        }
        &:disabled {
          @apply fin-bg-transparent;
        }
      }
      &:disabled {
        @apply fin-bg-transparent;
        @apply fin-text-buttons-main-set-stealth-color-label-disabled;
        @apply fin-cursor-not-allowed;
      }
    }

    &.fin-button-round {
      border-radius: 99999rem;
    }

    &.fin-button-rectangle {
      border-radius: 0.4rem;
    }
  }
}
