import { HttpClientModule } from '@angular/common/http';
import {
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  ContentChild,
  Input,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';
import { FinAngularMaterialModule } from '@fincloud/utils/angular-material';
import { FinButtonAppearance } from '../../enums/fin-button-appearance';
import { FinButtonShape } from '../../enums/fin-button-shape';
import { FinDotsLoaderComponent } from '../dots-loader/dots-loader.component';

/**
 * A clickable element used to perform actions or submit forms.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-button--docs Storybook Reference}
 */
@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: `
    button[fin-button],
    button[fin-button-icon]
  `,
  standalone: true,
  imports: [
    MatIconModule,
    MatButtonModule,
    FinAngularMaterialModule,
    FinDotsLoaderComponent,
    HttpClientModule,
    FinIconModule,
  ],
  templateUrl: './button.component.html',
  styleUrl: './button.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    '[class]': 'buttonCssClasses',
    '[class.fin-button-attention]': 'attention',
  },
})
export class FinButtonComponent {
  @ContentChild('label') protected label: string | null = '';

  /** Size of the button. */
  @Input() size: FinSize.XS | FinSize.S | FinSize.M | FinSize.L | FinSize.XL =
    FinSize.M;
  /** Shape of the button. */
  @Input() shape: FinButtonShape = FinButtonShape.ROUND;
  /** Appearance of the button. */
  @Input() appearance: FinButtonAppearance = FinButtonAppearance.PRIMARY;
  /** Keep the button in an `:hover` state. */
  @Input({ transform: booleanAttribute }) isActive = false;
  /** Highlight the button. */
  @Input({ transform: booleanAttribute }) attention = false;
  /** Activate the button's loading state. */
  @Input({ transform: booleanAttribute }) showLoader = false;
  /** Activate the button's elevation. */
  @Input({ transform: booleanAttribute }) elevation = false;

  protected get buttonCssClasses(): string {
    let activeClass = '';
    let elevationClass = '';

    if (this.isActive) {
      activeClass = 'fin-button-active';
    }

    if (this.elevation) {
      elevationClass = 'fin-shadow-medium';
    }

    return `fin-button fin-button-${this.appearance} fin-button-${this.size} fin-button-${this.shape} ${activeClass} ${elevationClass}`;
  }
}
