import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';
import { Args, Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { FinButtonAppearance } from '../../enums/fin-button-appearance';
import { FinButtonShape } from '../../enums/fin-button-shape';
import { FinButtonComponent } from './button.component';

const meta: Meta<FinButtonComponent> = {
  component: FinButtonComponent,
  title: 'Components/Buttons/Button',
  decorators: [
    moduleMetadata({
      imports: [CommonModule, MatButtonModule, FinIconModule],
    }),
  ],
  parameters: {
    docs: {
      description: {
        component: '`import { FinButtonModule } from "@fincloud/ui/button"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=8919-27031&m=dev',
    },
  },
};
export default meta;

type Story = StoryObj<
  FinButtonComponent & {
    label: string;
    finButtonPrefix: string;
  }
>;

const renderTemplate = (
  args: Args,
  buttonType: string,
  showIcon?: boolean,
  disabled?: boolean,
) => ({
  props: { ...args, showIcon, disabled },
  template: `
    <button
      ${buttonType}
      [size]="size"
      [shape]="shape"
      [appearance]="appearance"
      [showLoader]="showLoader"
      [attention]="attention"
      [isActive]="isActive"
      [disabled]="disabled"
    >
        {{ label }}

        @if(showIcon) {
          <fin-icon [name]="buttonType === 'fin-button' ? 'home' : 'add'"></fin-icon>
        }
    </button>
  `,
});

export const ButtonSample: Story = {
  args: {
    shape: FinButtonShape.ROUND,
    appearance: FinButtonAppearance.PRIMARY,
    size: FinSize.M,
    attention: false,
    showLoader: false,
    label: 'Sample Button',
    isActive: false,
  },
  argTypes: {
    size: {
      control: { type: 'select' },
      options: [FinSize.XS, FinSize.S, FinSize.M, FinSize.L, FinSize.XL],
    },
    shape: {
      control: { type: 'select' },
      options: [...Object.values(FinButtonShape)],
    },
    appearance: {
      control: { type: 'select' },
      options: [...Object.values(FinButtonAppearance)],
    },
    attention: {
      options: [true, false],
      control: { type: 'radio' },
    },
    isActive: {
      options: [true, false],
      control: { type: 'radio' },
    },
    showLoader: {
      options: [true, false],
      control: { type: 'radio' },
    },
  },
  render: (args) => renderTemplate(args, 'fin-button'),
};

export const ButtonPrimaryRectangle: Story = {
  args: {
    ...ButtonSample.args,
    shape: FinButtonShape.RECTANGLE,
    label: 'Rectangle Primary Button',
  },
  argTypes: {
    ...ButtonSample.argTypes,
  },
  render: (args) => renderTemplate(args, 'fin-button'),
};

export const ButtonPrimaryWithIcon: Story = {
  args: {
    ...ButtonSample.args,
    label: 'Primary Button',
  },
  argTypes: {
    ...ButtonSample.argTypes,
  },
  render: (args) => renderTemplate(args, 'fin-button', true),
};

export const ButtonDisabled: Story = {
  args: {
    ...ButtonSample.args,
    label: 'Primary Button Disabled',
  },
  argTypes: {
    ...ButtonSample.argTypes,
  },
  render: (args) => renderTemplate(args, 'fin-button', false, true),
};

export const ButtonPrimaryAttention: Story = {
  args: {
    ...ButtonSample.args,
    label: 'Primary Button Attention',
    attention: true,
  },
  argTypes: {
    ...ButtonSample.argTypes,
  },
  render: (args) => renderTemplate(args, 'fin-button'),
};

export const PrimaryButtonVariants: Story = {
  render: () => ({
    template: `
      <div class="fin-grid fin-grid-cols-3 fin-gap-4 fin-mb-4">
        <button
          fin-button
          [size]="'m'"
          [shape]="'round'"
          [appearance]="'primary'"
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
        <button
          fin-button
          [size]="'m'"
          [shape]="'round'"
          [appearance]="'primary'"
          disabled
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
        <button
          fin-button
          [size]="'m'"
          [shape]="'round'"
          [appearance]="'primary'"
          [attention]="true"
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
      </div>
      <div class="fin-grid fin-grid-cols-3 fin-gap-4">
        <button
          fin-button
          [size]="'m'"
          [shape]="'rectangle'"
          [appearance]="'primary'"
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
        <button
          fin-button
          [size]="'m'"
          [shape]="'rectangle'"
          [appearance]="'primary'"
          disabled
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
        <button
          fin-button
          [size]="'m'"
          [shape]="'rectangle'"
          [appearance]="'primary'"
          [attention]="true"
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
      </div>
    `,
  }),
};

export const SecondaryButtonVariants: Story = {
  render: () => ({
    template: `
      <div class="fin-grid fin-grid-cols-3 fin-gap-4 fin-mb-4">
        <button
          fin-button
          [size]="'m'"
          [shape]="'round'"
          [appearance]="'secondary'"
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
        <button
          fin-button
          [size]="'m'"
          [shape]="'round'"
          [appearance]="'secondary'"
          disabled
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
        <button
          fin-button
          [size]="'m'"
          [shape]="'round'"
          [appearance]="'secondary'"
          [attention]="true"
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
      </div>
      <div class="fin-grid fin-grid-cols-3 fin-gap-4">
        <button
          fin-button
          [size]="'m'"
          [shape]="'rectangle'"
          [appearance]="'secondary'"
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
        <button
          fin-button
          [size]="'m'"
          [shape]="'rectangle'"
          [appearance]="'secondary'"
          disabled
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
        <button
          fin-button
          [size]="'m'"
          [shape]="'rectangle'"
          [appearance]="'secondary'"
          [attention]="true"
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
      </div>
    `,
  }),
};

export const InformativeButtonVariants: Story = {
  render: () => ({
    template: `
      <div class="fin-grid fin-grid-cols-3 fin-gap-4 fin-mb-4">
        <button
          fin-button
          [size]="'m'"
          [shape]="'round'"
          [appearance]="'informative'"
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
        <button
          fin-button
          [size]="'m'"
          [shape]="'round'"
          [appearance]="'informative'"
          disabled
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
        <button
          fin-button
          [size]="'m'"
          [shape]="'round'"
          [appearance]="'informative'"
          [attention]="true"
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
      </div>
      <div class="fin-grid fin-grid-cols-3 fin-gap-4">
        <button
          fin-button
          [size]="'m'"
          [shape]="'rectangle'"
          [appearance]="'informative'"
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
        <button
          fin-button
          [size]="'m'"
          [shape]="'rectangle'"
          [appearance]="'informative'"
          disabled
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
        <button
          fin-button
          [size]="'m'"
          [shape]="'rectangle'"
          [appearance]="'informative'"
          [attention]="true"
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
      </div>
    `,
  }),
};

export const StealthButtonVariants: Story = {
  render: () => ({
    template: `
      <div class="fin-grid fin-grid-cols-3 fin-gap-4 fin-mb-4">
        <button
          fin-button
          [size]="'m'"
          [shape]="'round'"
          [appearance]="'stealth'"
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
        <button
          fin-button
          [size]="'m'"
          [shape]="'round'"
          [appearance]="'stealth'"
          disabled
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
        <button
          fin-button
          [size]="'m'"
          [shape]="'round'"
          [appearance]="'stealth'"
          [attention]="true"
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
      </div>
      <div class="fin-grid fin-grid-cols-3 fin-gap-4">
        <button
          fin-button
          [size]="'m'"
          [shape]="'rectangle'"
          [appearance]="'stealth'"
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
        <button
          fin-button
          [size]="'m'"
          [shape]="'rectangle'"
          [appearance]="'stealth'"
          disabled
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
        <button
          fin-button
          [size]="'m'"
          [shape]="'rectangle'"
          [appearance]="'stealth'"
          [attention]="true"
        >
          <fin-icon name="add"></fin-icon>
          Button
        </button>
      </div>
    `,
  }),
};

export const LoadingButton: Story = {
  render: () => ({
    template: `
      <button fin-button [showLoader]="true">
        Primary Button
      </button>
    `,
  }),
};

export const LoadingButtonVariants: Story = {
  render: () => ({
    template: `
    <div class="fin-grid fin-grid-cols-4 fin-gap-4 fin-mb-4">
      <button
        fin-button
        [showLoader]="true"
        [appearance]="'primary'"
        [shape]="'round'"
      >
        Button
      </button>
      <button
        fin-button
        [showLoader]="true"
        [appearance]="'secondary'"
        [shape]="'round'"
      >
        Button
      </button>
      <button
        fin-button
        [showLoader]="true"
        [appearance]="'informative'"
        [shape]="'round'"
      >
        Button
      </button>
      <button
        fin-button
        [showLoader]="true"
        [appearance]="'stealth'"
        [shape]="'round'"
      >
        Button
      </button>
    </div>
    <div class="fin-grid fin-grid-cols-4 fin-gap-4 fin-mb-4">
      <button
        fin-button
        [showLoader]="true"
        [appearance]="'primary'"
        [shape]="'rectangle'"
      >
        Button
      </button>
      <button
        fin-button
        [showLoader]="true"
        [appearance]="'secondary'"
        [shape]="'rectangle'"
      >
        Button
      </button>
      <button
        fin-button
        [showLoader]="true"
        [appearance]="'informative'"
        [shape]="'rectangle'"
      >
        Button
      </button>
      <button
        fin-button
        [showLoader]="true"
        [appearance]="'stealth'"
        [shape]="'rectangle'"
      >
        Button
      </button>
    </div>
    `,
  }),
};

export const PrimaryButtonIcon: Story = {
  args: {
    ...ButtonSample.args,
    label: '',
  },
  argTypes: {
    ...ButtonSample.argTypes,
  },
  render: (args) => renderTemplate(args, 'fin-button-icon', true),
};

export const PrimaryRectangleButtonIcon: Story = {
  args: {
    ...ButtonSample.args,
    label: '',
    shape: FinButtonShape.RECTANGLE,
  },
  argTypes: {
    ...ButtonSample.argTypes,
  },
  render: (args) => renderTemplate(args, 'fin-button-icon', true),
};

export const PrimaryDisabledButtonIcon: Story = {
  args: {
    ...ButtonSample.args,
    label: '',
  },
  argTypes: {
    ...ButtonSample.argTypes,
  },
  render: (args) => renderTemplate(args, 'fin-button-icon', true, true),
};

export const PrimaryAttentionButtonIcon: Story = {
  args: {
    ...ButtonSample.args,
    label: '',
    attention: true,
  },
  argTypes: {
    ...ButtonSample.argTypes,
  },
  render: (args) => renderTemplate(args, 'fin-button-icon', true),
};

export const PrimaryButtonIconVariants: Story = {
  render: (args) => ({
    props: args,
    template: `
      <div class="fin-grid fin-grid-cols-3 fin-gap-4 fin-mb-4">
        <button fin-button-icon [appearance]="'primary'">
          <fin-icon name="add"></fin-icon>
        </button>
        <button fin-button-icon [appearance]="'primary'" disabled>
          <fin-icon name="add"></fin-icon>
        </button>
        <button fin-button-icon [appearance]="'primary'" [attention]="true">
          <fin-icon name="add"></fin-icon>
        </button>
      </div>
      <div class="fin-grid fin-grid-cols-3 fin-gap-4">
        <button fin-button-icon [shape]="'rectangle'" [appearance]="'primary'">
          <fin-icon name="add"></fin-icon>
        </button>
        <button fin-button-icon [shape]="'rectangle'" [appearance]="'primary'" disabled>
          <fin-icon name="add"></fin-icon>
        </button>
        <button fin-button-icon [shape]="'rectangle'" [appearance]="'primary'" [attention]="true">
          <fin-icon name="add"></fin-icon>
        </button>
      </div>
    `,
  }),
};

export const SecondaryButtonIconVariants: Story = {
  render: (args) => ({
    props: args,
    template: `
      <div class="fin-grid fin-grid-cols-3 fin-gap-4 fin-mb-4">
        <button fin-button-icon [appearance]="'secondary'">
          <fin-icon name="add"></fin-icon>
        </button>
        <button fin-button-icon [appearance]="'secondary'" disabled>
          <fin-icon name="add"></fin-icon>
        </button>
        <button fin-button-icon [appearance]="'secondary'" [attention]="true">
          <fin-icon name="add"></fin-icon>
        </button>
      </div>
      <div class="fin-grid fin-grid-cols-3 fin-gap-4">
        <button fin-button-icon [shape]="'rectangle'" [appearance]="'secondary'">
          <fin-icon name="add"></fin-icon>
        </button>
        <button fin-button-icon [shape]="'rectangle'" [appearance]="'secondary'" disabled>
          <fin-icon name="add"></fin-icon>
        </button>
        <button fin-button-icon [shape]="'rectangle'" [appearance]="'secondary'" [attention]="true">
          <fin-icon name="add"></fin-icon>
        </button>
      </div>
    `,
  }),
};

export const InformativeButtonIconVariants: Story = {
  render: (args) => ({
    props: args,
    template: `
      <div class="fin-grid fin-grid-cols-3 fin-gap-4 fin-mb-4">
        <button fin-button-icon [appearance]="'informative'">
          <fin-icon name="add"></fin-icon>
        </button>
        <button fin-button-icon [appearance]="'informative'" disabled>
          <fin-icon name="add"></fin-icon>
        </button>
        <button fin-button-icon [appearance]="'informative'" [attention]="true">
          <fin-icon name="add"></fin-icon>
        </button>
      </div>
      <div class="fin-grid fin-grid-cols-3 fin-gap-4">
        <button fin-button-icon [shape]="'rectangle'" [appearance]="'informative'">
          <fin-icon name="add"></fin-icon>
        </button>
        <button fin-button-icon [shape]="'rectangle'" [appearance]="'informative'" disabled>
          <fin-icon name="add"></fin-icon>
        </button>
        <button fin-button-icon [shape]="'rectangle'" [appearance]="'informative'" [attention]="true">
          <fin-icon name="add"></fin-icon>
        </button>
      </div>
    `,
  }),
};

export const StealthButtonIconVariants: Story = {
  render: (args) => ({
    props: args,
    template: `
      <div class="fin-grid fin-grid-cols-3 fin-gap-4 fin-mb-4">
        <button fin-button-icon [appearance]="'stealth'">
          <fin-icon name="add"></fin-icon>
        </button>
        <button fin-button-icon [appearance]="'stealth'" disabled>
          <fin-icon name="add"></fin-icon>
        </button>
        <button fin-button-icon [appearance]="'stealth'" [attention]="true">
          <fin-icon name="add"></fin-icon>
        </button>
      </div>
      <div class="fin-grid fin-grid-cols-3 fin-gap-4">
        <button fin-button-icon [shape]="'rectangle'" [appearance]="'stealth'">
          <fin-icon name="add"></fin-icon>
        </button>
        <button fin-button-icon [shape]="'rectangle'" [appearance]="'stealth'" disabled>
          <fin-icon name="add"></fin-icon>
        </button>
        <button fin-button-icon [shape]="'rectangle'" [appearance]="'stealth'" [attention]="true">
          <fin-icon name="add"></fin-icon>
        </button>
      </div>
    `,
  }),
};
