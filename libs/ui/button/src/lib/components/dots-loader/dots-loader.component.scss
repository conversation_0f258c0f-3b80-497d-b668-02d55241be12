@keyframes blink {
  0% {
    opacity: 0.2;
  }

  20% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.fin-dots-container {
  &.fin-dots-loader-xl {
    top: -1.2rem;
  }

  &.fin-dots-loader-l {
    top: -1.2rem;
  }

  &.fin-dots-loader-m {
    top: -1.1rem;
  }

  &.fin-dots-loader-s {
    top: -1rem;
  }

  &.fin-dots-loader-xs {
    top: -0.9rem;
  }

  .fin-dot {
    display: inline-block;
    animation-name: blink;
    animation-duration: 1.4s;
    animation-iteration-count: infinite;
    animation-fill-mode: both;
    &.fin-dots-loader-xl {
      font-size: 4.8rem;
    }

    &.fin-dots-loader-l {
      font-size: 4.5rem;
    }

    &.fin-dots-loader-m {
      font-size: 4.1rem;
    }

    &.fin-dots-loader-s {
      font-size: 3.3rem;
    }

    &.fin-dots-loader-xs {
      font-size: 3rem;
    }
  }

  .dot:nth-child(2) {
    animation-delay: 0.2s;
  }

  .dot:nth-child(3) {
    animation-delay: 0.4s;
  }
}
