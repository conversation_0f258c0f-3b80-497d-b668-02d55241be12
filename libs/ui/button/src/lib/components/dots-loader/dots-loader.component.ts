import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FinSize } from '@fincloud/ui/types';

@Component({
  selector: 'fin-utils-dots-loader',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './dots-loader.component.html',
  styleUrl: './dots-loader.component.scss',
  host: {
    class: 'fin-dots-loader',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinDotsLoaderComponent {
  @Input() size: FinSize.XS | FinSize.S | FinSize.M | FinSize.L | FinSize.XL =
    FinSize.M;
  dots = Array(3).fill(0);
}
