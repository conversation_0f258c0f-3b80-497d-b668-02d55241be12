import { CommonModule } from '@angular/common';
import { FormControl, Validators } from '@angular/forms';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinFieldMessageModule } from '@fincloud/ui/field-message';
import { FinIconModule } from '@fincloud/ui/icon';
import {
  FIN_DATE_MASK,
  FIN_DEFAULT_REGION_ID,
  FIN_LOCALE_ID,
  LocaleId,
} from '@fincloud/ui/input';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinSize } from '@fincloud/ui/types';
import {
  componentWrapperDecorator,
  moduleMetadata,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { FinDatePickerModule } from '../../date-picker.module';
import { FinDatePickerComponent } from './date-picker.component';

const meta: Meta = {
  component: FinDatePickerComponent,
  title: 'Fields/Date Picker',
  decorators: [
    componentWrapperDecorator(
      (story) =>
        `<div class="fin-min-w-[27rem] fin-max-w-[50rem]">        
          ${story}      
        </div>`,
    ),
    moduleMetadata({
      imports: [
        CommonModule,
        FinDatePickerModule,
        FinFieldMessageModule,
        FinIconModule,
        FinTruncateTextModule,
        FinButtonModule,
      ],
      providers: [
        {
          provide: FIN_DATE_MASK,
          useValue: {
            en: {
              dateFormat: 'dd/MM/yyyy',
            },
            de: {
              dateFormat: 'dd.MM.yyyy',
            },
          },
        },
        {
          provide: FIN_DEFAULT_REGION_ID,
          useValue: LocaleId.DE,
        },
        {
          provide: FIN_LOCALE_ID,
          useValue: LocaleId.DE,
        },
      ],
    }),
  ],
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinDatePickerModule } from "@fincloud/ui/date-picker"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=7654-10499&m=dev',
    },
  },
};

export default meta;
type Story = StoryObj<
  FinDatePickerComponent & {
    finFieldLabel: string;
    finFieldSuffix: string;
    finFieldHint: string;
    finFooter: string;
    'isValidDate()': string;
    'minDate()': string;
    'maxDate()': string;
    'disabledDates()': string;
  }
>;

const invalidField = new FormControl('', Validators.required);
invalidField.markAsTouched();

// Functions
function formatDate(date: Date) {
  return new Date(date).toLocaleDateString('en-US');
}

export const Primary: Story = {
  args: {
    label: 'Input label',
    placeholder: 'dd/mm/yyyy',
    size: FinSize.M,
    selectionMode: 'single',
    view: 'date',
    showIcon: true,
    showButtonBar: false,
    minDate: undefined,
    maxDate: undefined,
    disabledDates: [],
  },
  argTypes: {
    size: {
      options: [FinSize.M, FinSize.L],
      control: { type: 'select' },
    },
    selectionMode: {
      options: ['single', 'range'],
      control: { type: 'select' },
    },
    view: {
      options: ['date', 'month', 'year'],
      control: { type: 'select' },
    },
    minDate: {
      control: false,
    },
    maxDate: {
      control: false,
    },
    dynamicErrorSpace: {
      control: false,
    },
    clearDate: {
      control: false,
    },
    inputChange: {
      control: false,
    },
    closeCalendar: {
      control: false,
    },
    selectDate: {
      control: false,
    },
    finFieldLabel: {
      description: 'Place the label with addition icons next to it',
      table: {
        category: 'Templates',
      },
    },
    finFieldSuffix: {
      description: 'Place icons after the date picker.',
      table: {
        category: 'Templates',
      },
    },
    finFieldHint: {
      description: 'Place icons after the date picker.',
      table: {
        category: 'Templates',
      },
    },
    finFooter: {
      table: {
        category: 'Templates',
      },
      description: 'Custom footer template rendered inside the calendar.',
    },

    'isValidDate()': {
      description:
        'Validates that the input is a valid date. Returns an `invalidDate` error.',
      control: false,
      table: {
        category: 'Validators',
      },
    },
    'minDate()': {
      description:
        'Validates that the input is on or after the provided date. Returns a `minDate` error.<br><br>`minDate()` accepts a `Date` or a string in the `yyyy-mm-dd` format.',
      control: false,
      table: {
        category: 'Validators',
      },
    },
    'maxDate()': {
      description:
        'Validates that the input is on or before the provided date. Returns a `maxDate` error.<br><br>`maxDate()` accepts a `Date` or a string in the `yyyy-mm-dd` format.',
      control: false,
      table: {
        category: 'Validators',
      },
    },
    'disabledDates()': {
      description:
        'Validates that the input does not include any disabled dates. Returns a `disabledDate` error.<br><br>`disabledDates()` accepts an array of `Date` objects or strings in the `yyyy-mm-dd` format.',
      control: false,
      table: {
        category: 'Validators',
      },
    },
  },
  render: (args) => {
    return {
      props: { ...args, formControl: new FormControl() },
      template: `
        <fin-date-picker         
          [formControl]="formControl"
          [label]="label"
          [placeholder]="placeholder"
          [size]="size"
          [selectionMode]="selectionMode"
          [view]="view"
          [showIcon]="showIcon"
          [showButtonBar]="showButtonBar"
          [disabledDates]="disabledDates"
        >
        </fin-date-picker>
      `,
    };
  },
};

export const DateRange: Story = {
  args: {
    ...Primary.args,
    selectionMode: 'range',
    showIcon: true,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl(),
      },
      template: `
        <fin-date-picker
          [formControl]="formControl"
          [label]="label"
          [showIcon]="showIcon"
          [selectionMode]="selectionMode"
          placeholder="dd/mm/yyyy - dd/mm/yyyy"
        >
        </fin-date-picker>
      `,
    };
  },
};

export const ValidationSuccess: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },
  render: (args) => {
    const formControl = new FormControl('');
    formControl.markAsTouched();

    return {
      props: {
        ...args,
        formControl,
      },
      template: `
        <fin-date-picker
          [formControl]="formControl"
          [label]="label"
          [showIcon]="showIcon"
          [selectionMode]="selectionMode"
          placeholder="dd/mm/yyyy"
        >
          <fin-field-messages>
            <ng-template finFieldMessage type="success">
              Some success message.
            </ng-template>
          </fin-field-messages>          
        </fin-date-picker>
      `,
    };
  },
};
export const ValidationError: Story = {
  args: {
    ...Primary.args,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: invalidField,
      },
      template: `
        <fin-date-picker
          [formControl]="formControl"
          [label]="label"
          [selectionMode]="selectionMode"
          [showIcon]="showIcon"
          placeholder="dd/mm/yyyy"
        >
          <fin-field-messages>
            <ng-template finFieldMessage type="error" errorKey="required">The field is required</ng-template>
          </fin-field-messages>
        </fin-date-picker>
      `,
    };
  },
};

export const ValidationWarning: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },
  render: (args) => {
    const formControl = new FormControl('', [Validators.requiredTrue]);
    formControl.markAsTouched();
    return {
      props: {
        ...args,
        formControl,
      },
      template: `
        <fin-date-picker
          [formControl]="formControl"
          [label]="label"
          [showIcon]="showIcon"
          [selectionMode]="selectionMode"
          placeholder="dd/mm/yyyy"
        >
          <fin-field-messages>
            <ng-template finFieldMessage type="warning" errorKey="required">
              Some warning message.
            </ng-template>
          </fin-field-messages>          
        </fin-date-picker>
      `,
    };
  },
};
export const MinAndMax: Story = {
  args: {
    ...Primary.args,
    minDate: new Date(),
    maxDate: new Date(new Date().setDate(new Date().getDate() + 3)),
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl(),
        formatDate,
      },
      template: `
        <div>minDate: {{ formatDate(minDate) }}</div>
        <div class="fin-mb-[1rem]">maxDate: {{ formatDate(maxDate) }}</div>

        <fin-date-picker
          [formControl]="formControl"
          [label]="label"
          [selectionMode]="selectionMode"
          [placeholder]="placeholder"
          [minDate]="minDate"
          [maxDate]="maxDate"
          [showIcon]="showIcon"
        >
        </fin-date-picker>
      `,
    };
  },
};

export const DisabledDates: Story = {
  args: {
    ...Primary.args,
    disabledDates: [
      new Date(new Date().setDate(new Date().getDate() + 1)),
      new Date(new Date().setDate(new Date().getDate() + 2)),
    ],
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl(),
        formatDate,
      },
      template: `
        <div class="fin-mb-[1rem]">Disabled dates: tomorrow and the day after tomorrow</div>

        <fin-date-picker
          [formControl]="formControl"
          [label]="label"
          [selectionMode]="selectionMode"
          [placeholder]="placeholder"
          [disabledDates]="disabledDates"
          [showIcon]="showIcon"
        >
        </fin-date-picker>
      `,
    };
  },
};

export const Disabled: Story = {
  args: {
    ...Primary.args,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl({ value: '', disabled: true }),
      },
      template: `
        <fin-date-picker
          [formControl]="formControl"
          [label]="label"
          [showIcon]="showIcon"
          [selectionMode]="selectionMode"
          placeholder="dd/mm/yyyy"
          [showIcon]="showIcon"
        >
        </fin-date-picker>
      `,
    };
  },
};

export const Readonly: Story = {
  args: {
    ...Primary.args,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl(),
        readonly: true,
      },
      template: `
        <fin-date-picker
          [formControl]="formControl"
          [label]="label"
          [showIcon]="showIcon"
          [selectionMode]="selectionMode"
          placeholder="dd/mm/yyyy"
          [readonly]="readonly"
        >
        </fin-date-picker>
      `,
    };
  },
};

export const ViewModes: Story = {
  args: {
    ...Primary.args,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl1: new FormControl(),
        formControl2: new FormControl(),
        formControl3: new FormControl(),
      },
      template: `
      <div class="fin-grid fin-grid-cols-3 fin-gap-[1rem]">
        <fin-date-picker
          [formControl]="formControl1"
          label="Day"
          placeholder="dd/mm/yyyy"
          view="date"
          showIcon
        >
        </fin-date-picker>

        <fin-date-picker
          [formControl]="formControl2"
          label="Month"
          placeholder="mm/yyyy"
          view="month"
          showIcon
        >
        </fin-date-picker>

        <fin-date-picker
          [formControl]="formControl3"
          label="Year"
          placeholder="yyyy"
          view="year"
          showIcon
        >
        </fin-date-picker>
      </div>
      `,
    };
  },
};

export const CustomLabel: Story = {
  args: {
    ...Primary.args,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    return {
      props: { ...args, formControl: new FormControl() },
      template: `
          <fin-date-picker         
            [formControl]="formControl"
            [placeholder]="placeholder"
            [size]="size"
            [selectionMode]="selectionMode"
            [view]="view"
            [showIcon]="showIcon"
            [showButtonBar]="showButtonBar"
            [disabledDates]="disabledDates"
          >
            <ng-container finFieldLabel>
              <label class="fin-w-[19rem]" finTruncateText>
                Label with multiple actions
              </label>
              
              <div class="fin-flex fin-gap-[0.4rem]">
                <button fin-button-action (click)="clickFn()">
                  <fin-icon src="/assets/storybook/input/chat-bubble.svg"></fin-icon>
                </button>

                <button fin-button-action (click)="clickFn()">
                  <fin-icon src="/assets/storybook/input/info.svg"></fin-icon>
                </button>

                <button fin-button-action (click)="clickFn()">
                  <fin-icon src="/assets/storybook/input/visibility-off.svg"></fin-icon>
                </button>
              </div>
            </ng-container>
            <ng-container finFieldSuffix>
              <fin-icon src="/assets/storybook/input/more-horiz.svg"></fin-icon>
            </ng-container>
          </fin-date-picker>
      `,
    };
  },
};

export const Suffix: Story = {
  args: {
    ...Primary.args,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    return {
      props: { ...args, formControl: new FormControl() },
      template: `
          <fin-date-picker         
            [formControl]="formControl"
            [label]="label"
            [placeholder]="placeholder"
            [size]="size"
            [selectionMode]="selectionMode"
            [view]="view"
            [showIcon]="showIcon"
            [showButtonBar]="showButtonBar"
            [disabledDates]="disabledDates"
          >
            <ng-container finFieldSuffix>
              <fin-icon src="/assets/storybook/input/more-horiz.svg"></fin-icon>
            </ng-container>
          </fin-date-picker>
      `,
    };
  },
};
export const Hint: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },

  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl(),
      },
      template: `
         <fin-date-picker         
            [formControl]="formControl"
            [label]="label"
            [placeholder]="placeholder"
            [size]="size"
            [selectionMode]="selectionMode"
            [view]="view"
            [showIcon]="showIcon"
            [showButtonBar]="showButtonBar"
            [disabledDates]="disabledDates"
          >
          <ng-container finFieldHint>
            Some hint text
          </ng-container>
        </fin-date-picker>
      `,
    };
  },
};

export const CustomFooter: Story = {
  args: {
    ...Primary.args,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl(),
      },
      template: `
        <fin-date-picker
          [formControl]="formControl"
          [label]="label"
          [placeholder]="placeholder"
          [showIcon]="showIcon"
        >
          <ng-template #finFooter>
            <button fin-button appearance="stealth">Today</button>
            <button fin-button appearance="stealth">Tomorrow</button>
            <button fin-button appearance="stealth">7 Days</button>
            <button fin-button appearance="stealth">30 Days</button>
          </ng-template>
        </fin-date-picker>
      `,
    };
  },
};
