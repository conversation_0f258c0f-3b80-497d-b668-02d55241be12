p-calendar {
  display: block;
  width: 100%;
}
::ng-deep {
  .p-hidden-accessible {
    display: none;
  }
  .p-calendar {
    display: block;
    @apply fin-text-body-2-strong;
  }
  .p-datepicker-header {
    padding: 1.2rem;
    .p-datepicker {
      &-prev,
      &-next,
      &-year,
      &-month {
        @apply fin-h-[3.2rem];
        @apply fin-rounded-full;
        @apply fin-text-buttons-main-set-stealth-color-label-default;
        &:hover {
          @apply fin-bg-buttons-main-set-stealth-color-background-hover;
        }
      }
      &-prev,
      &-next {
        @apply fin-w-[3.2rem];
      }
      &-year,
      &-month {
        @apply fin-px-[1.6rem];
        @apply fin-m-0;
      }
      &-month {
        @apply fin-me-[0.4rem];
      }
    }
    .p-datepicker-title .p-link {
      @apply fin-text-body-2-strong;
      color: theme('colors.color-text-interactive');
      margin: 0 0.2rem;
    }
  }
  .p-datepicker-calendar-container {
    padding: 0 1.6rem;
  }
  .p-datepicker {
    background-color: theme('colors.color-surface-primary');
    padding: 0.4rem;
    border-radius: 0.8rem;
    box-shadow: 0rem 0.4rem 1rem 0rem rgba(38, 40, 62, 0.16);

    table th > span,
    table td > span {
      display: flex;
      width: 4rem;
      height: 4rem;
      justify-content: center;
      align-items: center;
    }
    table th {
      @apply fin-text-body-2-moderate;
      text-transform: uppercase;
      color: theme('colors.color-text-disabled');
      padding: 0;
    }
    table td > span {
      color: theme('colors.color-text-primary');
      line-height: 0;
      border-radius: 999.9rem;
      &:not(.p-disabled):hover {
        background-color: theme('colors.color-hover-tertiary');
        color: theme('colors.color-text-primary');
      }
    }
    .p-datepicker-today span:not(.p-disabled) {
      background-color: theme('colors.color-surface-dark-strong');
      color: theme('colors.color-text-light');
    }
    td .p-datepicker-current-day:not(.p-disabled) {
      background-color: theme('colors.color-background-tertiary-subtle');
      color: theme('colors.color-text-primary');
    }
    td .p-disabled {
      color: theme('colors.color-text-disabled');
      cursor: not-allowed;
    }
  }
  .p-monthpicker,
  .p-yearpicker {
    > span {
      border-radius: 999.9rem;
      width: 25%;
      height: 4rem;
      margin: 1rem 0;
      &:hover {
        background-color: theme('colors.color-hover-tertiary');
        color: theme('colors.color-text-primary');
      }
      &.p-highlight {
        background-color: theme('colors.color-background-tertiary-subtle');
        color: theme('colors.color-text-primary');
      }
    }
  }
  .p-monthpicker {
    padding: 3.4rem 1.6rem;
  }
  .p-yearpicker {
    padding: 0.3rem 1.6rem;
  }
  .p-datepicker-buttonbar {
    padding: 0.4rem 1.2rem;
    flex-direction: row-reverse;
    .p-button {
      @apply fin-text-body-2-strong;
      @apply fin-h-[3.2rem];
      @apply fin-px-[1.6rem];
      @apply fin-rounded-full;
      @apply fin-text-buttons-main-set-stealth-color-label-default;
      &:hover {
        @apply fin-bg-buttons-main-set-stealth-color-background-hover;
      }
    }
  }

  .fin-date-picker-overlay {
    min-width: 34rem;
    max-width: 42rem;
  }
}
