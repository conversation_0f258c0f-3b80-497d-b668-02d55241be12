import { isValid, parse, startOfDay } from 'date-fns';
import { DATE_FORMAT } from './date-format';

export function stringToDate(date: Date | string): Date | null {
  let currentDate = null;

  if (date instanceof Date) {
    currentDate = date;
  }

  if (date && typeof date === 'string') {
    const parsedDate = parse(date, DATE_FORMAT, new Date());

    if (isValid(parsedDate)) {
      currentDate = parsedDate;
    }
  }

  return currentDate ? startOfDay(currentDate) : currentDate;
}
