import { Injectable } from '@angular/core';
import { format, isDate, isMatch } from 'date-fns';
import { DATE_FORMAT } from '../utils/date-format';

@Injectable({
  providedIn: 'root',
})
export class FinDateAdapterService {
  /**
   * Converts a JavaScript `Date` object into a formatted date string.
   *
   * @param {Date} date - The date object to convert.
   * @returns {string} Formatted date string in `YYYY-MM-DD` format.
   *
   * @example
   * const date = new Date(2025, 6, 12); // July 12, 2025
   * fromModel(date); // Returns "2025-07-12" (with provide date format)
   */
  fromModel(date: Date): string {
    return format(date, DATE_FORMAT);
  }

  /**
   * Converts a date string into a JavaScript `Date` object (if valid), otherwise returns the original string.
   *
   * @param {Date | string} dateString - The date string to parse (e.g., "2025-07-12") or JavaScript `Date` object.
   * @returns {Date | string} A `Date` object if the input is valid, otherwise the original string | Date.
   *
   * @example
   * toModel("2025-07-12"); // Returns Date object for July 12, 2025
   * toModel("invalid-date"); // Returns "invalid-date"
   * toModel(new Date()); // Returns current JavaScript `Date` object.
   */
  toModel(dateString: Date | string): Date | string {
    if (isDate(dateString)) {
      return dateString;
    }

    if (isMatch(dateString, DATE_FORMAT)) {
      return new Date(dateString);
    }

    throw new Error('invalid date');
  }
}
