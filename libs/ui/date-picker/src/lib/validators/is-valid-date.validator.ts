import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { isValid, parse } from 'date-fns';
import { DATE_FORMAT } from '../utils/date-format';

export function isValidDate(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;

    if (!value) {
      return null;
    }

    if (Array.isArray(value)) {
      const areDatesValid = value.every((date) =>
        isValid(parse(date, DATE_FORMAT, new Date())),
      );
      return areDatesValid ? null : { invalidDate: true };
    } else {
      const valueParsed = parse(value, DATE_FORMAT, new Date());
      return isValid(valueParsed) ? null : { invalidDate: true };
    }
  };
}
