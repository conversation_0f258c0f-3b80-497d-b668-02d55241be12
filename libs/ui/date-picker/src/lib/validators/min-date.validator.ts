import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { isBefore, startOfDay } from 'date-fns';
import { stringToDate } from '../utils/string-to-date';

export function minDate(minDate: Date | string): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;

    if (!value) {
      return null;
    }

    if (Array.isArray(value)) {
      const hasInvalidDate = value.some((date) =>
        isBefore(startOfDay(new Date(date)), stringToDate(minDate) as Date),
      );
      return hasInvalidDate ? { minDate: true } : null;
    } else {
      const date = stringToDate(minDate);
      const isUnderMinDate = isBefore(
        startOfDay(new Date(value)),
        date as Date,
      );
      
      return isUnderMinDate ? { minDate: true } : null;
    }
  };
}
