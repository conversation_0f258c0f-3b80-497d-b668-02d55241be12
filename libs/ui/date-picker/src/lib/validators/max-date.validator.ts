import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { isAfter, startOfDay } from 'date-fns';
import { stringToDate } from '../utils/string-to-date';

export function maxDate(maxDate: Date | string): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;

    if (!value) {
      return null;
    }

    if (Array.isArray(value)) {
      const hasInvalidDate = value.some((date) =>
        isAfter(startOfDay(new Date(date)), stringToDate(maxDate) as Date),
      );
      return hasInvalidDate ? { maxDate: true } : null;
    } else {
      const date = stringToDate(maxDate);
      const isOverMaxDate = isAfter(startOfDay(new Date(value)), date as Date);

      return isOverMaxDate ? { maxDate: true } : null;
    }
  };
}
