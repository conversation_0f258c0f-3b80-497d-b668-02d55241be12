import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { isSameDay, isWithinInterval } from 'date-fns';
import { stringToDate } from '../utils/string-to-date';

export function disabledDates(disabledDates: (Date | string)[]): ValidatorFn {
  const parsedDisabledDates = disabledDates.map((date) => stringToDate(date));

  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;

    if (!value) {
      return null;
    }

    if (Array.isArray(value)) {
      const start = new Date(value[0]);
      const end = new Date(value[1]);
      const hasDisabled = parsedDisabledDates.some((date) =>
        isWithinInterval(date as Date, { start, end }),
      );
      return hasDisabled ? { disabledDate: true } : null;
    } else {
      const isDisabled = (date: Date) =>
        parsedDisabledDates.some((disabled) =>
          isSameDay(date, disabled as Date),
        );
      return isDisabled(new Date(value)) ? { disabledDate: true } : null;
    }
  };
}
