import { Inject, Optional, Pipe, PipeTransform } from '@angular/core';
import {
  FIN_DATE_MASK,
  FIN_REGION_LOCALE_ID,
  FinDateMaskConfig,
  LocaleId,
} from '@fincloud/ui/input';
import { format } from 'date-fns';
import { compact, isEmpty, isNil } from 'lodash-es';
import { FinDateAdapterService } from '../services/date-adapter.service';

@Pipe({
  name: 'dateRange',
  standalone: true,
})
export class DateRangePipe implements PipeTransform {
  constructor(
    @Optional()
    @Inject(FIN_REGION_LOCALE_ID)
    private regionalLocaleId: LocaleId,

    @Optional()
    @Inject(FIN_DATE_MASK)
    private finDateMask: Record<LocaleId, FinDateMaskConfig>,
    private finDateAdapterService: FinDateAdapterService,
  ) {}
  transform(value: Date | string | string[] | null): string | null {
    const dateFormat = this.finDateMask[this.regionalLocaleId].dateFormat;

    if (Array.isArray(value) && compact(value).length === 2) {
      const [startValue, endValue] = value.map((dateString) =>
        this.finDateAdapterService.toModel(dateString),
      );
      const startDate = format(startValue, dateFormat);
      const endDate = format(endValue, dateFormat);

      return `${startDate} - ${endDate}`;
    }

    if (
      !Array.isArray(value) &&
      !isNil(value) &&
      (!isEmpty(value) || !isNaN(new Date(value).getDate()))
    ) {
      return format(this.finDateAdapterService.toModel(value), dateFormat);
    }

    return null;
  }
}
