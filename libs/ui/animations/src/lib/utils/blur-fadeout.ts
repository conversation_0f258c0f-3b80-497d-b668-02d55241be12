import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';

// eslint-disable-next-line @fincloud/ns/no-export-constant
export const BLUR_FADEOUT = trigger('blurFadeOut', [
  state(
    'blurred',
    style({
      filter: 'blur(8px)',
      width: '0%',
    }),
  ),
  state(
    'clear',
    style({
      filter: 'blur(0px)',
      width: '100%',
    }),
  ),
  transition('blurred => clear', [animate('2s ease-out')]),
]);
