import { CommonModule } from '@angular/common';
import {
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  Input,
  numberAttribute,
} from '@angular/core';
import { pxToRem } from '@fincloud/utils/functions';

@Component({
  selector: 'fin-border-sweep',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './border-sweep.component.html',
  styleUrl: './border-sweep.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'fin-border-sweep',
  },
})
export class FinBorderSweepComponent {
  /** Whether the animated border sweep effect is enabled. */
  @Input({ transform: booleanAttribute }) enableAnimation = false;

  /** The border radius of the component container, in `px` units. */
  @Input({ transform: pxToRem }) borderRadius = pxToRem(4);

  /** The border width around the component, in `px` units. */
  @Input({ transform: pxToRem }) borderWidth = pxToRem(1);

  /** Delay before the border sweep animation starts, in `seconds`. */
  @Input({ transform: numberAttribute }) animationDelay = 0;

  /** Duration of the border sweep animation cycle, in `seconds`. */
  @Input({ transform: numberAttribute }) animationDuration = 1.5;
}
