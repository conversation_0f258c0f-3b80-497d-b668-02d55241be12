:host {
  display: block;
  width: 100%;

  .fin-border-sweep {
    &-container {
      height: 100%;
    }

    &-animation-on {
      position: relative;
      overflow: hidden;
      z-index: 0;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        background: linear-gradient(
          to right,
          #060fd0,
          #03086a,
          #8e1ccc,
          #556bff,
          #198aff,
          #7587ff,
          #0022fc,
          #8e1ccc
        );
        mask-image: linear-gradient(
          to right,
          rgba(0, 0, 0, 1) 0%,
          rgba(0, 0, 0, 0.75) 25%,
          rgba(0, 0, 0, 0.5) 50%,
          rgba(0, 0, 0, 0.25) 75%,
          rgba(0, 0, 0, 0.1) 100%
        );
        mask-size: 0% 100%;
        mask-repeat: no-repeat;
        animation: borderSweep var(--animation-duration) ease-in-out infinite
          alternate;
        animation-delay: var(--animation-delay);
      }
    }
  }
}

@keyframes borderSweep {
  from {
    mask-size: 0% 100%;
  }
  to {
    mask-size: 100% 100%;
  }
}
