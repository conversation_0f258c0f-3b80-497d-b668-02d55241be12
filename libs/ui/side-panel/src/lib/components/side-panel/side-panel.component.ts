import { CommonModule } from '@angular/common';
import {
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  ContentChild,
  EventEmitter,
  Input,
  Output,
  ViewChild,
} from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { Mat<PERSON>idenav, MatSidenavModule } from '@angular/material/sidenav';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { pxToRem } from '@fincloud/utils/functions';
import { FinSidePanelContentDirective } from '../../directives/fin-side-panel-content.directive';
import { FinSidePanelSlideDirective } from '../../directives/fin-side-panel-slide.directive';
import { FinSidePanelMod } from '../../enums/fin-side-panel-mod';
import { FinSidePanelPosition } from '../../enums/fin-side-panel-position';

/**
 * A customizable side panel component designed for presenting dynamic content and navigation options.
 *
 * For detailed documentation visit
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-side-panel--docs https://lib-ui.neoshare.dev/?path=/docs/components-side-panel--docs}.
 */

@Component({
  selector: 'fin-side-panel',
  standalone: true,
  imports: [
    CommonModule,
    MatSidenavModule,
    MatIconModule,
    MatTooltipModule,
    RouterModule,
  ],
  templateUrl: './side-panel.component.html',
  styleUrl: './side-panel.component.scss',
  host: {
    class: 'fin-side-panel',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinSidePanelComponent {
  /** Whether the side panel is opened. */
  @Input({ transform: booleanAttribute }) open = true;

  /** Enables partially open mode. The value specifies the collapsed size in `px` units. */
  @Input({ transform: pxToRem }) partiallyOpenSize = 0;

  /** Mode of the side panel. */
  @Input() mode = FinSidePanelMod.SIDE;

  /** The side from which the side panel will appear. */
  @Input() position = FinSidePanelPosition.START;

  /** Option to add a box shadow to the `finSidePanelSlide`. */
  @Input({ transform: booleanAttribute }) elevation = false;

  /** Whether the container should have a backdrop while the side panel is open. */
  @Input({ transform: booleanAttribute }) hasBackdrop = false;

  /** Determines whether the backdrop should be transparent when the side panel is open. */
  @Input({ transform: booleanAttribute }) transparentBackdrop = false;

  /** Whether the side panel can be closed with the escape key or by clicking on the backdrop.<br/><span class="fin-text-red-500">The escape key functionality for closing is not working in Storybook.</span> */
  @Input({ transform: booleanAttribute }) disableClose = true;

  /** Whether the side panel is fixed in the viewport.<br/><span class="fin-text-red-500">The simulation in Storybook is not very accurate, but it should work fine in actual use.</span> */
  @Input({ transform: booleanAttribute }) fixedInViewport = true;

  /** Can be used when sticky content inside the side panel is not working properly. */
  @Input({ transform: booleanAttribute }) fixSticky = false;

  /** Event emitted when the side panel open state is changed. */
  @Output() isOpen = new EventEmitter<boolean>();

  @ViewChild('sidePanel') private sidePanel!: MatSidenav;

  @ContentChild(FinSidePanelSlideDirective)
  protected finSidePanelSlide!: FinSidePanelSlideDirective;

  @ContentChild(FinSidePanelContentDirective)
  protected finSidePanelContent!: FinSidePanelContentDirective;

  protected positions = FinSidePanelPosition;

  get opened(): boolean {
    return this.sidePanel?.opened;
  }

  /** Toggles the visibility of the side panel. */
  toggleSidePanel() {
    this.sidePanel.toggle();
  }

  protected openedChange(isOpen: boolean) {
    this.isOpen.emit(isOpen);
  }
}
