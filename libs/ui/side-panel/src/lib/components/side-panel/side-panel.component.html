<mat-sidenav-container
  class="fin-side-panel fin-h-full fin-bg-transparent"
  [ngClass]="{
    'fin-side-panel-transparent-backdrop': transparentBackdrop,
    'fin-side-fix-sticky': fixSticky,
  }"
  [hasBackdrop]="hasBackdrop"
  autosize
>
  <mat-sidenav
    #sidePanel
    class="fin-border-0"
    [ngClass]="{
      'fin-shadow-none': !elevation,
      'fin-shadow-medium': elevation,
    }"
    [opened]="open"
    [mode]="mode"
    [position]="position"
    [disableClose]="disableClose"
    [fixedInViewport]="fixedInViewport"
    [class.partially-open]="!!partiallyOpenSize && !sidePanel.opened"
    [style.width]="
      !!partiallyOpenSize && !sidePanel.opened ? partiallyOpenSize : 'unset'
    "
    (openedChange)="openedChange($event)"
  >
    <ng-container
      [ngTemplateOutlet]="finSidePanelSlide.template"
    ></ng-container>
  </mat-sidenav>

  <mat-sidenav-content>
    <div
      class="fin-h-full"
      [style.marginLeft]="
        !!partiallyOpenSize && !sidePanel.opened && position === positions.START
          ? partiallyOpenSize
          : 0
      "
      [style.marginRight]="
        !!partiallyOpenSize && !sidePanel.opened && position === positions.END
          ? partiallyOpenSize
          : 0
      "
    >
      <ng-container
        [ngTemplateOutlet]="finSidePanelContent.template"
      ></ng-container>
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>
