:host::ng-deep {
  --mat-sidenav-scrim-color: theme('colors.color-transparency-dark-bold');

  &.fin-side-fix-sticky {
    &.mat-sidenav-container,
    .mat-sidenav-content,
    .mat-drawer,
    .mat-drawer-inner-container {
      overflow: clip;
    }
  }

  .fin-side-panel-transparent-backdrop {
    .mat-drawer-backdrop.mat-drawer-shown {
      --mat-sidenav-scrim-color: transparent;
    }
  }

  .mat-drawer-backdrop.mat-drawer-shown {
    position: fixed;
  }
  .mat-drawer-inner-container {
    overflow: hidden;
  }

  .partially-open {
    visibility: visible !important;
    transform: none !important;
  }
  .partially-open.mat-drawer[style*='visibility: hidden'] {
    display: block !important;
  }
  .partial-margin-left {
    margin-left: var(--partially-open-size, 0px);
  }
  .partial-margin-right {
    margin-right: var(--partially-open-size, 0px);
  }

  .mat-drawer-transition .mat-drawer-content {
    transition: none !important;
  }
}
