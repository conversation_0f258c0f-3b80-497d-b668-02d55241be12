import { CommonModule } from '@angular/common';
import { ElementRef } from '@angular/core';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinMenuItemModule } from '@fincloud/ui/menu-item';
import { FinTooltipModule } from '@fincloud/ui/tooltip';
import {
  componentWrapperDecorator,
  moduleMetadata,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { FinSidePanelContentDirective } from '../../directives/fin-side-panel-content.directive';
import { FinSidePanelSlideDirective } from '../../directives/fin-side-panel-slide.directive';
import { FinSidePanelMod } from '../../enums/fin-side-panel-mod';
import { FinSidePanelPosition } from '../../enums/fin-side-panel-position';
import { FinSidePanelModule } from '../../side-panel.module';
import { FinSidePanelComponent } from './side-panel.component';

const meta: Meta<FinSidePanelComponent> = {
  component: FinSidePanelComponent,
  title: 'Components/Side Panel',
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        FinSidePanelModule,
        FinSidePanelContentDirective,
        FinSidePanelSlideDirective,
        FinButtonModule,
        FinIconModule,
        FinTooltipModule,
        FinMenuItemModule,
      ],
    }),
    componentWrapperDecorator(
      (story) =>
        `<div class="fin-w-[700px] fin-h-[300px] fin-border fin-border-gray">
          <!-- Do not copy <style>...</style>, storybook workaround -->
          <style>
            ::ng-deep .css-1qq744x .innerZoomElementWrapper > * {
              border: 0 !important;
            }
          </style>

          ${story}
        </div>`,
    ),
  ],
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinSidePanelModule } from "@fincloud/ui/side-panel"`',
      },
      design: {
        type: 'figma',
        url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6212-940&t=f4pqckYkgUv2n9Y3-4',
      },
    },
  },
};
export default meta;
type Story = StoryObj<
  FinSidePanelComponent & {
    finSidePanelSlide: ElementRef<unknown>;
    finSidePanelContent: ElementRef<unknown>;
  }
>;

export const Default: Story = {
  args: {
    open: true,
    partiallyOpenSize: 0,
    mode: FinSidePanelMod.SIDE,
    position: FinSidePanelPosition.START,
    elevation: false,
    hasBackdrop: false,
    transparentBackdrop: false,
    disableClose: true,
    fixedInViewport: false,
  },
  argTypes: {
    open: {
      control: { type: 'boolean' },
    },
    partiallyOpenSize: {
      control: { type: 'number' },
    },
    mode: {
      options: Object.values(FinSidePanelMod),
      control: { type: 'select' },
    },
    position: {
      options: Object.values(FinSidePanelPosition),
      control: { type: 'select' },
    },
    hasBackdrop: {
      control: { type: 'boolean' },
    },
    disableClose: {
      control: { type: 'boolean' },
    },
    fixedInViewport: {
      control: { type: 'boolean' },
    },
    isOpen: {
      control: { type: undefined },
    },
    finSidePanelSlide: {
      defaultValue: { summary: 'attribute' },
      table: {
        category: 'Attributes',
      },
    },
    finSidePanelContent: {
      defaultValue: { summary: 'attribute' },
      table: {
        category: 'Attributes',
      },
    },
    toggleSidePanel: {
      control: false,
    },
  },
  render: (args) => ({
    props: args,
    template: `
      <fin-side-panel
        #sidePanel 
        [open]="open"
        [partiallyOpenSize]="partiallyOpenSize"
        [mode]="mode"
        [position]="position"
        [elevation]="elevation"
        [hasBackdrop]="hasBackdrop"
        [transparentBackdrop]="transparentBackdrop"
        [disableClose]="disableClose"
        [fixedInViewport]="fixedInViewport"
      >
        <ng-template finSidePanelSlide>
          <div class="fin-h-full fin-p-[1rem]">
            Slide details
          </div>
        </ng-template>

        <ng-template finSidePanelContent>
          <div class="fin-h-full fin-p-[1rem] fin-bg-color-surface-secondary">
            <div>
              Content details.
            </div>
            <div>
              Is side panel open: <span class="fin-font-bold">{{ sidePanel.opened }}</span>
            </div>

            <button fin-button (click)="sidePanel.toggleSidePanel()" size="s">
              Toggle side panel
            </button>
          </div>     
        </ng-template>
      </fin-side-panel>
    `,
  }),
};

export const PartiallyOpen: Story = {
  args: {
    ...Default.args,
    partiallyOpenSize: 60,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: args,
    template: `
      <fin-side-panel
        #sidePanel 
        [open]="open"
        [partiallyOpenSize]="partiallyOpenSize"
        [mode]="mode"
        [position]="position"
        [hasBackdrop]="hasBackdrop"
        [disableClose]="disableClose"
        [fixedInViewport]="fixedInViewport"
      >
        <ng-template finSidePanelSlide>
          <div class="fin-h-full fin-p-[1rem]">
            Slide details
          </div>
        </ng-template>

        <ng-template finSidePanelContent>
          <div class="fin-h-full fin-p-[1rem] fin-bg-color-surface-secondary">
            <div>
              Content details.
            </div>
            <div>
              Is side panel open: <span class="fin-font-bold">{{ sidePanel.opened }}</span>
            </div>

            <button fin-button (click)="sidePanel.toggleSidePanel()" size="s">
              Toggle side panel
            </button>
          </div>     
        </ng-template>
      </fin-side-panel>
    `,
  }),
};

export const CustomExample: Story = {
  args: {
    ...Default.args,
    partiallyOpenSize: 80,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => {
    const menuItems = [
      {
        iconName: 'dashboard',
        title: 'Dashboard',
        active: false,
      },
      {
        iconName: 'list_alt',
        title: 'Cases',
        active: true,
      },
    ];

    return {
      props: { ...args, menuItems },
      template: `
        <fin-side-panel
          #sidePanel 
          [open]="open"
          [partiallyOpenSize]="partiallyOpenSize"
          [mode]="mode"
          [position]="position"
          [hasBackdrop]="hasBackdrop"
          [disableClose]="disableClose"
          [fixedInViewport]="fixedInViewport"
        >
          <ng-template finSidePanelSlide>
            <div 
              class="fin-h-full fin-px-size-spacing-12 fin-pt-size-spacing-24 fin-pb-size-spacing-32 fin-flex fin-flex-col fin-gap-size-spacing-24"
              [class.fin-w-[24rem]]="sidePanel.opened"
            >
              <div class="fin-flex-grow fin-flex fin-flex-col fin-gap-size-spacing-16">
                @for (menuItem of menuItems; track $index) {
                  <a
                    fin-menu-item
                    [compact]="!sidePanel.opened"
                    [iconName]="menuItem.iconName"
                    [active]="menuItem.active"
                    finTooltip
                    [showArrow]="true"
                    placement="right"
                    [content]="!sidePanel.opened ? menuItem.title : ''"
                    [openDelay]="300"
                  >
                    <ng-container finMenuItemTitle>
                    {{ menuItem.title }}
                    </ng-container>
                  </a>  
                }                 
              </div>
  
              <button 
                class="fin-justify-self-start fin-mx-size-spacing-12" 
                fin-button-icon 
                appearance="informative" 
                (click)="sidePanel.toggleSidePanel()">
                <fin-icon [name]="sidePanel.opened ? 'keyboard_arrow_left' : 'keyboard_arrow_right'"></fin-icon>
              </button>
            </div>
          </ng-template>
  
          <ng-template finSidePanelContent>
            <div class="fin-h-full fin-p-[1rem] fin-bg-color-surface-secondary">
              router-outlet...
            </div>     
          </ng-template>
        </fin-side-panel>
      `,
    };
  },
};
