<label
  class="fin-label fin-flex fin-justify-between fin-items-center"
  [ngClass]="{
    'fin-text-color-text-disabled': control.disabled,
    'fin-text-color-text-primary': control.enabled,
  }"
  [for]="control"
>
  @if (label) {
    <span class="fin-w-full" finTruncateText> {{ label }} </span>
  } @else {
    <ng-content select="[finInputLabel]"></ng-content>
  }
</label>
<mat-form-field
  class=" fin-field fin-field-size-{{ size }} fin-block"
  [ngClass]="{
    'fin-field-warning':
      (externalFieldMessages?.type || (getMessage$ | async)?.type) ===
      'warning',
    'fin-field-success':
      (externalFieldMessages?.type || (getMessage$ | async)?.type) ===
      'success',
  }"
  [subscriptSizing]="dynamicErrorSpace"
  (click)="openInputPopup(fileInput)"
>
  <input
    matInput
    type="text"
    [readonly]="control.enabled"
    [placeholder]="placeholder"
    [formControl]="control"
    (blur)="blur($event)"
  />
  <div matSuffix class="fin-suffix fin-pl-size-spacing-8">
    <fin-icon
      [size]="size"
      name="upload"
      class="fin-text-color-icons-primary"
    ></fin-icon>

    <input
      type="file"
      hidden
      #fileInput
      (change)="selectFileToUpload(fileInput.files)"
    />
  </div>

  <mat-hint class="fin-w-full">
    @if (
      control.valid && control.touched && (getMessage$ | async);
      as message
    ) {
      @if (message.type === 'success') {
        <ng-container *ngTemplateOutlet="message.template"></ng-container>
      }
    } @else {
      <div class="fin-hint">
        <ng-content select="[finInputHint]"></ng-content>
      </div>
    }
  </mat-hint>

  <mat-error>
    <div class="fin-inline-block fin-w-full">
      @if ((getMessage$ | async)?.template; as messageTemplate) {
        <ng-container *ngTemplateOutlet="messageTemplate"></ng-container>
      }
    </div>
  </mat-error>
</mat-form-field>
