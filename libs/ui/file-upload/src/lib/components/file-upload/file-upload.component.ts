import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  EventEmitter,
  forwardRef,
  Injector,
  Input,
  Optional,
  Output,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import {
  FinFieldMessageBodyDirective,
  FinFieldMessageService,
} from '@fincloud/ui/field-message';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinErrorSpace, FinSize } from '@fincloud/ui/types';
import {
  FIN_MAT_FORM_FIELD_DEFAULT_OPTIONS,
  FinAngularMaterialModule,
} from '@fincloud/utils/angular-material';
import { FinControlValueAccessor } from '@fincloud/utils/control-value-accessor';
import { FinFieldService } from '@fincloud/utils/services';
import { shareReplay, startWith } from 'rxjs';

/**
 * An icon that trigger selection of file to upload.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-file-upload--docs Storybook Reference}
 */

@Component({
  selector: 'fin-file-upload',
  standalone: true,
  imports: [
    CommonModule,
    FinIconModule,
    ReactiveFormsModule,
    FinAngularMaterialModule,
    MatInputModule,
    FinIconModule,
  ],
  templateUrl: './file-upload.component.html',
  styleUrl: './file-upload.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FinFileUploadComponent),
      multi: true,
    },
    FIN_MAT_FORM_FIELD_DEFAULT_OPTIONS,
    FinFieldService,
    FinFieldMessageService,
  ],
  host: {
    class: 'fin-file-upload',
  },
})
// implements AfterViewInit
export class FinFileUploadComponent
  extends FinControlValueAccessor
  implements AfterViewInit
{
  /** Label for the input field. */
  @Input() label = '';

  /** The placeholder for this control. */
  @Input() placeholder = '';

  /** FinSize of the input. It should be from type FinSize. Default is Medium (FinSize.Medium). */
  @Input() size: FinSize.M | FinSize.L = FinSize.M;

  /** Whether the form field should reserve space for error. */
  @Input() dynamicErrorSpace!: FinErrorSpace;

  /** <span style="color:red">**Deprecated**</span><br>Switch readonly mode. */
  @Input({ transform: booleanAttribute }) readonly = false;

  /** To be used only with external messages */
  @Input() externalFieldMessages: FinFieldMessageBodyDirective | null = null;

  /** Event emitted when file is selected. */
  @Output() selectFile = new EventEmitter<FileList | null>();

  protected sizes = FinSize;

  constructor(
    private finFieldService: FinFieldService,
    private destroyRef: DestroyRef,
    @Optional() private finFieldMessageService: FinFieldMessageService,
    injector: Injector,
  ) {
    super(injector);
  }

  /** Returns the active validation message. */
  protected getMessage$ = this.finFieldMessageService?.getMessage$.pipe(
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  ngAfterViewInit(): void {
    this.control.statusChanges
      .pipe(startWith(this.control.errors), takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.finFieldService.controlErrors$.next(this.control.errors);
      });
  }

  protected selectFileToUpload(files: FileList | null): void {
    const fileName = files?.item(0)?.name;
    this.control.patchValue(fileName);
    this.selectFile.emit(files);
  }

  protected openInputPopup(fileInput: HTMLElement) {
    if (this.control.enabled) {
      fileInput.click();
    }
  }
}
