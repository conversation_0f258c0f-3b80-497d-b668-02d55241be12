import { CommonModule } from '@angular/common';
import { FormControl, Validators } from '@angular/forms';
import { FinFieldMessageModule } from '@fincloud/ui/field-message';
import { FinSize } from '@fincloud/ui/types';
import { moduleMetadata, type Meta, type StoryObj } from '@storybook/angular';
import { FinFileUploadComponent } from './file-upload.component';

const meta: Meta<FinFileUploadComponent> = {
  component: FinFileUploadComponent,
  title: 'Fields/File Upload',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinFileUploadModule } from "@fincloud/ui/file-upload"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=31742-1180305&m=dev',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [CommonModule, FinFieldMessageModule],
    }),
  ],
};

export default meta;
type Story = StoryObj<
  FinFileUploadComponent & {
    finInputLabel: string;
    finInputHint: string;
    formControl: FormControl;
  }
>;

export const Primary: Story = {
  args: {
    label: 'File upload label',
    placeholder: 'Placeholder',
    size: FinSize.M,
    dynamicErrorSpace: 'dynamic',
  },
  argTypes: {
    label: {
      control: { type: 'text' },
    },
    placeholder: {
      control: { type: 'text' },
    },
    readonly: {
      control: 'boolean',
    },
    size: {
      options: [FinSize.M, FinSize.L],
      control: { type: 'select' },
    },
    dynamicErrorSpace: {
      options: ['dynamic', 'fixed'],
      control: { type: 'select' },
    },
    finInputLabel: {
      description: 'Place the label with addition icons next to it',
      table: {
        category: 'Templates',
      },
    },
    finInputHint: {
      description: 'Place for a hint text',
      table: {
        category: 'Templates',
      },
    },
    selectFile: {
      control: false,
      type: 'function',
    },
  },

  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl(''),
      },
      template: `
        <fin-file-upload
          [label]="label"
          [placeholder]=[placeholder]
          [size]=[size]
          [dynamicErrorSpace]="dynamicErrorSpace"
          [formControl]="formControl"
          (selectFile)="selectFile($event)"
        ></fin-file-upload>        
      `,
    };
  },
};

export const ErrorMessageField: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl('', Validators.requiredTrue),
      },
      template: `
        <fin-file-upload
          [label]="label"
          [placeholder]=[placeholder]
          [dynamicErrorSpace]="dynamicErrorSpace"
          [formControl]="formControl"
          (selectFile)="selectFile($event)"
        >
          <fin-field-messages>
            <ng-template finFieldMessage type="error" errorKey="minlength"
              >The field should be longer than 10</ng-template
            >
            <ng-template finFieldMessage type="error" errorKey="required"
              >This field is required</ng-template
            >

            <ng-template finFieldMessage type="success">Success</ng-template>
          </fin-field-messages>
        </fin-file-upload>        
      `,
    };
  },
};

export const ValidationSuccess: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },
  render: (args) => {
    const formControl = new FormControl('');
    formControl.markAsTouched();

    return {
      props: {
        ...args,
        formControl,
      },
      template: `
        <fin-file-upload
          [label]="label"
          [dynamicErrorSpace]="dynamicErrorSpace"
          [formControl]="formControl"
        >
          <fin-field-messages>
            <ng-template finFieldMessage type="success">
              Some success message.
            </ng-template>
          </fin-field-messages>          
        </fin-file-upload>
      `,
    };
  },
};

export const ValidationError: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },
  render: (args) => {
    const formControl = new FormControl('', [Validators.required]);
    formControl.markAsTouched();

    return {
      props: {
        ...args,
        formControl,
      },
      template: `
        <fin-file-upload
          [label]="label"
          [dynamicErrorSpace]="dynamicErrorSpace"
          [formControl]="formControl"
        >
          <fin-field-messages>
            <ng-template finFieldMessage type="error" errorKey="required">
              Some error message.
            </ng-template>
          </fin-field-messages>          
        </fin-file-upload>
      `,
    };
  },
};

export const ValidationWarning: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },
  render: (args) => {
    const formControl = new FormControl('', [Validators.requiredTrue]);
    formControl.markAsTouched();
    return {
      props: {
        ...args,
        formControl,
      },
      template: `
        <fin-file-upload
          [label]="label"
          [dynamicErrorSpace]="dynamicErrorSpace"
          [formControl]="formControl"
        >
          <fin-field-messages>
            <ng-template finFieldMessage type="warning" errorKey="required">
              Some warning message.
            </ng-template>
          </fin-field-messages>          
        </fin-file-upload>
      `,
    };
  },
};

export const Disabled: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },
  render: (args) => {
    const formControl = new FormControl({ value: 5, disabled: true });
    return {
      props: {
        ...args,
        formControl,
      },
      template: `
        <fin-file-upload
          [label]="label"
          [formControl]="formControl"
        >
          <fin-field-messages>
            <ng-template finFieldMessage type="warning" errorKey="required">
              Some warning message.
            </ng-template>
          </fin-field-messages>          
        </fin-file-upload>
      `,
    };
  },
};

export const Hint: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },
  render: (args) => {
    const formControl = new FormControl();
    return {
      props: {
        ...args,
        formControl,
      },
      template: `
        <fin-file-upload
          [label]="label"
          [formControl]="formControl"
        >
          <ng-container finInputHint>
            Some hint text
          </ng-container>
        </fin-file-upload>
      `,
    };
  },
};
