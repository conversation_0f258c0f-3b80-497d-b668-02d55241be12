import { ConnectedPosition } from '@angular/cdk/overlay';
import { HORIZONTAL_FALLBACKS } from './horizontal-fallbacks';
import { VERTICAL_FALLBACKS } from './vertical-fallbacks';

export const POSITION_STRATEGIES: Record<
  'top' | 'bottom' | 'left' | 'right',
  ConnectedPosition[]
> = {
  top: [
    {
      originX: 'center',
      originY: 'top',
      overlayX: 'center',
      overlayY: 'bottom',
      offsetY: -8,
    },
    {
      originX: 'center',
      originY: 'bottom',
      overlayX: 'center',
      overlayY: 'top',
      offsetY: 8,
    },
    ...HORIZONTAL_FALLBACKS,
  ],
  bottom: [
    {
      originX: 'center',
      originY: 'bottom',
      overlayX: 'center',
      overlayY: 'top',
      offsetY: 8,
    },
    {
      originX: 'center',
      originY: 'top',
      overlayX: 'center',
      overlayY: 'bottom',
      offsetY: -8,
    },
    ...HORIZONTAL_FALLBACKS,
  ],
  left: [
    {
      originX: 'start',
      originY: 'center',
      overlayX: 'end',
      overlayY: 'center',
      offsetX: -8,
    },
    {
      originX: 'end',
      originY: 'center',
      overlayX: 'start',
      overlayY: 'center',
      offsetX: 8,
    },
    ...VERTICAL_FALLBACKS,
  ],
  right: [
    {
      originX: 'end',
      originY: 'center',
      overlayX: 'start',
      overlayY: 'center',
      offsetX: 8,
    },
    {
      originX: 'start',
      originY: 'center',
      overlayX: 'end',
      overlayY: 'center',
      offsetX: -8,
    },
    ...VERTICAL_FALLBACKS,
  ],
};
