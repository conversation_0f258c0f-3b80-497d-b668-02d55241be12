import {
  ConnectedPosition,
  FlexibleConnectedPositionStrategy,
  Overlay,
  OverlayRef,
} from '@angular/cdk/overlay';
import {
  DestroyRef,
  Directive,
  ElementRef,
  Input,
  OnChanges,
  Renderer2,
  SimpleChanges,
  TemplateRef,
  ViewContainerRef,
  booleanAttribute,
  numberAttribute,
} from '@angular/core';
import {
  BehaviorSubject,
  Observable,
  distinctUntilChanged,
  filter,
  fromEvent,
  map,
  merge,
  scan,
  shareReplay,
  switchMap,
  take,
  tap,
  timer,
} from 'rxjs';

import { ComponentPortal } from '@angular/cdk/portal';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { orderBy } from 'lodash-es';
import { FinTooltipComponent } from '../components/tooltip/tooltip.component';
import { TooltipState } from '../models/tooltip-state';
import { POSITION_STRATEGIES } from '../utils/positions';

@Directive({
  selector: '[finTooltip]',
  standalone: true,
  exportAs: 'finTooltip',
})
export class FinTooltipDirective implements OnChanges {
  /** The tooltip content: string, raw HTML, or a TemplateRef  */
  @Input() content!: TemplateRef<unknown> | string | null | undefined;

  /** Optional context object supplied when `content` is a `TemplateRef` */
  @Input() context: Record<string, unknown> | null = null;

  /** When set, overrides tooltipPosition  */
  @Input() placement: keyof typeof POSITION_STRATEGIES = 'top';

  /** Delay (ms) before showing tooltip on hover/focus  */
  @Input({ transform: numberAttribute }) openDelay = 250;

  /** Delay (ms) before hiding tooltip on mouse leave/blurs  */
  @Input({ transform: numberAttribute }) closeDelay = 0;

  /** If true, tooltip opens immediately on init and stays visible until disabled  */
  @Input({ transform: booleanAttribute }) setAlwaysVisible = false;

  /** If true, tooltip is disabled and never shows  */
  @Input({ transform: booleanAttribute }) disableTooltip = false;

  /** If true, tooltip is disabled and never shows  */
  @Input({ transform: booleanAttribute }) showArrow = false;

  /** Maximum width (px) for the tooltip bubble  */
  @Input({ transform: numberAttribute }) maxWidth: number | null = null;

  /** CSS selector by ID for an element to anchor the tooltip to (instead of the host)  */
  @Input() positionTarget: string | null = null;

  private positionStrategy$: FlexibleConnectedPositionStrategy = this.overlay
    .position()
    .flexibleConnectedTo(this.hostElementRef.nativeElement);

  private positionStrategyChanges$: Observable<{
    placement: keyof typeof POSITION_STRATEGIES;
  }> = this.positionStrategy$.positionChanges.pipe(
    map((event) => ({
      placement: this.mapConnectionToPlacement(event.connectionPair),
    })),
  );

  private overlayReference$: OverlayRef = this.overlay.create({
    positionStrategy: this.positionStrategy$.withPositions(
      POSITION_STRATEGIES[this.placement],
    ),
    scrollStrategy: this.overlay.scrollStrategies.reposition(),
  });

  private state: TooltipState = {
    overlayReference: this.overlayReference$,
    componentPortal: new ComponentPortal(
      FinTooltipComponent,
      this.viewContainerRef,
    ),
    componentReference: null,
    positionStrategy: this.positionStrategy$.withPositions(
      POSITION_STRATEGIES[this.placement],
    ),
    cachedContainerElement: null,
    mouseInside: false,
    focus: false,
    isVisible: false,

    closeDelay: this.closeDelay,
    content: this.content,
    maxWidth: this.maxWidth,
    openDelay: this.openDelay,
    placement: this.placement,
    positionTarget: this.positionTarget,
    setAlwaysVisible: this.setAlwaysVisible,
    showArrow: this.showArrow,
    disableTooltip: this.disableTooltip,
    context: this.context,
  };

  private programmaticEvent$$ = new BehaviorSubject<Partial<TooltipState>>({});

  //Event sourcing
  private mouseEnter$: Observable<Event> = fromEvent(
    this.hostElementRef.nativeElement,
    'mouseenter',
  );

  private mouseLeave$: Observable<Event> = fromEvent(
    this.hostElementRef.nativeElement,
    'mouseleave',
  );

  private focusIn$: Observable<Event> = fromEvent(
    this.hostElementRef.nativeElement,
    'focusin',
  );

  private focusOut$: Observable<Event> = fromEvent(
    this.hostElementRef.nativeElement,
    'focusout',
  );

  private visibility$ = this.createVisibilityStream().pipe(
    distinctUntilChanged(),
  );

  /**
   * This stream merges mouse and focus events, emitting objects that indicate whether the mouse
   * is inside the tooltip trigger element or whether the element is focused.
   */
  private nativeEvents$: Observable<Partial<TooltipState>> = merge(
    this.mouseEnter$.pipe(map(() => ({ mouseInside: true }))),
    this.mouseLeave$.pipe(map(() => ({ mouseInside: false }))),
    this.focusIn$.pipe(map(() => ({ focus: true }))),
    this.focusOut$.pipe(map(() => ({ focus: false }))),
    this.visibility$,
  );

  /**
   * This observable merges:
   * - `programmaticEvent$$`: Events triggered programmatically to control the tooltip.
   * - `positionStrategyChanges$`: Events emitted when the tooltip's position strategy changes.
   */
  private externalEvents$ = merge(
    this.programmaticEvent$$,
    this.positionStrategyChanges$,
  );

  private state$: Observable<TooltipState> = merge(
    this.nativeEvents$,
    this.externalEvents$,
  ).pipe(
    scan((state, command) => {
      return { ...state, ...command };
    }, this.state),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  constructor(
    private overlay: Overlay,
    private hostElementRef: ElementRef<HTMLElement>,
    private viewContainerRef: ViewContainerRef,
    private destroyRef: DestroyRef,
    private renderer: Renderer2,
  ) {
    this.onStateChanges().subscribe();
  }

  ngOnChanges(changes: SimpleChanges): void {
    const state = this.extractLastChanges(changes);

    this.updateState(state);
  }

  updateState(state: Partial<TooltipState>) {
    if (state.placement) {
      this.overlayReference$.updatePositionStrategy(
        this.positionStrategy$.withPositions(
          POSITION_STRATEGIES[state.placement],
        ),
      );
    }

    this.programmaticEvent$$.next(state);
  }

  /**
   * Opens or reopens the tooltip overlay. Creates overlay if not exists, or updates position strategy.
   */
  private openTooltip(state: Partial<TooltipState>): void {
    // Show the tooltip when its actual position is calculated
    state.overlayReference?.removePanelClass('fin-opacity-0');

    // If the overlay and component are already attached, just update their classes and content
    if (
      state.overlayReference &&
      state.componentReference &&
      state.overlayReference.hasAttached()
    ) {
      if (state.showArrow) {
        // Remove any previous tooltip position classes
        state.overlayReference.removePanelClass([
          'tooltip-bottom',
          'tooltip-top',
          'tooltip-left',
          'tooltip-right',
        ]);

        // Add the current placement class for styling
        state.overlayReference.addPanelClass('tooltip-' + state.placement);
      }

      // Update the tooltip content
      state.componentReference.setInput('content', state.content);
      state.componentReference.setInput('context', state.context);
      return;
    }

    // Attach the tooltip component to the overlay if not already attached
    const componentReference = state.overlayReference?.attach(
      state.componentPortal,
    );

    // If a custom position target is specified, update the overlay's position strategy
    if (state.positionTarget) {
      const el = this.resolveOriginElement();
      const positionStrategy = this.overlay.position().flexibleConnectedTo(el);
      positionStrategy.withPositions(
        POSITION_STRATEGIES[state.placement || 'top'],
      );
      state.overlayReference?.updatePositionStrategy(positionStrategy);
      state.overlayReference?.updatePosition();
    }

    // Set the tooltip content and placement inputs on the component
    if (componentReference) {
      componentReference.setInput('content', state.content);
      componentReference.setInput('position', state.placement);
      componentReference.setInput('context', state.context);
      componentReference.setInput('showArrow', state.showArrow);
      // Store the component reference in the state for future updates
      this.programmaticEvent$$.next({ componentReference });
    }
  }

  private closeTooltip(state: Partial<TooltipState>): void {
    if (state.overlayReference && state.overlayReference.hasAttached()) {
      state.overlayReference.detach();
    }
  }

  /**
   * Returns the element to anchor the tooltip to: either positionTarget or the host
   */
  private resolveOriginElement() {
    if (this.positionTarget) {
      const element = document.getElementById(this.positionTarget);
      if (element) {
        return element || this.hostElementRef.nativeElement;
      }
    }
    return this.hostElementRef.nativeElement;
  }

  /**
   * Translates a CDK ConnectedPosition into human-readable placement
   */
  private mapConnectionToPlacement(
    connectionPair: ConnectedPosition,
  ): 'top' | 'right' | 'bottom' | 'left' {
    if (connectionPair.overlayY === 'bottom') {
      return 'top';
    }
    if (connectionPair.overlayY === 'top') {
      return 'bottom';
    }
    if (connectionPair.overlayX === 'end') {
      return 'left';
    }
    return 'right';
  }

  private onStateChanges(): Observable<TooltipState> {
    return this.state$.pipe(
      filter((state) => !state.disableTooltip && !!state.content),
      switchMap((state) => {
        if (state.mouseInside || (state.setAlwaysVisible && state.isVisible)) {
          // Hide the tooltip until its actual position is calculated
          state.overlayReference?.addPanelClass('fin-opacity-0');

          return timer(state.openDelay).pipe(
            take(1),
            map(() => state),
          );
        }
        return timer(state.closeDelay).pipe(
          take(1),
          map(() => state),
        );
      }),
      tap((state) => {
        if (state.mouseInside || (state.setAlwaysVisible && state.isVisible)) {
          this.openTooltip(state);
        } else {
          this.closeTooltip(state);
        }
      }),
      takeUntilDestroyed(this.destroyRef),
    );
  }

  private extractLastChanges(changes: SimpleChanges): Partial<TooltipState> {
    return Object.entries(changes).reduce((acc, change) => {
      const [key, inputValue] = change;
      return {
        ...acc,
        [key as keyof TooltipState]: inputValue.currentValue,
      };
    }, {} as Partial<TooltipState>);
  }

  private createVisibilityStream(): Observable<Partial<TooltipState>> {
    return new Observable<Partial<TooltipState>>((observer) => {
      const visibilityObserver = new IntersectionObserver(
        (entries) => {
          const orderedEntries = orderBy(entries || [], 'time', 'desc');
          const isVisible = orderedEntries[0]?.isIntersecting;
          observer.next({ isVisible });
        },
        {
          root: this.hostElementRef.nativeElement.parentElement,
          threshold: 0.1,
        },
      );

      visibilityObserver.observe(this.hostElementRef.nativeElement);

      return () => {
        visibilityObserver.disconnect();
      };
    });
  }
}
