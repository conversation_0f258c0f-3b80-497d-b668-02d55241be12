import { Directive, Input, On<PERSON><PERSON>roy, OnInit, Renderer2 } from '@angular/core';
import { NgbTooltip, NgbTooltipConfig } from '@ng-bootstrap/ng-bootstrap';
import { FinTooltipConfigService } from '../services/fin-tooltip-config.service';

/**
 * A small pop-up box that provides additional information about an element.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/directives-tooltip--docs Storybook Reference}
 */
@Directive({
  selector: '[finTooltip]',
  standalone: true,
  hostDirectives: [
    {
      directive: NgbTooltip,
      inputs: [
        'placement',
        'ngbTooltip: content',
        'tooltipClass',
        'tooltipContext: context',
        'openDelay',
        'closeDelay',
        'disableTooltip',
        'positionTarget',
      ],
    },
  ],

  providers: [
    {
      provide: NgbTooltipConfig,
      useClass: FinTooltipConfigService,
    },
  ],
})
export class FinTooltipDirective implements OnInit, On<PERSON><PERSON>roy {
  @Input()
  set showArrow(showArrow: boolean) {
    if (showArrow) {
      this.ngbTooltip.tooltipClass = 'fin-tooltip fin-tooltip-arrow';
    }
  }

  @Input()
  set container(container: string) {
    this.ngbTooltip.container = container;
  }

  @Input()
  set setAlwaysVisible(alwaysVisible: boolean) {
    if (alwaysVisible) {
      this.ngbTooltip.triggers = 'manual';
      this.ngbTooltip.autoClose = false;
    }
    this._alwaysVisible = alwaysVisible;
  }

  @Input() maxWidth?: number;

  private _alwaysVisible = false;
  private scrollHandler = this.onScroll.bind(this);

  constructor(
    private ngbTooltip: NgbTooltip,
    private renderer: Renderer2,
  ) {}

  ngOnInit(): void {
    if (this._alwaysVisible) {
      this.ngbTooltip.open();
    }
    if (this.maxWidth) {
      const width = `${this.maxWidth}rem`;
      const element = document.querySelector('.tooltip-inner') as HTMLElement;
      if (element) {
        this.renderer.setStyle(element, 'max-width', width);
      }
    }

    document.addEventListener(
      'scroll',
      () => {
        this.scrollHandler();
      },
      { capture: true, passive: true },
    );
  }

  private onScroll(): void {
    if (this.ngbTooltip.isOpen() && !this._alwaysVisible) {
      this.ngbTooltip.close();
    }
  }

  ngOnDestroy(): void {
    document.removeEventListener('scroll', this.scrollHandler);
  }
}
