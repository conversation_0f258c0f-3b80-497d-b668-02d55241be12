import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EmbeddedViewRef,
  Input,
  numberAttribute,
  TemplateRef,
} from '@angular/core';
import { POSITION_STRATEGIES } from '../../utils/positions';

@Component({
  selector: 'fin-tooltip',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './tooltip.component.html',
  styleUrl: './tooltip.component.scss',
  host: {
    '[class.fin-tooltip]': '!!content',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinTooltipComponent {
  /** string or TemplateRef */
  @Input() content!: string | TemplateRef<unknown>;
  @Input() context!: EmbeddedViewRef<unknown>;
  @Input() showArrow = false;

  /** current side: 'top' | 'right' | 'bottom' | 'left' */
  @Input() position: keyof typeof POSITION_STRATEGIES = 'top';

  /** Optional px limit injected by directive */
  @Input({ transform: numberAttribute }) maxWidth: number | null = null;

  isTemplate(
    value: string | TemplateRef<unknown>,
  ): value is TemplateRef<unknown> {
    return value instanceof TemplateRef;
  }
}
