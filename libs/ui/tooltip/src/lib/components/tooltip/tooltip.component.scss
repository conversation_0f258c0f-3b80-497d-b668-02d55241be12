:host.fin-tooltip {
  @apply fin-text-body-3-moderate;
  @apply fin-relative;
  @apply fin-bg-color-background-tertiary-strong;
  @apply fin-text-color-text-light;
  @apply fin-p-[0.8rem];
  @apply fin-rounded-[0.4rem];
  @apply fin-max-w-[24rem];
  @apply fin-pointer-events-none;
}

.tooltip-arrow {
  @apply fin-absolute;
  @apply fin-size-0;
}

/* ▸ arrow variants */
::ng-deep {
  .tooltip {
    &-top .tooltip-arrow {
      @apply fin-bottom-[-0.6rem];
      @apply fin-left-1/2;
      @apply fin--translate-x-1/2;
      border-left: 0.6rem solid transparent;
      border-right: 0.6rem solid transparent;
      border-top: 0.6rem solid theme('colors.color-background-tertiary-strong');
    }
    &-bottom .tooltip-arrow {
      @apply fin-top-[-0.6rem];
      @apply fin-left-1/2;
      @apply fin--translate-x-1/2;
      border-left: 0.6rem solid transparent;
      border-right: 0.6rem solid transparent;
      border-bottom: 0.6rem solid
        theme('colors.color-background-tertiary-strong');
    }
    &-left .tooltip-arrow {
      @apply fin-right-[-0.6rem];
      @apply fin-top-1/2;
      @apply fin--translate-y-1/2;
      border-top: 0.6rem solid transparent;
      border-bottom: 0.6rem solid transparent;
      border-left: 0.6rem solid theme('colors.color-background-tertiary-strong');
    }
    &-right .tooltip-arrow {
      @apply fin-left-[-0.6rem];
      @apply fin-top-1/2;
      @apply fin--translate-y-1/2;
      border-top: 0.6rem solid transparent;
      border-bottom: 0.6rem solid transparent;
      border-right: 0.6rem solid
        theme('colors.color-background-tertiary-strong');
    }
  }
}
