import { FormControl } from '@angular/forms';
import { FinSize } from '@fincloud/ui/types';
import type { Meta, StoryObj } from '@storybook/angular';
import { FinSlideTogglePosition } from '../enums/fin-slide-toggle-position';
import { FinSlideToggleType } from '../enums/fin-slide-toggle-type';
import { FinSlideToggleComponent } from './slide-toggle.component';

const meta: Meta<FinSlideToggleComponent> = {
  component: FinSlideToggleComponent,
  title: 'Fields/Slide Toggle',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinSlideToggleModule } from "@fincloud/ui/slide-toggle"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6547-17250&t=c1ay6COSvII1GNBD-4',
    },
  },
};
export default meta;

type Story = StoryObj<FinSlideToggleComponent & { onClick: () => void }>;

export const SlideToggle: Story = {
  args: {
    label: 'label',
    size: FinSize.M,
    type: FinSlideToggleType.PRIMARY,
  },
  argTypes: {
    label: {
      control: 'text',
    },
    labelPosition: {
      control: { type: 'select' },
      options: Object.values(FinSlideTogglePosition),
    },
    size: {
      control: { type: 'select' },
      options: [FinSize.S, FinSize.M],
    },
    type: {
      control: { type: 'select' },
      options: [FinSlideToggleType.PRIMARY, FinSlideToggleType.SECONDARY],
    },
    onClick: {
      action: 'clicked',
      table: {
        disable: true,
      },
    },
  },
  render: (args) => ({
    props: { ...args, formControl: new FormControl(true) },
  }),
};

export const Disabled: Story = {
  argTypes: {
    ...SlideToggle.argTypes,
  },
  args: {
    ...SlideToggle.args,
  },
  render: (args) => ({
    props: {
      ...args,
      formControl: new FormControl({ value: true, disabled: true }),
    },
  }),
  parameters: {
    ...SlideToggle.parameters,
  },
};

export const SmallSize: Story = {
  argTypes: {
    ...SlideToggle.argTypes,
  },
  args: {
    ...SlideToggle.args,
    size: FinSize.S,
  },
  render: SlideToggle.render,
  parameters: {
    ...SlideToggle.parameters,
  },
};

export const SpaceBetween: Story = {
  argTypes: {
    ...SlideToggle.argTypes,
  },
  args: {
    ...SlideToggle.args,
  },
  render: (args) => ({
    props: {
      ...args,
      formControl1: new FormControl(false),
      formControl2: new FormControl(false),
      formControl3: new FormControl(false),
    },
    template: `
      <div class="fin-flex fin-flex-col fin-w-[20rem]">
        <fin-slide-toggle
          class="fin-block"
          [formControl]="formControl1"
          label="Compact view"
        ></fin-slide-toggle>

        <fin-slide-toggle
          class="fin-block"
          [formControl]="formControl2"
          label="Expand 2nd level"
        ></fin-slide-toggle>

        <fin-slide-toggle
          class="fin-block"
          [formControl]="formControl3"
          label="Expand 3rd level"
        ></fin-slide-toggle>
      </div>
    `,
  }),
};
