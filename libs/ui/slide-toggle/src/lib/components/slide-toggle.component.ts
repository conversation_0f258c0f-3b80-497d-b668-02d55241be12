import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  booleanAttribute,
  forwardRef,
} from '@angular/core';
import { NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import {
  MatSlideToggleChange,
  MatSlideToggleModule,
} from '@angular/material/slide-toggle';
import { FinSize } from '@fincloud/ui/types';
import { FinAngularMaterialModule } from '@fincloud/utils/angular-material';
import { FinControlValueAccessor } from '@fincloud/utils/control-value-accessor';
import { FinSlideTogglePosition } from '../enums/fin-slide-toggle-position';
import { FinSlideToggleType } from '../enums/fin-slide-toggle-type';

/**
 * A switch-like component that toggles between two states.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-slide-toggle--docs Storybook Reference}
 */
@Component({
  selector: 'fin-slide-toggle',
  standalone: true,
  imports: [
    CommonModule,
    MatSlideToggleModule,
    ReactiveFormsModule,
    FinAngularMaterialModule,
  ],
  templateUrl: './slide-toggle.component.html',
  styleUrl: './slide-toggle.component.scss',
  host: {
    class: 'fin-slide-toggle',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FinSlideToggleComponent),
      multi: true,
    },
  ],
})
export class FinSlideToggleComponent extends FinControlValueAccessor {
  /** Label of the slide toggle. */
  @Input() label = '';

  /** Position of the label. */
  @Input() labelPosition: FinSlideTogglePosition =
    FinSlideTogglePosition.BEFORE;

  /** Type of the slide toggle. */
  @Input() type: FinSlideToggleType = FinSlideToggleType.PRIMARY;

  /** Size of the slide toggle. */
  @Input() size: FinSize.S | FinSize.M = FinSize.M;

  /** The slide toggle value is updated only programmatically. */
  @Input() isManuallyUpdated = false;
  @Input() class = '';

  /** Whether the checkbox is checked. */
  @Input({ transform: booleanAttribute }) checked!: boolean;

  /** Disables the slide toggle. Can be used when no form control is provided. */
  @Input({ transform: booleanAttribute }) disabled = false;

  /** Event emitted when the slide toggle value changes. */
  @Output() slideChange = new EventEmitter<boolean>();

  protected positions = FinSlideTogglePosition;
  protected sizes = FinSize;

  protected onChange(event: MatSlideToggleChange) {
    if (this.isManuallyUpdated) {
      event.source.checked = !event.source.checked;
    }

    this.slideChange.emit(event.source.checked);
  }
}
