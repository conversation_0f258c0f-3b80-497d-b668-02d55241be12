<div class="fin-zoom-bar-wrapper">
  <div class="fin-zoom-bar-inner fin-flex fin-items-center fin-gap-[0.8rem]">
    <button
      class="fin-zoom-bar-control"
      fin-button-icon
      [appearance]="appearance.INFORMATIVE"
      [size]="size.XS"
      [shape]="shape.RECTANGLE"
      type="button"
      (click)="decrease()"
      [disabled]="control.disabled || control.value === min"
    >
      <fin-icon name="remove"></fin-icon>
    </button>

    <mat-slider
      class="fin-flex-grow fin-mx-0"
      [min]="min"
      [max]="max"
      [step]="step"
      [disabled]="control.disabled"
    >
      <input
        [formControl]="control"
        matSliderThumb
        (dragStart)="onDragStart($event)"
        (dragEnd)="onDragEnd($event)"
      />
    </mat-slider>

    <button
      class="fin-zoom-bar-control"
      fin-button-icon
      [appearance]="appearance.INFORMATIVE"
      [size]="size.XS"
      [shape]="shape.RECTANGLE"
      type="button"
      (click)="increase()"
      [disabled]="control.disabled || control.value === max"
    >
      <fin-icon name="add"></fin-icon>
    </button>
  </div>
</div>
