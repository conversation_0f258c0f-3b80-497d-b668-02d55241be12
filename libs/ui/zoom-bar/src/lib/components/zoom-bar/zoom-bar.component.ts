import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  forwardRef,
  Injector,
  Input,
  numberAttribute,
  Output,
} from '@angular/core';
import { NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { MatSliderDragEvent, MatSliderModule } from '@angular/material/slider';
import {
  FinButtonAppearance,
  FinButtonModule,
  FinButtonShape,
} from '@fincloud/ui/button';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';
import { FinControlValueAccessor } from '@fincloud/utils/control-value-accessor';
import { pxToRem } from '@fincloud/utils/functions';
import { FinZoomBarOrientation } from '../../enums/fin-zoom-bar-orientation';

/**
 * A component for adjusting the zoom level of visual content.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-zoom-bar--docs Storybook Reference}
 */

@Component({
  selector: 'fin-zoom-bar',
  standalone: true,
  imports: [
    CommonModule,
    MatSliderModule,
    ReactiveFormsModule,
    FinButtonModule,
    FinIconModule,
  ],
  templateUrl: './zoom-bar.component.html',
  styleUrl: './zoom-bar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    '[class]': 'hostCssClasses',
    '[style]': 'hostStyles',
  },
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FinZoomBarComponent),
      multi: true,
    },
  ],
})
export class FinZoomBarComponent extends FinControlValueAccessor {
  /** Sets the zoom bar's direction. */
  @Input() orientation: FinZoomBarOrientation =
    FinZoomBarOrientation.HORIZONTAL;

  /** The minimum value that the zoom bar can have. */
  @Input({ transform: numberAttribute }) min = 0;

  /** The maximum value that the zoom bar can have. */
  @Input({ transform: numberAttribute }) max = 100;

  /** The values at which the thumb will snap. */
  @Input({ transform: numberAttribute }) step = 1;

  /** The bar's width or height depending on the orientation. */
  @Input({ transform: numberAttribute }) zoomBarLength = 160;

  /** Event emitted when the zoom bar thumb starts being dragged. */
  @Output() dragStart = new EventEmitter<number>();

  /** Event emitted when the zoom bar thumb stops being dragged. */
  @Output() dragEnd = new EventEmitter<number>();

  protected shape = FinButtonShape;
  protected size = FinSize;
  protected appearance = FinButtonAppearance;

  constructor(injector: Injector) {
    super(injector);
  }

  protected get hostStyles(): string {
    return '--fin-zoom-bar-length:' + pxToRem(this.zoomBarLength);
  }

  protected get hostCssClasses() {
    return `fin-zoom-bar fin-zoom-bar-${this.orientation}`;
  }

  protected onDragStart({ value }: MatSliderDragEvent) {
    this.dragStart.emit(value);
  }

  protected onDragEnd({ value }: MatSliderDragEvent) {
    this.dragEnd.emit(value);
  }

  protected decrease() {
    this.control.setValue(this.control.value - this.step);
  }

  protected increase() {
    this.control.setValue(this.control.value + this.step);
  }
}
