import { CommonModule } from '@angular/common';
import { FormControl } from '@angular/forms';
import { FinContainerDirective } from '@fincloud/ui/container';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSeparatorsModule } from '@fincloud/ui/separators';
import { moduleMetadata, type Meta, type StoryObj } from '@storybook/angular';
import { FinZoomBarOrientation } from '../../enums/fin-zoom-bar-orientation';
import { FinZoomBarModule } from '../../zoom-bar.module';
import { FinZoomBarComponent } from './zoom-bar.component';

const meta: Meta = {
  component: FinZoomBarComponent,
  title: 'Components/Zoom Bar',
  parameters: {
    docs: {
      description: {
        component: '`import { FinZoomBarModule } from "@fincloud/ui/zoom-bar"`',
      },
    },
    design: {
      type: 'figma',
      url: 'TODO',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        FinZoomBarModule,
        FinIconModule,
        FinContainerDirective,
        FinSeparatorsModule,
      ],
    }),
  ],
};
export default meta;
type Story = StoryObj<FinZoomBarComponent>;

export const Default: Story = {
  name: 'Horizontal',
  args: {
    orientation: FinZoomBarOrientation.HORIZONTAL,
    min: 0,
    max: 100,
    step: 1,
  },
  argTypes: {
    orientation: {
      control: { type: 'select' },
      options: Object.values(FinZoomBarOrientation),
    },
    dragStart: {
      control: false,
    },
    dragEnd: {
      control: false,
    },
  },
  render: (args) => ({
    props: {
      ...args,
      formControl: new FormControl(50),
    },
  }),
};

export const Vertical: Story = {
  args: {
    ...Default.args,
    orientation: FinZoomBarOrientation.VERTICAL,
  },
  argTypes: {
    ...Default.argTypes,
  },

  render: (args) => ({
    props: {
      ...args,
      formControl: new FormControl(50),
    },
  }),
};

export const HorizontalCustomSize: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: {
      ...args,
      formControl: new FormControl(50),
    },
    template: `
      <fin-zoom-bar
        [zoomBarLength]="300"
        [formControl]="formControl"
      >
      </fin-zoom-bar>
    `,
  }),
};

export const VerticalCustomSize: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: {
      ...args,
      formControl: new FormControl(50),
    },
    template: `
      <fin-zoom-bar
        [zoomBarLength]="300"
        orientation="vertical"
        [formControl]="formControl"
      >
      </fin-zoom-bar>
    `,
  }),
};

export const InContainer: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: {
      ...args,
      formControl: new FormControl(50),
    },
    template: `
      <div finContainer class="fin-px-[1.6rem] fin-py-[0.8rem]">
        <fin-zoom-bar     
          [formControl]="formControl"
        >
        </fin-zoom-bar>
      </div>
    `,
  }),
};

export const CustomExample: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: {
      ...args,
      formControl: new FormControl(50),
    },
    template: `
      <div finContainer [borderRadius]="false" class="fin-rounded-tl-[0.8rem] fin-px-[1.6rem] fin-py-[0.8rem]">
        <div class="fin-py-[1rem]">
          Custom content
        </div>

        <div class="fin-flex fin-items-center fin-gap-[0.8rem]">
          <fin-icon name="map"></fin-icon>

          <div class="fin-h-[1.4rem]">
            <div finVerticalSeparator type="subtle"></div>
          </div>

          <fin-zoom-bar [formControl]="formControl"> </fin-zoom-bar>
        </div>
      </div>
    `,
  }),
};
