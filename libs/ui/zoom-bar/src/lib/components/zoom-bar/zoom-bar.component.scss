:host {
  display: block;

  &.fin-zoom-bar {
    &-horizontal {
      min-width: 16rem;
      width: var(--fin-zoom-bar-length);
    }

    &-vertical {
      height: var(--fin-zoom-bar-length);
      .fin-zoom-bar {
        &-wrapper {
          width: 2.4rem;
        }
        &-inner {
          min-width: 16rem;
          width: var(--fin-zoom-bar-length);
          transform: rotate(90deg);
          transform-origin: left top;
          position: relative;
          left: 2.4rem;
          top: 0;
          flex-direction: row-reverse;
          .fin-zoom-bar-control:first-of-type {
            transform: rotate(90deg);
          }
        }
      }
    }
  }

  ::ng-deep {
    .mat-mdc-slider {
      --mdc-slider-inactive-track-color: theme(
        'colors.color-background-dark-minimal'
      );
      --mdc-slider-active-track-color: theme(
        'colors.color-background-dark-minimal'
      );
      --mdc-slider-disabled-inactive-track-color: theme(
        'colors.color-background-dark-minimal'
      );
      --mdc-slider-disabled-active-track-color: theme(
        'colors.color-background-dark-minimal'
      );

      --mdc-slider-handle-width: 1.6rem;
      --mdc-slider-handle-height: 2.4rem;
      --mdc-slider-handle-elevation: none;
      --mdc-slider-handle-shape: 0.5rem;
      --mdc-slider-disabled-handle-color: theme(
        'colors.color-background-neutral-minimal'
      );
      --mdc-slider-handle-color: theme(
        'colors.color-background-neutral-minimal'
      );
      --mdc-slider-focus-handle-color: theme(
        'colors.color-background-neutral-minimal'
      );

      min-width: auto;
      height: 2.4rem;
      &.mdc-slider--disabled {
        opacity: 1;
        .mdc-slider {
          &__track--inactive {
            opacity: 1;
          }
          &__input {
            cursor: not-allowed;
          }
        }
      }

      .mdc-slider {
        &__track--inactive,
        &__value-indicator {
          opacity: 1;
        }
        &__thumb {
          height: 2.4rem;
          &-knob {
            border: 0.2rem solid theme('colors.color-border-default-primary');
            border-radius: 0.4rem;
            position: relative;
            &::after {
              content: '';
              width: 0.2rem;
              height: 1.4rem;
              border-radius: 0.4rem;
              background-color: theme('colors.color-border-default-primary');
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
            }
          }
          &--with-indicator {
            .mdc-slider__thumb-knob {
              border-color: theme('colors.color-border-default-strong');
              &::after {
                background-color: theme('colors.color-border-default-strong');
              }
            }
          }
        }
        &__input {
          padding: 0 !important;
          width: 100% !important;
          left: 0 !important;
          height: 2.4rem;
        }
      }

      .mat-ripple-element {
        display: none;
      }
    }
  }
}
