import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { FinActionsMenuModule } from '@fincloud/ui/actions-menu';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinMenuItemModule } from '@fincloud/ui/menu-item';
import { FinSize } from '@fincloud/ui/types';
import { FinAngularMaterialModule } from '@fincloud/utils/angular-material';
import { FinSplitButtonType } from '../enums/fin-split-button-type';
import { FinSplitButtonOption } from '../models/fin-split-button-option';

/**
 * A button with a primary action and additional options in a dropdown.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-split-button--docs Storybook Reference}
 */
@Component({
  selector: 'fin-split-button',
  standalone: true,
  imports: [
    CommonModule,
    MatMenuModule,
    MatButtonModule,
    FinAngularMaterialModule,
    FinIconModule,
    FinMenuItemModule,
    FinActionsMenuModule,
  ],
  templateUrl: './split-button.component.html',
  styleUrl: './split-button.component.scss',
  host: {
    class: 'fin-split-button',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinSplitButtonComponent {
  /** Defines the text displayed on the button. */
  @Input() label = '';

  /** Specifies the size of the button. */
  @Input() size: FinSize.M | FinSize.L = FinSize.M;

  /** Specifies the type of the button. */
  @Input() type: FinSplitButtonType = FinSplitButtonType.PRIMARY;

  /** Determines if the button is disabled. */
  @Input() disabled = false;

  /** All of the defined button options. */
  @Input() options: FinSplitButtonOption[] = [];

  /** Event emitted when the button is clicked. */
  @Output() buttonClick = new EventEmitter<void>();

  /** Event emitted when an option is clicked. */
  @Output() optionClick = new EventEmitter<string>();

  protected isMenuOpened = false;
  protected sizes = FinSize;

  protected onMainButtonClick(): void {
    this.buttonClick.emit();
  }

  protected onOptionButtonClick(optionButton: FinSplitButtonOption): void {
    this.optionClick.emit(optionButton.value);
  }

  protected toggleMenu(): void {
    this.isMenuOpened = !this.isMenuOpened;
  }

  protected trackByLabel(_: number, option: FinSplitButtonOption): string {
    return option.label;
  }
}
