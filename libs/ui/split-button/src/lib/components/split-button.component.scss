.fin-split-button-primary {
  .fin-main-button,
  .fin-dropdown-toggle {
    background-color: theme('colors.color-background-primary-strong');
    color: theme('colors.color-text-primary');

    &:hover {
      background-color: theme('colors.color-hover-primary');
    }

    &:disabled {
      color: theme('colors.color-text-disabled');
      background-color: theme('colors.color-background-disabled');
      cursor: not-allowed;

      ~ .fin-divider-container {
        background-color: theme('colors.color-background-disabled');

        .fin-divider {
          background-color: theme('colors.color-border-default-inactive');
        }
      }
    }
  }

  .fin-divider-container {
    background-color: theme('colors.color-background-primary-strong');

    .fin-divider {
      background-color: theme('colors.color-hover-primary');
    }
  }

  .fin-button-container:hover
    .fin-main-button:not(:disabled)
    + .fin-divider-container {
    background-color: theme('colors.color-hover-primary');
  }
}

.fin-split-button-secondary {
  .fin-main-button,
  .fin-dropdown-toggle {
    background-color: theme('colors.white');
    color: theme('colors.color-text-interactive');
    border: 1px solid theme('colors.color-text-interactive');
    &:first-child {
      border-right: none;
    }
    &:last-child {
      border-left: none;
    }
    &:hover:not(:disabled) {
      background-color: theme('colors.color-background-secondary-minimal');
    }
    &:disabled {
      border: 1px solid theme('colors.color-border-default-inactive');
      color: theme('colors.color-text-disabled');
      cursor: not-allowed;
      &:first-child {
        border-right: none;
      }
      &:last-child {
        border-left: none;
      }
      ~ .fin-divider-container {
        border-top: 1px solid theme('colors.color-border-default-inactive');
        border-bottom: 1px solid theme('colors.color-border-default-inactive');

        .fin-divider {
          background-color: theme('colors.color-border-default-inactive');
        }
      }
    }
  }

  .fin-divider-container {
    background-color: theme('colors.white');
    border-top: 1px solid theme('colors.color-text-interactive');
    border-bottom: 1px solid theme('colors.color-text-interactive');

    .fin-divider {
      background-color: theme('colors.color-text-interactive');
    }
  }
}

.fin-main-button {
  border-top-left-radius: 2.5rem;
  border-bottom-left-radius: 2.5rem;
}

.fin-dropdown-toggle {
  border-top-right-radius: 2.5rem;
  border-bottom-right-radius: 2.5rem;
}

.fin-split-button-l {
  height: 4rem;

  .fin-main-button {
    @apply fin-text-body-1-moderate;
    padding: 0.8rem 1.2rem 0.8rem 2.4rem;
  }

  .fin-dropdown-toggle {
    padding: 0.8rem 1.4rem 0.8rem 0.8rem;
  }
}
.fin-split-button-m {
  height: 3.2rem;

  .fin-main-button {
    @apply fin-text-body-2-moderate;
    padding: 0.6rem 0.8rem 0.6rem 1.6rem;
  }

  .fin-dropdown-toggle {
    padding: 0.6rem 1.2rem 0.6rem 0.8rem;
  }
}
