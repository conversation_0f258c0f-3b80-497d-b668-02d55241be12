import { FinSize } from '@fincloud/ui/types';
import { Meta, StoryObj } from '@storybook/angular';
import { FinSplitButtonType } from '../enums/fin-split-button-type';
import { FinSplitButtonOption } from '../models/fin-split-button-option';
import { FinSplitButtonComponent } from './split-button.component';

const meta: Meta<FinSplitButtonComponent> = {
  component: FinSplitButtonComponent,
  title: 'Components/Buttons/Split Button',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinSplitButtonModule } from "@fincloud/ui/split-button"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6880-19721&m=dev',
    },
  },
};
export default meta;

type Story = StoryObj<FinSplitButtonComponent>;

const options: FinSplitButtonOption[] = [
  {
    label: 'Default',
    value: 'default',
    icon: 'check',
    disabled: false,
  },
  {
    label: 'Hover',
    value: 'hover',
    icon: 'check',
    disabled: false,
  },
  {
    label: 'Disabled',
    value: 'disabled',
    icon: 'check',
    disabled: true,
  },
];

export const Primary: Story = {
  args: {
    label: 'Button',
    size: FinSize.M,
    type: FinSplitButtonType.PRIMARY,
    options: options,
    disabled: false,
  },
  argTypes: {
    size: {
      options: [FinSize.M, FinSize.L],
      control: { type: 'select' },
    },
    type: {
      options: Object.values(FinSplitButtonType),
      control: { type: 'select' },
    },
    disabled: {
      control: { type: 'boolean' },
    },
  },
};

export const PrimaryDisabled: Story = {
  argTypes: {
    ...Primary.argTypes,
  },
  args: {
    ...Primary.args,
    disabled: true,
  },
};

export const Secondary: Story = {
  argTypes: {
    ...Primary.argTypes,
  },
  args: {
    ...Primary.args,
    type: FinSplitButtonType.SECONDARY,
  },
};

export const SecondaryDisabled: Story = {
  argTypes: {
    ...Primary.argTypes,
  },
  args: {
    ...Secondary.args,
    disabled: true,
  },
};
