<div
  [ngClass]="['fin-split-button-' + size, 'fin-split-button-' + type]"
  class="fin-button-container fin-flex"
>
  <button
    class="fin-main-button fin-flex fin-items-center fin-font-semibold"
    (click)="onMainButtonClick()"
    [disabled]="disabled"
  >
    @if (label) {
      <mat-label>{{ label }}</mat-label>
    }
  </button>
  <div class="fin-divider-container fin-flex fin-py-2">
    <div
      class="fin-divider fin-w-[0.1rem]"
      [ngClass]="{
        'fin-h-[2.8rem]': size === sizes.L,
        'fin-h-[2rem]': size === sizes.M,
      }"
    ></div>
  </div>
  <button
    [finActionMenuTrigger]="finMenu.panel"
    [disabled]="disabled"
    class="fin-dropdown-toggle fin-flex fin-items-center"
    [ngClass]="{
      'fin-w-[4.4rem]': size === sizes.L,
      'fin-w-[4.2rem]': size === sizes.M,
    }"
    (menuOpened)="toggleMenu()"
    (menuClosed)="toggleMenu()"
  >
    <fin-icon
      [name]="isMenuOpened ? 'expand_less' : 'expand_more'"
      [size]="sizes.L"
    ></fin-icon>
  </button>
</div>

<fin-actions-menu #finMenu="finActionMenu">
  @for (option of options; track trackByLabel($index, option)) {
    <button
      fin-menu-item
      [size]="sizes.M"
      [disabled]="option.disabled"
      [iconName]="option.icon || ''"
      (click)="onOptionButtonClick(option)"
    >
      <ng-container finMenuItemTitle> {{ option.label }}</ng-container>
    </button>
  }
</fin-actions-menu>
