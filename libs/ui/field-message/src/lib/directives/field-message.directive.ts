import { Directive, Input, TemplateRef } from '@angular/core';
import { FinFieldMessagesType } from '@fincloud/ui/types';

@Directive({
  selector: '[finFieldMessage]',
  standalone: true,
})
export class FinFieldMessageDirective {
  /** The key corresponding to the validation error (e.g. 'required', 'minlength', etc.) */
  @Input() errorKey = '';

  /** The message type. */
  @Input() type:
    | FinFieldMessagesType
    | Lowercase<keyof typeof FinFieldMessagesType> = FinFieldMessagesType.ERROR;

  /** Optional priority; If omitted the priority is set by the order of finFieldMessage in the template */
  @Input() priority?: number;

  constructor(public template: TemplateRef<unknown> | null) {}
}
