import { Directive, Input, TemplateRef } from '@angular/core';
import { FinFieldMessagesType } from '@fincloud/ui/types';

@Directive({
  selector: '[finFieldMessageBody]',
  standalone: true,
})
export class FinFieldMessageBodyDirective {
  // The type of message
  @Input() type:
    | FinFieldMessagesType
    | Lowercase<keyof typeof FinFieldMessagesType>
    | undefined = FinFieldMessagesType.ERROR;

  constructor(public template: TemplateRef<unknown> | null) {}
}
