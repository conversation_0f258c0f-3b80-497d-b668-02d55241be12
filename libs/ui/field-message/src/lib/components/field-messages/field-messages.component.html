<ng-template finFieldMessageBody [type]="(getSelectedMessage$ | async)?.type">
  @if (getSelectedMessage$ | async; as selectedMessage) {
    <div
      class="fin-flex fin-items-start fin-gap-size-spacing-8 fin-mt-size-spacing-4"
    >
      @switch (selectedMessage.type) {
        @case ('error') {
          <fin-icon
            [size]="sizes.S"
            name="error"
            class="fin-text-color-icons-error fin-mt-size-spacing-1"
          ></fin-icon>
        }
        @case ('warning') {
          <fin-icon
            [size]="sizes.S"
            name="assignment_late"
            class="fin-text-color-icons-warning fin-mt-size-spacing-1"
          ></fin-icon>
        }
        @case ('success') {
          <fin-icon
            [size]="sizes.S"
            name="check_circle"
            class="fin-text-color-icons-success fin-mt-size-spacing-1"
          ></fin-icon>
        }
      }
      <div
        class="fin-text-body-3-moderate fin-text-color-text-secondary fin-message-body"
      >
        <ng-container
          *ngTemplateOutlet="selectedMessage.template"
        ></ng-container>
      </div>
    </div>
  }
</ng-template>

<ng-template></ng-template>

@if (control && messageTemplate) {
  <ng-container *ngTemplateOutlet="messageTemplate.template"></ng-container>
}
