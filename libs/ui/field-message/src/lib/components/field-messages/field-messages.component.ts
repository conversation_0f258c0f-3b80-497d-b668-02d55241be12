import { CommonModule } from '@angular/common';
import {
  AfterContentInit,
  ChangeDetectionStrategy,
  Component,
  ContentChildren,
  DestroyRef,
  forwardRef,
  Injector,
  OnInit,
  Optional,
  QueryList,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NG_VALUE_ACCESSOR, ValidationErrors } from '@angular/forms';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';
import { FinControlValueAccessor } from '@fincloud/utils/control-value-accessor';
import { FinFieldService } from '@fincloud/utils/services';
import { isEqual } from 'lodash-es';
import {
  BehaviorSubject,
  debounceTime,
  distinctUntilChanged,
  shareReplay,
  tap,
} from 'rxjs';
import { FinFieldMessageBodyDirective } from '../../directives/field-message-body.directive';
import { FinFieldMessageDirective } from '../../directives/field-message.directive';
import { FinFieldMessageService } from '../../services/fin-field-message.service';

@Component({
  selector: 'fin-field-messages',
  standalone: true,
  imports: [CommonModule, FinIconModule, FinFieldMessageBodyDirective],
  templateUrl: './field-messages.component.html',
  styleUrl: './field-messages.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FinFieldMessagesComponent),
      multi: true,
    },
  ],
})
export class FinFieldMessagesComponent
  extends FinControlValueAccessor
  implements OnInit, AfterContentInit
{
  @ContentChildren(FinFieldMessageDirective, { descendants: true })
  protected messages!: QueryList<FinFieldMessageDirective>;

  @ViewChild(FinFieldMessageBodyDirective)
  protected messageTemplate!: FinFieldMessageBodyDirective;

  protected validationErrors: ValidationErrors | null = null;

  /** The current validation message */
  private setSelectedMessage$$ =
    new BehaviorSubject<FinFieldMessageDirective | null>(null);

  getSelectedMessage$ = this.setSelectedMessage$$.asObservable().pipe(
    tap((message) => {
      const messageTemplate = this.messageTemplate;
      if (message && !message?.template) {
        messageTemplate.template = null;
      }
      if (messageTemplate && message?.type) {
        messageTemplate.type = message?.type;
      }
      // Update the shared service with the selected message type.
      this.finFieldMessageService?.setMessage(this.messageTemplate);
    }),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  protected sizes = FinSize;

  constructor(
    private destroyRef: DestroyRef,
    injector: Injector,
    @Optional() private finFieldService?: FinFieldService,
    @Optional() private finFieldMessageService?: FinFieldMessageService,
  ) {
    super(injector);
  }

  ngOnInit(): void {
    this.finFieldService?.controlErrors$
      .pipe(
        distinctUntilChanged((prev, curr) => isEqual(prev, curr)),
        debounceTime(300),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((errors) => {
        this.validationErrors = errors;
        this.selectMessage();
      });

    this.control?.statusChanges
      .pipe(debounceTime(300), takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.validationErrors = this.control?.errors;
        this.selectMessage();
      });
  }

  ngAfterContentInit() {
    if (!this.control) {
      return;
    }
    this.validationErrors = this.control.errors;
    this.selectMessage();
    this.messages.changes
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => this.selectMessage());
  }

  private selectMessage() {
    if (!this.messages) {
      this.setSelectedMessage$$.next(null);
      this.finFieldMessageService?.setMessage(null);
      return;
    }

    const successMessage = this.messages.filter(
      (msg) => msg.type === 'success',
    )[0];

    const errors = this.validationErrors;
    if (!errors && !successMessage) {
      this.setSelectedMessage$$.next(null);
      this.finFieldMessageService?.setMessage(null);
      return;
    }

    // Filter messages matching a current error on the control.
    const activeMessages = this.messages.filter((msg) => {
      return (
        !!errors?.[msg.errorKey] ||
        (msg.type === 'success' && !errors?.[msg.errorKey])
      );
    });

    if (activeMessages.length === 0) {
      this.setSelectedMessage$$.next(null);
      this.finFieldMessageService?.setMessage(null);
      return;
    }

    // Sort messages by priority if defined or by projection order.
    const selectedMessage = activeMessages.sort((a, b) => {
      if (a.priority != null && b.priority != null) {
        return a.priority - b.priority;
      } else if (a.priority != null) {
        return -1;
      } else if (b.priority != null) {
        return 1;
      } else {
        return 0;
      }
    })[0];

    if (this.isMessageTextEmpty(selectedMessage.template)) {
      const emptyMessage: FinFieldMessageDirective = {
        ...selectedMessage,
        template: null,
      };
      this.setSelectedMessage$$.next(emptyMessage);
    } else {
      this.setSelectedMessage$$.next(selectedMessage);
    }
  }

  private isMessageTextEmpty(template: TemplateRef<unknown> | null) {
    if (!template) {
      return true;
    }
    const viewRef = template.createEmbeddedView(null);
    viewRef.detectChanges();

    const isEmpty = viewRef.rootNodes.length === 0;

    viewRef.destroy(); // clean up
    return isEmpty;
  }
}
