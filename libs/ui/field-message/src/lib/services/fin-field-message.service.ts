import { Injectable } from '@angular/core';
import { animation<PERSON>rameScheduler, BehaviorSubject, observeOn } from 'rxjs';
import { FinFieldMessageBodyDirective } from '../directives/field-message-body.directive';

@Injectable()
export class FinFieldMessageService {
  private messageSubject$$ =
    new BehaviorSubject<FinFieldMessageBodyDirective | null>(null);

  /** Returns the active validation message. */
  getMessage$ = this.messageSubject$$
    .asObservable()
    .pipe(observeOn(animationFrameScheduler));

  /** Set the active validation message. */
  setMessage(selectedMessage: FinFieldMessageBodyDirective | null) {
    this.messageSubject$$.next(selectedMessage);
  }
}
