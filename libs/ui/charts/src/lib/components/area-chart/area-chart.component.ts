import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  Input,
  OnDestroy,
  ViewChild,
} from '@angular/core';
import {
  Chart,
  ChartConfiguration,
  ScriptableContext,
  registerables,
} from 'chart.js';

/**
 * A chart that represents quantitative data with filled areas under a line graph.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-area-chart--docs Storybook Reference}
 */
@Component({
  selector: 'fin-area-chart',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './area-chart.component.html',
  styleUrl: './area-chart.component.scss',
  host: {
    class: 'fin-area-chart',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinAreaChartComponent implements AfterViewInit, OnDestroy {
  /** An array of labels for each data point on the `x-axis` of the line chart. */
  @Input() labels: string[] = [];
  /** An array of values representing the data points for the line chart. */
  @Input() values: number[] = [];
  /** Color of the line border in `HEX` format. */
  @Input() borderColor = '#00675D';
  /** Points' color on the line chart in `HEX` format. */
  @Input() pointBackgroundColor = '#FFFFFF';
  /** Whether the grid lines should be displayed on the chart. */
  @Input() showGrid = false;

  @ViewChild('lineCanvas') private lineCanvas!: ElementRef<HTMLCanvasElement>;

  private lineChart!: Chart<'line'>;

  ngAfterViewInit(): void {
    Chart.register(...registerables);
    this.createLineChart();
  }

  ngOnDestroy(): void {
    this.lineChart.destroy();
  }

  private createLineChart(): void {
    const config: ChartConfiguration<'line'> = {
      type: 'line',
      data: {
        labels: this.labels,
        datasets: [
          {
            data: this.values,
            borderColor: this.borderColor,
            pointBackgroundColor: this.pointBackgroundColor,
            backgroundColor: (context) => {
              return this.getGradientBg(context);
            },
            fill: true,
          },
        ],
      },
      options: {
        responsive: true,
        plugins: {
          legend: {
            display: false,
          },
        },
        scales: {
          x: {
            grid: {
              display: this.showGrid,
            },
          },
          y: {
            grid: {
              display: this.showGrid,
            },
          },
        },
      },
    };

    this.lineChart = new Chart(this.lineCanvas.nativeElement, config);
  }

  private getGradientBg(
    context: ScriptableContext<'line'>,
  ): CanvasGradient | '' {
    if (context.chart.chartArea) {
      const rgb = this.getRGB(this.borderColor);
      const bgColor = [
        `rgba(${rgb}, 0.4)`,
        `rgba(${rgb}, 0.3)`,
        `rgba(${rgb}, 0.2)`,
        `rgba(${rgb}, 0.1)`,
        `rgba(${rgb}, 0)`,
      ];
      const {
        ctx,
        chartArea: { top, bottom },
      } = context.chart;
      const gradientBg = ctx.createLinearGradient(0, top, 0, bottom);
      const colorTranches = 1 / (bgColor.length - 1);

      bgColor.forEach((color, index) => {
        gradientBg.addColorStop(0 + index * colorTranches, bgColor[index]);
      });

      return gradientBg;
    }

    return '';
  }

  private getRGB(color: string) {
    let r = 255;
    let g = 255;
    let b = 255;

    if (color.startsWith('#')) {
      const hex = color.slice(1);
      r = parseInt(hex.substring(0, 2), 16);
      g = parseInt(hex.substring(2, 4), 16);
      b = parseInt(hex.substring(4, 6), 16);
    }

    if (color.startsWith('rgb')) {
      const rgbValues = color.match(/\d+/g);
      if (rgbValues) {
        r = parseInt(rgbValues[0], 10);
        g = parseInt(rgbValues[1], 10);
        b = parseInt(rgbValues[2], 10);
      }
    }

    return `${r}, ${g}, ${b}`;
  }
}
