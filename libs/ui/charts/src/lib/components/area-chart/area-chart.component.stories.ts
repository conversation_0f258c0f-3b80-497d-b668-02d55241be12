import {
  componentWrapperDecorator,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { FinAreaChartComponent } from './area-chart.component';

const meta: Meta<FinAreaChartComponent> = {
  component: FinAreaChartComponent,
  title: 'Components/Charts/Chart Area',
  decorators: [
    componentWrapperDecorator(
      (story) => `<div class="fin-w-[800px]">${story}</div>`,
    ),
  ],
  parameters: {
    docs: {
      description: {
        component: '`import { FinChartsModule } from "@fincloud/ui/charts"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6090-874&t=f4pqckYkgUv2n9Y3-4',
    },
  },
};
export default meta;
type Story = StoryObj<FinAreaChartComponent>;

export const Default: Story = {
  args: {
    labels: ['January', 'February', 'March', 'April'],
    values: [10, 20, 15, 30],
    showGrid: false,
    borderColor: '#00675D',
    pointBackgroundColor: '#FFFFFF',
  },
  argTypes: {
    showGrid: {
      type: 'boolean',
    },
  },
  render: (args) => ({
    props: args,
    template: `
      <fin-area-chart
        [labels]="labels"
        [values]="values"
        [showGrid]="showGrid"
        [borderColor]="borderColor"
        [pointBackgroundColor]="pointBackgroundColor"
      ></fin-area-chart>
    `,
  }),
};
