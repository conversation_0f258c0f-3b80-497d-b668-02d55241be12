import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  Input,
  OnDestroy,
  ViewChild,
} from '@angular/core';
import { FinExpansionPanelModule } from '@fincloud/ui/expansion-panel';
import { Chart, ChartConfiguration, registerables } from 'chart.js';
import { FinBarChart } from '../../models/fin-bar-chart';
import { generateHexColor } from '../../utils/generate-hex-color';
import { PREDEFINED_COLORS } from '../../utils/predefined-colors';

/**
 * A chart that displays data with rectangular bars proportional to the values they represent.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-bar-chart--docs Storybook Reference}
 */
@Component({
  selector: 'fin-bar-chart',
  standalone: true,
  imports: [CommonModule, FinExpansionPanelModule],
  templateUrl: './bar-chart.component.html',
  styleUrl: './bar-chart.component.scss',
  host: {
    class: 'fin-bar-chart',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinBarChartComponent implements AfterViewInit, OnDestroy {
  /** An array of labels for each sector of the bar chart. */
  @Input() labels: string[] = [];

  /** An array datasets for the bar chart. */
  @Input() bars: FinBarChart[] = [];

  /** Whether the bars are stacked on top of each other.  */
  @Input({ transform: booleanAttribute }) areBarsStacked = false;

  /** Whether the grid lines should be displayed on the chart. */
  @Input({ transform: booleanAttribute }) showGrid = false;

  /** If set the colors will be generated automatically for the `bars.data`. The `bars.backgroundColor` is not required in this case. */
  @Input({ transform: booleanAttribute }) autoColors = false;

  /** Prefix for each value. */
  @Input() valuePrefix = '';

  /** Suffix for each value. */
  @Input() valueSuffix = '';

  @ViewChild('barCanvas') private barCanvas!: ElementRef<HTMLCanvasElement>;

  private barChart!: Chart<'bar'>;

  ngAfterViewInit(): void {
    Chart.register(...registerables);
    this.createBarChart();
  }

  ngOnDestroy(): void {
    this.barChart.destroy();
  }

  private createBarChart(): void {
    const config: ChartConfiguration<'bar'> = {
      type: 'bar',
      data: {
        labels: this.labels,
        datasets: this.bars.map((bar, index) => ({
          ...bar,
          barThickness: this.areBarsStacked ? 8 : 12,
          borderWidth: this.areBarsStacked ? 0 : 2,
          borderColor: 'transparent',
          backgroundColor: this.autoColors
            ? this.getAutoColor(index)
            : bar.backgroundColor,
        })),
      },
      options: {
        responsive: true,
        scales: {
          x: {
            stacked: this.areBarsStacked,
            grid: {
              display: this.showGrid,
            },
          },
          y: {
            stacked: this.areBarsStacked,
            grid: {
              display: this.showGrid,
            },
          },
        },
        plugins: {
          legend: {
            display: false,
          },
          tooltip: {
            callbacks: {
              label: (context) => {
                const value = context.raw;
                return ` ${this.valuePrefix} ${value} ${this.valueSuffix}`;
              },
            },
          },
        },
      },
    };

    this.barChart = new Chart(this.barCanvas.nativeElement, config);
  }

  private getAutoColor(varIndex: number) {
    return PREDEFINED_COLORS[varIndex] ?? generateHexColor();
  }
}
