import {
  componentWrapperDecorator,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { FinBarChartComponent } from './bar-chart.component';

const meta: Meta<FinBarChartComponent> = {
  component: FinBarChartComponent,
  title: 'Components/Charts/Chart Bar',
  decorators: [
    componentWrapperDecorator(
      (story) => `<div class="fin-w-[400px]">${story}</div>`,
    ),
  ],
  parameters: {
    docs: {
      description: {
        component: '`import { FinChartsModule } from "@fincloud/ui/charts"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6090-874&t=f4pqckYkgUv2n9Y3-4',
    },
  },
};
export default meta;
type Story = StoryObj<FinBarChartComponent>;

const renderTemplate = (args: Partial<FinBarChartComponent>) => ({
  props: args,
  template: `
      <fin-bar-chart
        [labels]="labels"
        [bars]="bars"
        [areBarsStacked]="areBarsStacked"
        [showGrid]="showGrid"
        [autoColors]="autoColors"
        [valuePrefix]="valuePrefix"
        [valueSuffix]="valueSuffix"
      ></fin-bar-chart>
  `,
});

export const Default: Story = {
  name: 'Non stacked bars',
  args: {
    labels: ['January', 'February', 'March', 'April', 'May'],
    bars: [
      {
        label: 'Dataset 1',
        data: [12, 19, 3, 5, 2],
        backgroundColor: '#CDCFF6',
      },
      {
        label: 'Dataset 2',
        data: [5, 7, 2, 3, 7],
        backgroundColor: '#FFCC85',
      },
      {
        label: 'Dataset 3',
        data: [13, 8, 5, 6, 4],
        backgroundColor: '#B4F2B0',
      },
    ],
    areBarsStacked: false,
    showGrid: false,
    autoColors: false,
    valuePrefix: '',
    valueSuffix: '',
  },
  argTypes: {
    labels: {},
    bars: {},
    areBarsStacked: {
      type: 'boolean',
    },
    showGrid: {
      type: 'boolean',
    },
    autoColors: {
      type: 'boolean',
    },
  },
  render: (args) => renderTemplate(args),
};

export const StackedBars: Story = {
  args: {
    ...Default.args,
    areBarsStacked: true,
    bars: [
      {
        label: 'Dataset 1',
        data: [12, 19, 3, 5, 2],
        backgroundColor: '#00675D',
      },
      {
        label: 'Dataset 2',
        data: [5, 7, 2, 3, 7],
        backgroundColor: '#E5F0EF',
      },
    ],
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => renderTemplate(args),
};
