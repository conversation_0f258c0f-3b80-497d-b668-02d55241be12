import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  Input,
  OnDestroy,
  ViewChild,
} from '@angular/core';
import { FinSize } from '@fincloud/ui/types';
import { IgnoresNull } from '@fincloud/utils/decorators';
import { Chart, ChartConfiguration, registerables } from 'chart.js';
import { EMPTY_CHART_COLOR } from '../../utils/empty-chart-color';
import { generateHexColor } from '../../utils/generate-hex-color';
import { PREDEFINED_COLORS } from '../../utils/predefined-colors';

/**
 * A circular chart similar to a pie chart but with a hollow center, used to show proportional data.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-doughnut-chart--docs Storybook Reference}
 */
@Component({
  selector: 'fin-doughnut-chart',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './doughnut-chart.component.html',
  styleUrl: './doughnut-chart.component.scss',
  host: {
    class: 'fin-doughnut-chart',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinDoughnutChartComponent implements AfterViewInit, OnDestroy {
  /** An array of labels for each sector of the doughnut chart. */
  @Input() labels: string[] = [];

  private _values: number[] = [];
  get values() {
    return this._values;
  }
  /** An array of values for each sector of the doughnut chart. */
  @IgnoresNull()
  @Input()
  set values(data: number[]) {
    this._values = data;
    this.createDoughnutChart();
  }

  /** An array of background colors for each sector in HEX or RGB format. */
  @Input() colors: string[] = [];

  /** Sectors size in the chart. */
  @Input() size: FinSize.S | FinSize.L | FinSize.XXL = FinSize.L;

  /** Colors will be generated automatically for every value. The `colors` array is not required in this case. */
  @Input() autoColors = false;

  /** Prefix for each value. */
  @Input() valuePrefix = '';

  /** Suffix for each value. */
  @Input() valueSuffix = '';

  /** Enable chart tooltip. */
  @Input() enabledTooltip = true;

  /** Pass total value for the chart to calculate the difference. */
  @Input() totalChartValue = 0;

  @ViewChild('doughnutCanvas')
  private doughnutCanvas: ElementRef<HTMLCanvasElement> | null = null;

  private doughnutChart: Chart<'doughnut'> | null = null;

  ngAfterViewInit(): void {
    Chart.register(...registerables);
    this.createDoughnutChart();
  }

  ngOnDestroy(): void {
    this.doughnutChart?.destroy();
  }

  private calculateRelativeProportion(
    dataPoints: number[],
    totalValue: number,
  ): number[] {
    if (dataPoints.length && totalValue) {
      return dataPoints.concat(
        totalValue - dataPoints.reduce((acc, curr) => (acc += curr), 0),
      );
    }

    if (totalValue) {
      return [totalValue];
    }

    if (dataPoints.length) {
      return dataPoints;
    }

    // when no datapoints are provided, we still have to show the chart full (or empty, depends of the mood)
    return [1];
  }

  private createDoughnutChart() {
    if (!this.doughnutCanvas) {
      return;
    }
    if (this.doughnutChart) {
      this.doughnutChart.destroy();
      this.doughnutChart = null;
    }
    const config: ChartConfiguration<'doughnut'> = {
      type: 'doughnut',
      data: {
        labels: this.labels,
        datasets: [
          {
            data: this.calculateRelativeProportion(
              this.values,
              this.totalChartValue,
            ),
            backgroundColor: this.autoColors
              ? this.getAutoColors()
              : this.colors.concat(EMPTY_CHART_COLOR),
            borderWidth: this.size === FinSize.S ? 2 : 0,
          },
        ],
      },
      options: {
        responsive: true,
        cutout: this.getCutout(this.size),
        plugins: {
          legend: {
            display: false,
          },
          tooltip: {
            enabled: this.enabledTooltip,
            callbacks: {
              label: (context) => {
                const value = context.raw;
                return ` ${this.valuePrefix} ${value} ${this.valueSuffix}`;
              },
            },
          },
        },
      },
    };

    this.doughnutChart = new Chart(this.doughnutCanvas.nativeElement, config);
  }

  private getCutout(size: FinSize) {
    switch (size) {
      case FinSize.S:
        return '80%';
      case FinSize.L:
        return '50%';
      default:
        return '0%';
    }
  }

  private getAutoColors() {
    if (this.values.length > PREDEFINED_COLORS.length) {
      const additionalColors = Array.from(
        { length: this.values.length - PREDEFINED_COLORS.length },
        () => generateHexColor(),
      );
      return PREDEFINED_COLORS.concat(additionalColors, EMPTY_CHART_COLOR);
    }
    return PREDEFINED_COLORS.slice(0, this.values.length).concat(
      EMPTY_CHART_COLOR,
    );
  }
}
