import { FinSize } from '@fincloud/ui/types';
import {
  componentWrapperDecorator,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { FinDoughnutChartComponent } from './doughnut-chart.component';

const meta: Meta<FinDoughnutChartComponent> = {
  component: FinDoughnutChartComponent,
  title: 'Components/Charts/Chart Doughnut',
  decorators: [
    componentWrapperDecorator(
      (story) => `<div class="fin-w-[183px] fin-h-[183px]">${story}</div>`,
    ),
  ],
  parameters: {
    docs: {
      description: {
        component: '`import { FinChartsModule } from "@fincloud/ui/charts"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6090-874&t=f4pqckYkgUv2n9Y3-4',
    },
  },
};
export default meta;
type Story = StoryObj<FinDoughnutChartComponent>;

const renderTemplate = (args: Partial<FinDoughnutChartComponent>) => ({
  props: args,
  template: `
      <fin-doughnut-chart
        [labels]="labels"
        [values]="values"
        [colors]="colors"
        [size]="size"
        [autoColors]="autoColors"
        [valuePrefix]="valuePrefix"
        [valueSuffix]="valueSuffix"
      ></fin-doughnut-chart>
  `,
});

export const Default: Story = {
  name: 'Doughnut L',
  args: {
    labels: ['Label 1', 'Label 2'],
    values: [35, 65],
    colors: ['#CCE1DF', '#33857D'],
    size: FinSize.L,
    autoColors: false,
    valuePrefix: '',
    valueSuffix: '',
  },
  argTypes: {
    size: {
      control: { type: 'select' },
      options: [FinSize.S, FinSize.L, FinSize.XXL],
    },
    autoColors: {
      type: 'boolean',
    },
  },
  render: (args) => renderTemplate(args),
};

export const DoughnutS: Story = {
  args: {
    labels: [
      'Label 1',
      'Label 2',
      'Label 3',
      'Label 4',
      'Label 5',
      'Label 6',
      'Label 7',
      'Label 8',
    ],
    values: [30, 10, 10, 10, 10, 20, 5, 5],
    colors: [
      '#8287E7',
      '#88EA81',
      '#FFCC85',
      '#80B3AE',
      '#68758B',
      '#FFB0AB',
      '#676978',
      '#F1E480',
    ],
    size: FinSize.S,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => renderTemplate(args),
};

export const DoughnutXXL: Story = {
  args: {
    ...Default.args,
    size: FinSize.XXL,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => renderTemplate(args),
};
