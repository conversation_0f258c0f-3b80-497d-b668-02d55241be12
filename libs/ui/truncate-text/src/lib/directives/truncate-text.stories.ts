import { BrowserModule } from '@angular/platform-browser';
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { FinTruncateTextModule } from '../truncate-text.module';
import { FinTruncateTextDirective } from './truncate-text.directive';
const meta: Meta<FinTruncateTextDirective> = {
  title: 'Directives/Truncate Text',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinTruncateTextModule } from "@fincloud/ui/truncate-text"`',
      },
    },
  },
  decorators: [
    moduleMetadata({
      imports: [BrowserModule, FinTruncateTextModule],
    }),
  ],
};

export default meta;

type Story = StoryObj<
  FinTruncateTextDirective & {
    showArrow: boolean;
    content: string;
    container: string;
    disableTooltip: false;
  }
>;

export const Truncate: Story = {
  args: {
    disableTooltip: false,
    showArrow: false,
  },
  argTypes: {
    disableTooltip: {
      description: 'Disable the tooltip.',
      control: 'boolean',
    },
    content: {
      description:
        'The string content or a TemplateRef for the content to be displayed in the tooltip.',
    },
    container: {
      description:
        'A selector specifying the element the tooltip should be appended to.',
      defaultValue: { summary: 'body' },
    },
    showArrow: {
      options: [true, false],
      description: 'Display an arrow icon..',
      defaultValue: false,
      control: { type: 'radio' },
      table: {
        defaultValue: { summary: 'false' },
      },
    },
  },
  render: (args) => ({
    props: args,
    template: `
    <div
      [disableTooltip]="disableTooltip"
      [showArrow]="showArrow"
      class="fin-max-w-xs fin-text-body-1-moderate" finTruncateText
    >
      Hover over me to see the tooltip
    </div>`,
  }),
};
