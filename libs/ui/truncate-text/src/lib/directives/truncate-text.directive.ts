import {
  AfterContentInit,
  Directive,
  ElementRef,
  Host,
  Input,
  <PERSON><PERSON><PERSON>,
  On<PERSON><PERSON>roy,
  Renderer2,
  TemplateRef,
} from '@angular/core';
import { FinTooltipDirective } from '@fincloud/ui/tooltip';

/**
 * A directive that truncates text with an ellipsis when it overflows its container.
 * It also optionally shows a tooltip with the full text content
 * when hovering over truncated text.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/directives-truncate-text--docs Storybook Reference}
 */
@Directive({
  selector: '[finTruncateText]',
  standalone: true,
  hostDirectives: [
    {
      directive: FinTooltipDirective,
      inputs: [
        'showArrow',
        'placement',
        'content',
        'openDelay',
        'closeDelay',
        'disableTooltip',
      ],
    },
  ],
  host: {
    class: 'fin-truncate-text',
  },
})
export class FinTruncateTextDirective implements AfterContentInit, On<PERSON><PERSON>roy {
  @Input() content: TemplateRef<unknown> | string | null | undefined;
  /**
   * Stores the original text content of the element.
   * This is used to populate the tooltip content when text is truncated.
   */
  private originalText = '';

  /**
   * ResizeObserver instance used to detect changes in the element's dimensions.
   * Set to null when destroyed to prevent memory leaks.
   */
  private resizeObserver: ResizeObserver | null = null;

  /**
   * MutationObserver instance used to detect changes in the element's text content.
   * Set to null when destroyed to prevent memory leaks.
   */
  private mutationObserver: MutationObserver | null = null;

  constructor(
    private elementRef: ElementRef<HTMLElement>,
    private renderer: Renderer2,
    @Host() private finTooltipDirective: FinTooltipDirective,
    private ngZone: NgZone,
  ) {}

  /**
   * Lifecycle hook that is called after the directive's content has been initialized.
   * Sets up text truncation, initializes observers for resize and content changes.
   */
  ngAfterContentInit(): void {
    this.updateOriginalText();
    this.setupTruncation();
    this.observeResize();
    this.observeContentChanges();
  }

  /**
   * Cleans up observers to prevent memory leaks.
   */
  ngOnDestroy(): void {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = null;
    }

    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
      this.mutationObserver = null;
    }
  }

  /**
   * Updates the stored original text content from the host element.
   * Called during initialization and whenever the element's content changes.
   */
  private updateOriginalText(): void {
    this.originalText = this.elementRef.nativeElement.textContent || '';
  }

  /**
   * Applies CSS styles to the host element to enable text truncation with ellipsis.
   * Also performs initial truncation check.
   */
  private setupTruncation(): void {
    const element = this.elementRef.nativeElement;

    // Apply CSS for text truncation
    this.renderer.setStyle(element, 'overflow', 'hidden');
    this.renderer.setStyle(element, 'text-overflow', 'ellipsis');
    this.renderer.setStyle(element, 'white-space', 'nowrap');

    // Check if text is actually truncated
    this.checkTruncation();
  }

  /**
   * Determines if the text content is truncated and updates tooltip state accordingly.
   * Uses multiple detection strategies to ensure reliable truncation detection across
   * different browsers, fonts, and rendering scenarios.
   * @sideEffects Updates the tooltip directive's state
   */
  private checkTruncation(): void {
    const element = this.elementRef.nativeElement;
    const isTruncated = this.isTextTruncated(element);

    // Add tooltip only if text is truncated and tooltip is not explicitly disabled
    if (isTruncated && !this.finTooltipDirective.disableTooltip) {
      this.finTooltipDirective.updateState({
        content: this.content || this.originalText,
        disableTooltip: false,
      });
    } else {
      this.finTooltipDirective.updateState({
        disableTooltip: true,
      });
    }
  }

  /**
   * Determines if text within an element is visually truncated using multiple detection strategies.
   */
  private isTextTruncated(element: HTMLElement): boolean {
    // First verify the element has the necessary CSS styles for truncation
    const hasEllipsisStyle = this.hasEllipsisStyles(element);
    if (!hasEllipsisStyle) {
      return false;
    }

    // Then check if the content actually overflows using two different methods
    return this.hasWidthOverflow(element) || this.hasRangeOverflow(element);
  }

  /**
   * Checks if the element has the necessary CSS styles for text truncation with ellipsis.
   * This is a prerequisite for truncation to occur visually.
   */
  private hasEllipsisStyles(element: HTMLElement): boolean {
    try {
      const computedStyle = window.getComputedStyle(element);
      return (
        computedStyle.textOverflow === 'ellipsis' &&
        computedStyle.overflow === 'hidden' &&
        computedStyle.whiteSpace === 'nowrap'
      );
    } catch (e) {
      // If we can't check styles, assume styles are correct
      return true;
    }
  }

  /**
   * Checks if the element's content width exceeds its container width.
   * This is the most common and reliable method for detecting overflow.
   */
  private hasWidthOverflow(element: HTMLElement): boolean {
    // Add a small buffer (0.5px) to account for sub-pixel rendering differences
    const widthDifference = element.scrollWidth - element.clientWidth;
    return widthDifference > 0.1;
  }

  /**
   * Uses DOM Range API to measure the actual text width and compare it to container width.
   * This provides a secondary check that can catch some edge cases the scrollWidth method misses.
   */
  private hasRangeOverflow(element: HTMLElement): boolean {
    try {
      const range = document.createRange();
      range.selectNodeContents(element);
      const textWidth = range.getBoundingClientRect().width;

      // Compare with container width, with a small buffer for rounding errors
      return textWidth > element.clientWidth;
    } catch (e) {
      // If Range API fails, fall back to width overflow check
      return this.hasWidthOverflow(element);
    }
  }

  /**
   * Sets up a ResizeObserver to detect changes in the element's dimensions.
   * When the element is resized, re-checks truncation state.
   *
   * @performance Uses NgZone.runOutsideAngular to avoid triggering change detection
   * for the observer setup, but re-enters the Angular zone when handling resize events
   * to ensure proper UI updates.
   */
  private observeResize(): void {
    this.ngZone.runOutsideAngular(() => {
      this.resizeObserver = new ResizeObserver(() => {
        this.ngZone.run(() => {
          this.checkTruncation();
        });
      });

      this.resizeObserver.observe(this.elementRef.nativeElement);
    });
  }

  /**
   * Sets up a MutationObserver to detect changes in the element's text content.
   * When content changes, updates the stored original text and re-checks truncation.
   *
   * @performance Uses NgZone.runOutsideAngular to avoid triggering change detection
   * for observer setup, but re-enters the Angular zone when handling mutations
   * to ensure proper UI updates. This pattern optimizes performance by only triggering
   * change detection when actual content changes occur.
   */
  private observeContentChanges(): void {
    this.ngZone.runOutsideAngular(() => {
      this.mutationObserver = new MutationObserver(() => {
        this.ngZone.run(() => {
          this.updateOriginalText();
          this.checkTruncation();
        });
      });

      this.mutationObserver.observe(this.elementRef.nativeElement, {
        characterData: true,
        childList: true,
        subtree: true,
      });
    });
  }
}
