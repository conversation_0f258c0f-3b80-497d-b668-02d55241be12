import { UniqueSelectionDispatcher } from '@angular/cdk/collections';
import { CommonModule, DOCUMENT } from '@angular/common';
import {
  booleanAttribute,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ContentChild,
  Inject,
  Input,
  OnInit,
  Optional,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import {
  MAT_EXPANSION_PANEL,
  MatAccordionBase,
  MatExpansionModule,
  MatExpansionPanel,
  MatExpansionPanelDefaultOptions,
} from '@angular/material/expansion';
import { FinSize } from '@fincloud/ui/types';
import { FinAngularMaterialModule } from '@fincloud/utils/angular-material';
import { FinAccordionType } from '../../enums/fin-accordion-type';
import {
  FIN_ACCORDION,
  FinAccordionComponent,
} from '../accordion/accordion.component';

/**
 * A collapsible panel that can expand to show more content.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-expansion-panel--docs Storybook Reference}
 */
@Component({
  selector: 'fin-expansion-panel',
  standalone: true,
  imports: [CommonModule, MatExpansionModule, FinAngularMaterialModule],
  templateUrl: './expansion-panel.component.html',
  styleUrl: './expansion-panel.component.scss',
  host: {
    class: 'fin-expansion-panel',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    // Provide MatAccordion as undefined to prevent nested expansion panels from registering
    // to the same accordion.
    { provide: FinAccordionComponent, useValue: undefined },
    { provide: MAT_EXPANSION_PANEL, useExisting: MatExpansionPanel },
  ],
})
export class FinExpansionPanelComponent
  extends MatExpansionPanel
  implements OnInit
{
  @Input({ transform: booleanAttribute }) isSummaryAlwaysVisible = false;

  @ViewChild('expansionPanel')
  expansionPanel!: MatExpansionPanel;

  @ContentChild('finPrefix')
  prefixTemplate?: TemplateRef<unknown>;

  @ContentChild('finTitle')
  titleTemplate?: TemplateRef<unknown>;

  @ContentChild('finDescription')
  descriptionTemplate?: TemplateRef<unknown>;

  @ContentChild('finSummary')
  summaryTemplate?: TemplateRef<unknown>;

  @ContentChild('finContent', { descendants: false })
  contentTemplate!: TemplateRef<unknown>;

  protected nestedHeaderOffset = 0;
  protected nestedContentOffset = 0;

  get isExpanded(): boolean {
    return this.expansionPanel?.expanded;
  }

  onOpened() {
    this.opened.emit();
  }

  onClosed() {
    this.closed.emit();
  }

  onAfterExpand() {
    this.afterExpand.emit();
  }

  onAfterCollapse() {
    this.afterCollapse.emit();
  }

  // The constructor is necessary because this component extends CdkAccordionItem.
  // Without it, Storybook throws an error due to missing dependencies.
  // To be removed in feature versions.
  constructor(
    @Inject(FinAccordionComponent) accordion: MatAccordionBase,
    @Optional()
    @Inject(FIN_ACCORDION)
    private finAccordion: any, // the FinAccordionComponent is initialized once, we don't want to do it again
    _changeDetectorRef: ChangeDetectorRef,
    _uniqueSelectionDispatcher: UniqueSelectionDispatcher,
    _viewContainerRef: ViewContainerRef,
    @Inject(DOCUMENT) _document: any,
  ) {
    super(
      accordion,
      _changeDetectorRef,
      _uniqueSelectionDispatcher,
      _viewContainerRef,
      _document,
      '',
      {} as MatExpansionPanelDefaultOptions,
    );
  }

  ngOnInit(): void {
    this.setupNestedOffset();
  }

  private setupNestedOffset() {
    if (this.finAccordion.size === FinSize.S) {
      this.nestedHeaderOffset = this.finAccordion.level * 1.6;
      this.nestedContentOffset = this.finAccordion.level * 1.6;
    }
    if (
      this.finAccordion.size === FinSize.M ||
      this.finAccordion.size === FinSize.L
    ) {
      this.nestedHeaderOffset = this.finAccordion.level * 2.4;
      this.nestedContentOffset = this.finAccordion.level * 2.4;
    }

    if (
      this.finAccordion.level === 1 &&
      this.finAccordion.type === FinAccordionType.LIGHT
    ) {
      this.nestedContentOffset = 0;
    }
  }
}
