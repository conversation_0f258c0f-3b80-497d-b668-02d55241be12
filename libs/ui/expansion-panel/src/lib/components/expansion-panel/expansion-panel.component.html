<mat-expansion-panel
  #expansionPanel
  [hideToggle]="hideToggle"
  [togglePosition]="togglePosition"
  [expanded]="expanded"
  [disabled]="disabled"
  (opened)="onOpened()"
  (closed)="onClosed()"
  (afterExpand)="onAfterExpand()"
  (afterCollapse)="onAfterCollapse()"
>
  <mat-expansion-panel-header
    class="fin-group"
    [class.fin-cursor-not-allowed]="disabled"
    [style.padding-left.rem]="nestedHeaderOffset"
  >
    <mat-panel-title
      [ngClass]="{
        'fin-text-color-text-disabled': disabled,
        'fin-w-full': !descriptionTemplate,
      }"
    >
      @if (prefixTemplate) {
        <div (click)="$event.stopImmediatePropagation()" class="fin-pe-5">
          <ng-container [ngTemplateOutlet]="prefixTemplate"></ng-container>
        </div>
      }
      @if (titleTemplate) {
        <ng-container [ngTemplateOutlet]="titleTemplate"></ng-container>
      }
    </mat-panel-title>

    @if (descriptionTemplate) {
      <mat-panel-description
        [ngClass]="{
          'fin-text-color-text-disabled': disabled,
        }"
      >
        <ng-container [ngTemplateOutlet]="descriptionTemplate"></ng-container>
      </mat-panel-description>
    }

    @if (summaryTemplate) {
      <div
        (click)="$event.stopImmediatePropagation()"
        class="fin-summary group-hover:fin-block"
        [class.fin-hidden]="!isSummaryAlwaysVisible"
      >
        <ng-container [ngTemplateOutlet]="summaryTemplate"></ng-container>
      </div>
    }
  </mat-expansion-panel-header>

  @if (contentTemplate) {
    <div
      class="fin-panel-content"
      [style.padding-left.rem]="nestedContentOffset"
    >
      <ng-container [ngTemplateOutlet]="contentTemplate"></ng-container>
    </div>
  }
  <ng-content select="fin-accordion"></ng-content>
</mat-expansion-panel>
