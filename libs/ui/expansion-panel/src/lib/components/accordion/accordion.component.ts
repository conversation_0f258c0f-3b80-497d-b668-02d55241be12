import { CommonModule } from '@angular/common';
import {
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  Inject,
  InjectionToken,
  Input,
  OnInit,
  Optional,
  SkipSelf,
} from '@angular/core';
import {
  MAT_ACCORDION,
  MatAccordion,
  MatExpansionModule,
} from '@angular/material/expansion';
import { FinSize } from '@fincloud/ui/types';
import { FinAngularMaterialModule } from '@fincloud/utils/angular-material';
import { isNil } from 'lodash-es';
import { FinAccordionToggleDirection } from '../../enums/fin-accordion-toggle-direction';
import { FinAccordionType } from '../../enums/fin-accordion-type';

// eslint-disable-next-line @fincloud/ns/no-export-constant
export const FIN_ACCORDION = new InjectionToken<any>('FIN_ACCORDION');

@Component({
  selector: 'fin-accordion',
  standalone: true,
  imports: [CommonModule, MatExpansionModule, FinAngularMaterialModule],
  providers: [
    {
      provide: MAT_ACCORDION,
      useExisting: FinAccordionComponent,
    },
    {
      provide: FIN_ACCORDION,
      useExisting: FinAccordionComponent,
    },
  ],
  templateUrl: './accordion.component.html',
  styleUrl: './accordion.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'fin-accordion',
    '[class]': 'containerClasses',
  },
  exportAs: 'finAccordion',
})
export class FinAccordionComponent extends MatAccordion implements OnInit {
  /** Size of the accordion. */
  @Input() size: FinSize.L | FinSize.M | FinSize.S = FinSize.S;

  /** <span style="color:red">**Deprecated**</span><br> Whether the border separators are disabled. */
  @Input({ transform: booleanAttribute }) borderless = false;

  /** Direction of the expansion indicator. */
  @Input() toggleDirection: FinAccordionToggleDirection =
    FinAccordionToggleDirection.AUTO;

  /** Type of the accordion. */
  @Input() type: FinAccordionType = FinAccordionType.DEFAULT;

  /** Useful when nesting `fin-according` in `finContent` template. */
  @Input({ transform: booleanAttribute }) resetNesting!: boolean;

  /** Accordion nesting: starts at 1 and increments when nested. */
  level = 1;

  constructor(
    @Optional()
    @SkipSelf()
    @Inject(FIN_ACCORDION)
    private finAccordion: FinAccordionComponent,
  ) {
    super();
    this.displayMode = 'flat';
  }
  ngOnInit(): void {
    if (!isNil(this.finAccordion?.level) && !this.resetNesting) {
      this.level = this.finAccordion?.level + 1;
    }
  }

  protected get containerClasses(): string {
    let currentClass = `fin-accordion-${this.size}`;

    if (this.toggleDirection === FinAccordionToggleDirection.LEFT) {
      currentClass += ' fin-accordion-toggle-left';
    }

    if (this.toggleDirection === FinAccordionToggleDirection.RIGHT) {
      currentClass += ' fin-accordion-toggle-right';
    }

    if (this.type === FinAccordionType.LIGHT) {
      currentClass += ' fin-accordion-light';
    }

    return currentClass;
  }
}
