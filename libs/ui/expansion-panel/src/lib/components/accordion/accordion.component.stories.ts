import { CommonModule } from '@angular/common';
import { FormControl } from '@angular/forms';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinCheckboxModule } from '@fincloud/ui/checkbox';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';
import {
  componentWrapperDecorator,
  moduleMetadata,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { FinAccordionToggleDirection } from '../../enums/fin-accordion-toggle-direction';
import { FinAccordionTogglePosition } from '../../enums/fin-accordion-toggle-position';
import { FinAccordionType } from '../../enums/fin-accordion-type';
import { FinExpansionPanelModule } from '../../expansion-panel.module';
import { FinAccordionComponent } from './accordion.component';

const meta: Meta<FinAccordionComponent> = {
  component: FinAccordionComponent,
  title: 'Components/Expansion Panel',
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        FinExpansionPanelModule,
        FinButtonModule,
        FinIconModule,
        FinCheckboxModule,
      ],
    }),
    componentWrapperDecorator(
      (story) => `<div class="fin-w-[900px]">${story}</div>`,
    ),
  ],
  parameters: {
    backgrounds: {
      default: 'dark',
      values: [{ name: 'dark', value: '#f8f8f8' }],
    },
    docs: {
      description: {
        component:
          '`import { FinExpansionPanelModule } from "@fincloud/ui/expansion-panel"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6708-10488&m=dev',
    },
  },
};
export default meta;

type Story = StoryObj<FinAccordionComponent | any>;

const FinAccordionCategory = {
  category: 'Accordion inputs',
};
const FinAccordionMethodsCategory = {
  category: 'Accordion methods',
};
const FinExpansionPanelCategory = {
  category: 'Expansion panel inputs',
};

export const Default: Story = {
  name: 'Basic usage',
  args: {
    multi: false,
    hideToggle: false,
    togglePosition: FinAccordionTogglePosition.AFTER,
    toggleDirection: FinAccordionToggleDirection.AUTO,
    size: FinSize.S,
    disabled: false,
    expanded: false,
    isSummaryAlwaysVisible: false,
    borderless: false,
    type: FinAccordionType.DEFAULT,
  },
  argTypes: {
    multi: {
      table: {
        ...FinAccordionCategory,
      },
      description:
        'Whether the accordion should allow multiple expanded accordion items simultaneously.',
      control: { type: 'boolean' },
    },
    hideToggle: {
      table: {
        ...FinAccordionCategory,
      },
      description:
        'Show/hide the expansion indicator for all expansion panels.',
      control: { type: 'boolean' },
    },
    togglePosition: {
      table: {
        ...FinAccordionCategory,
        defaultValue: { summary: 'FinAccordionTogglePosition.AFTER' },
      },
      description:
        'The position of the expansion indicator for all expansion panels.',
      options: Object.values(FinAccordionTogglePosition),
      control: { type: 'select' },
    },
    toggleDirection: {
      table: {
        ...FinAccordionCategory,
      },
      description: 'The direction of the expansion indicator.',
      options: Object.values(FinAccordionToggleDirection),
      control: { type: 'select' },
    },
    resetNesting: {
      table: {
        ...FinAccordionCategory,
      },
      description:
        'Reset nesting, useful when nesting `fin-according` in `finContent` template.',
      control: { type: 'boolean' },
    },

    size: {
      table: {
        ...FinAccordionCategory,
      },
      options: [FinSize.L, FinSize.M, FinSize.S],
      control: { type: 'select' },
    },
    type: {
      table: {
        ...FinAccordionCategory,
      },
      options: Object.values(FinAccordionType),
      control: { type: 'select' },
    },
    borderless: {
      table: {
        ...FinAccordionCategory,
        defaultValue: { summary: 'false' },
      },
      control: { type: 'boolean' },
    },
    closeAll: {
      table: {
        ...FinAccordionMethodsCategory,
      },
      description:
        'Closes all enabled accordion items. Used with accordion reference.',
      control: false,
    },
    openAll: {
      table: {
        ...FinAccordionMethodsCategory,
      },
      description:
        'Opens all enabled accordion items in an accordion where multi is enabled.Used with accordion reference.',
      control: false,
    },

    disabled: {
      table: {
        ...FinExpansionPanelCategory,
        defaultValue: { summary: 'false' },
      },
      description: 'Whether the expansion panel is disabled.`',
      control: { type: 'boolean' },
    },
    expanded: {
      table: {
        ...FinExpansionPanelCategory,
        defaultValue: { summary: 'false' },
      },
      description: 'Whether the expansion panel is expanded.',
      control: { type: 'boolean' },
    },
    isSummaryAlwaysVisible: {
      table: {
        ...FinExpansionPanelCategory,
        defaultValue: { summary: 'false' },
      },
      description: 'Whether the summary is always visible or only on hover.',
      control: { type: 'boolean' },
    },
    opened: {
      table: {
        ...FinExpansionPanelCategory,
      },
      description:
        'Event emitted every time the expansion panel item is opened.',
      control: false,
    },
    closed: {
      table: {
        ...FinExpansionPanelCategory,
      },
      description:
        'Event emitted every time the expansion panel item is closed.',
      control: false,
    },
    afterExpand: {
      table: {
        ...FinExpansionPanelCategory,
      },
      description:
        "An event emitted after the body's expansion animation happens.",
      control: false,
    },
    afterCollapse: {
      table: {
        ...FinExpansionPanelCategory,
      },
      description:
        "An event emitted after the body's collapse animation happens.",
      control: false,
    },
    'hideToggle ': {
      table: {
        ...FinExpansionPanelCategory,
        defaultValue: { summary: 'false' },
      },
      description:
        'Show/hide the expansion indicator for the current expansion panel.',
      control: { type: 'boolean' },
    },
    'togglePosition ': {
      table: {
        ...FinExpansionPanelCategory,
        defaultValue: { summary: 'FinAccordionTogglePosition.AFTER' },
      },
      description: 'The position of the expansion indicator.',
      options: Object.values(FinAccordionTogglePosition),
      control: { type: 'select' },
    },
    finTitle: {
      table: {
        ...FinExpansionPanelCategory,
      },
      description: 'Expansion panel title template.',
      defaultValue: { summary: 'attribute' },
    },
    finDescription: {
      table: {
        ...FinExpansionPanelCategory,
      },
      description: 'Expansion panel description template.',
      defaultValue: { summary: 'attribute' },
    },
    finSummary: {
      table: {
        ...FinExpansionPanelCategory,
      },
      description: 'Expansion panel summary template`.',
      defaultValue: { summary: 'attribute' },
    },
    finContent: {
      table: {
        ...FinExpansionPanelCategory,
      },
      description: 'Expansion panel content template.',
      defaultValue: { summary: 'attribute' },
    },
  },
  render: (args) => {
    return {
      props: args,
      template: `
       <fin-accordion
          [multi]="multi"
          [hideToggle]="hideToggle"
          [togglePosition]="togglePosition"
          [toggleDirection]="toggleDirection"
          [size]="size"
          [type]="type"
        >
         <fin-expansion-panel
          [disabled]="disabled"
          [expanded]="expanded"
          #panel
        >
            <ng-template #finTitle>
              With description
            </ng-template>
            <ng-template #finDescription>
              Is expanded: {{ panel.isExpanded }}
            </ng-template>
            <ng-template #finContent>
              Content...
            </ng-template>
          </fin-expansion-panel>

          <fin-expansion-panel>
            <ng-template #finTitle>
              Summary content on hover
            </ng-template>
            <ng-template #finSummary>
              <button fin-button-icon size="s" appearance="stealth" class="fin-me-[0.8rem]">
                <fin-icon name="add"></fin-icon>
              </button>
              <button fin-button-icon size="s" appearance="stealth" class="fin-me-[0.8rem]">
                <fin-icon name="close"></fin-icon>
              </button>
            </ng-template>
            <ng-template #finContent>
              Content...
            </ng-template>
          </fin-expansion-panel>

          <fin-expansion-panel [isSummaryAlwaysVisible]="true">
            <ng-template #finTitle>
              Always visible summary content
            </ng-template>
            <ng-template #finSummary>
              <button fin-button-icon size="s" appearance="stealth" class="fin-me-[0.8rem]">
                <fin-icon name="add"></fin-icon>
              </button>
              <button fin-button-icon size="s" appearance="stealth" class="fin-me-[0.8rem]">
                <fin-icon name="close"></fin-icon>
              </button>
            </ng-template>
            <ng-template #finContent>
              Content...
            </ng-template>
          </fin-expansion-panel>

          <fin-expansion-panel disabled>
            <ng-template #finTitle>
              Disabled expansion panel
            </ng-template>
            <ng-template #finDescription>
              Description text
            </ng-template>
            <ng-template #finContent>
              Content...
            </ng-template>
          </fin-expansion-panel>
        </fin-accordion>
    `,
    };
  },
};

export const AutoHideDescription: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => {
    const exampleText =
      "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.";
    const panels = [
      {
        title: 'Title 1',
        description: exampleText,
        content: exampleText,
        control: new FormControl(),
      },
      {
        title: 'Title 2',
        description: exampleText,
        content: exampleText,
        control: new FormControl(),
      },
    ];

    return {
      props: { args, panels },
      template: `
        <fin-accordion multi togglePosition="before">
          @for (panel of panels; track $index) {
            <fin-expansion-panel #finPanel [isSummaryAlwaysVisible]="true">
              <ng-template #finTitle>
                {{ panel.title }}
              </ng-template>

              @if(!finPanel.isExpanded) {
                <ng-template #finDescription>
                  <div class="fin-w-[40rem]">
                    <div class="fin-truncate">
                      {{ panel.description }}
                    </div>
                  </div>
                </ng-template>
              }

              <ng-template #finContent>
                {{ panel.content }}
              </ng-template>
            </fin-expansion-panel>
          }
      </fin-accordion>
    `,
    };
  },
};

export const CheckBoxStart: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => {
    const exampleText =
      "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.";
    const panels = [
      {
        title: 'Title 1',
        content: exampleText,
        control: new FormControl(),
      },
      {
        title: 'Title 2',
        content: exampleText,
        control: new FormControl(),
      },
    ];

    return {
      props: { args, panels },
      template: `
        <fin-accordion multi togglePosition="after">
          @for (panel of panels; track $index) {
            <fin-expansion-panel #finPanel [isSummaryAlwaysVisible]="true">

              <ng-template #finPrefix>
                  <fin-checkbox
                  [formControl]="panel.control"
                  label=""
                  size="s"
                ></fin-checkbox>
              </ng-template>

              <ng-template #finTitle>
                {{ panel.title }}
              </ng-template>

            <ng-template #finContent>
                {{ panel.content }}
              </ng-template>
            </fin-expansion-panel>
          }
      </fin-accordion>
    `,
    };
  },
};

export const CheckBoxEnd: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => {
    const exampleText =
      "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.";
    const panels = [
      {
        title: 'Title 1',
        content: exampleText,
        control: new FormControl(),
      },
      {
        title: 'Title 2',
        content: exampleText,
        control: new FormControl(),
      },
    ];

    return {
      props: { args, panels },
      template: `
        <fin-accordion multi togglePosition="before">
          @for (panel of panels; track $index) {
            <fin-expansion-panel #finPanel [isSummaryAlwaysVisible]="true">
              <ng-template #finTitle>
                {{ panel.title }}
              </ng-template>

              <ng-template #finSummary>
               <fin-checkbox
                [formControl]="panel.control"
                label=""
                size="s"
              ></fin-checkbox>
              </ng-template>

              <ng-template #finContent>
                {{ panel.content }}
              </ng-template>
            </fin-expansion-panel>
          }
      </fin-accordion>
    `,
    };
  },
};

export const LightType: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: () => {
    return {
      template: `
       <fin-accordion type="light">
         <fin-expansion-panel>
            <ng-template #finTitle>
              Title
            </ng-template>
            <ng-template #finDescription>
              Description
            </ng-template>
            <ng-template #finContent>
              Content
            </ng-template>
          </fin-expansion-panel>

          <fin-expansion-panel>
            <ng-template #finTitle>
              Title
            </ng-template>
            <ng-template #finDescription>
              Description
            </ng-template>
            <ng-template #finContent>
              Content
            </ng-template>
          </fin-expansion-panel>
        </fin-accordion>
    `,
    };
  },
};
export const LightNestedTree: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: () => {
    return {
      template: `
        <fin-accordion type="light" togglePosition="before" multi>
          <fin-expansion-panel>
              <ng-template #finTitle>
                Title
              </ng-template>

              <ng-template #finDescription>
                Parent
              </ng-template>

              <fin-accordion type="light" togglePosition="before" multi>
                  <fin-expansion-panel>
                    <ng-template #finTitle>
                      Title
                    </ng-template>
                    <ng-template #finDescription>
                      Nested
                    </ng-template>      

                    <fin-accordion type="light" togglePosition="before" multi>
                      <fin-expansion-panel>
                        <ng-template #finTitle>
                            Title
                          </ng-template>
                          <ng-template #finDescription>
                            Nested
                          </ng-template>
                          <ng-template #finContent>
                            Content
                          </ng-template>
                        </fin-expansion-panel>
                    </fin-accordion>

                  </fin-expansion-panel>
              </fin-accordion>
            </fin-expansion-panel>

            <fin-expansion-panel>
              <ng-template #finTitle>
                Title
              </ng-template>

              <ng-template #finDescription>
                Parent
              </ng-template>

              <fin-accordion type="light" togglePosition="before" multi>
                <fin-expansion-panel>
                  <ng-template #finTitle>
                      Title
                    </ng-template>
                    <ng-template #finDescription>
                      Nested
                    </ng-template>
                    <ng-template #finContent>
                      Content
                    </ng-template>
                  </fin-expansion-panel>

                  <fin-expansion-panel>
                    <ng-template #finTitle>
                      Title
                    </ng-template>
                    <ng-template #finDescription>
                      Nested
                    </ng-template>                    
                    <ng-template #finContent>
                      Content
                    </ng-template>
                  </fin-expansion-panel>
              </fin-accordion>
            </fin-expansion-panel>
        </fin-accordion>
    `,
    };
  },
};
export const NestedContent: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'The nested `fin-accordion` in `finContent` template is using `resetNesting` attribute.',
      },
    },
  },
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: () => {
    return {
      template: `
        <fin-accordion type="light" multi>
          <fin-expansion-panel>
              <ng-template #finTitle>
                Title
              </ng-template>

              <ng-template #finDescription>
                Parent
              </ng-template>

              <ng-template #finContent>
                <div class="fin-p-5">
                  <div class="fin-columns-2 fin-gap-5 fin-w-full">
                    <div>
                      Lorem ipsum dolor sit amet consectetur adipisicing elit. Rerum officiis, quod corporis recusandae veniam maiores velit? Ad facere doloremque veritatis officiis voluptatum omnis. Commodi dolorem fugit beatae libero impedit corporis!
                    </div>
                    <div>
                      <fin-accordion type="light" multi resetNesting>
                        <fin-expansion-panel>
                          <ng-template #finTitle>
                              Title
                            </ng-template>
                            <ng-template #finDescription>
                              Nested
                            </ng-template>
                            <ng-template #finContent>
                              Content
                            </ng-template>
                          </fin-expansion-panel>
                          <fin-expansion-panel>
                            <ng-template #finTitle>
                              Title
                            </ng-template>
                            <ng-template #finDescription>
                              Nested
                            </ng-template>
                            <ng-template #finContent>
                              Content
                            </ng-template>
                          </fin-expansion-panel>
                      </fin-accordion>
                    </div>
                  </div>
                </div>
              </ng-template>

            </fin-expansion-panel>
        </fin-accordion>
    `,
    };
  },
};
