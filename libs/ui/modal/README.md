# Modal Components

A comprehensive modal system for displaying overlay content, dialogs, and forms. The modal module provides a service-based approach for opening modals with flexible configuration options, content projection, and data injection.

## Installation

```bash
npm install @fincloud/ui
```

```typescript
import { FinModalModule } from '@fincloud/ui/modal';

@Component({
  imports: [FinModalModule],
  // ...
})
export class YourComponent {}
```

## Basic Usage

### Opening a Modal

```typescript
import { Component } from '@angular/core';
import { FinModalService } from '@fincloud/ui/modal';
import { MyModalComponent } from './my-modal.component';

@Component({
  selector: 'app-example',
  template: ` <button (click)="openModal()">Open Modal</button> `,
})
export class ExampleComponent {
  constructor(private modalService: FinModalService) {}

  openModal(): void {
    const modalRef = this.modalService.open(MyModalComponent);

    modalRef.afterClosed().subscribe((result) => {
      console.log('Modal closed with result:', result);
    });
  }
}
```

### Basic Modal Component

```typescript
import { Component } from '@angular/core';
import { FinModalModule } from '@fincloud/ui/modal';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinHeaderAndFooterModule } from '@fincloud/ui/header-and-footer';
import { FinIconModule } from '@fincloud/ui/icon';

@Component({
  selector: 'app-my-modal',
  imports: [FinModalModule, FinButtonModule, FinHeaderAndFooterModule, FinIconModule],
  template: `
    <fin-modal-header>
      <fin-header>
        Modal Title

        <button fin-button-action size="l" fin-modal-close>
          <fin-icon name="close"></fin-icon>
        </button>
      </fin-header>
    </fin-modal-header>

    <fin-modal-content>
      <p>Modal content goes here</p>
    </fin-modal-content>

    <fin-modal-footer>
      <fin-footer>
        <button fin-button fin-modal-close appearance="secondary">Cancel</button>

        <button fin-button appearance="primary" (click)="save()">Save</button>
      </fin-footer>
    </fin-modal-footer>
  `,
})
export class MyModalComponent {
  save(): void {
    // Handle save logic
  }
}
```

## API Reference

### FinModalService

#### Methods

| Name           | Parameters                                                                 | Return Type         | Description                                                  |
| -------------- | -------------------------------------------------------------------------- | ------------------- | ------------------------------------------------------------ |
| `open<R, C>()` | `component: ComponentType<C> \| TemplateRef<C>`, `config?: FinModalConfig` | `FinModalRef<C, R>` | Opens a modal with the specified component and configuration |

### FinModalConfig

Configuration interface for modal options.

#### Properties

| Name           | Type      | Default     | Description                                                        |
| -------------- | --------- | ----------- | ------------------------------------------------------------------ |
| `data`         | `any`     | `undefined` | Data to inject into the modal component                            |
| `size`         | `FinSize` | `FinSize.S` | Size of the modal                                                  |
| `hasBackdrop`  | `boolean` | `true`      | Whether the modal has a backdrop                                   |
| `disableClose` | `boolean` | `false`     | Whether the user can close the modal with escape or backdrop click |

### FinModalRef

Reference to an opened modal.

#### Methods

| Name            | Return Type                  | Description                                    |
| --------------- | ---------------------------- | ---------------------------------------------- |
| `afterClosed()` | `Observable<R \| undefined>` | Observable that emits when the modal is closed |
| `close()`       | `void`                       | Closes the modal                               |
| `updateSize()`  | `void`                       | Updates the modal size                         |

### Directives

#### fin-modal-header

Defines the header section of the modal.

**Selector:** `fin-modal-header`

#### fin-modal-content

Specifies the main content area of the modal.

**Selector:** `fin-modal-content`

#### fin-modal-footer

Defines the footer section of the modal.

**Selector:** `fin-modal-footer`

**Inputs:**
| Name | Type | Default | Description |
|------|------|---------|-------------|
| `separator` | `boolean` | `false` | If true, adds a top border to the footer |

#### fin-modal-close

Directive that adds close functionality to an element inside the modal.

**Selector:** `[fin-modal-close]`

**Inputs:**
| Name | Type | Default | Description |
|------|------|---------|-------------|
| `fin-modal-close` | `any` | `undefined` | Result to return when closing the modal |

#### fin-modal-slot

Slots that can be added inside `fin-modal-slots-container`.

**Selector:** `fin-modal-slot`

**Inputs:**
| Name | Type | Default | Description |
|------|------|---------|-------------|
| `size` | `FinSize.S \| FinSize.M \| FinSize.L \| FinSize.XL` | `FinSize.S` | Size of the modal slot |

### Components

#### FinModalSlotsContainerComponent

Container for organizing modal content into sized slots.

**Selector:** `fin-modal-slots-container`

## Configuration Options

### Modal Sizes

| Size          | Dimensions            | Description                          |
| ------------- | --------------------- | ------------------------------------ |
| `FinSize.S`   | 410px                 | Small modal for simple dialogs       |
| `FinSize.M`   | 600px                 | Medium modal for forms               |
| `FinSize.L`   | 800px                 | Large modal for complex content      |
| `FinSize.XL`  | 1024px                | Extra large modal for detailed views |
| `FinSize.XXL` | 100% width and height | Full screen modal                    |

### Slot Sizes

| Size         | Dimensions | Description     |
| ------------ | ---------- | --------------- |
| `FinSize.S`  | 360px      | Small slot      |
| `FinSize.M`  | 440px      | Medium slot     |
| `FinSize.L`  | 664px      | Large slot      |
| `FinSize.XL` | 100% width | Full width slot |

### Data Injection

Use the `FIN_MODAL_DATA` injection token to access data passed to the modal:

```typescript
import { Inject } from '@angular/core';
import { FIN_MODAL_DATA } from '@fincloud/ui/modal';

@Component({
  // ...
})
export class MyModalComponent {
  constructor(@Inject(FIN_MODAL_DATA) public data: any) {}
}
```

### Modal Reference

Use the `FIN_MODAL_REF` injection token to access the modal reference:

```typescript
import { Inject } from '@angular/core';
import { FIN_MODAL_REF, FinModalRef } from '@fincloud/ui/modal';

@Component({
  // ...
})
export class MyModalComponent {
  constructor(@Inject(FIN_MODAL_REF) private modalRef: FinModalRef<any>) {}

  closeModal(result?: any): void {
    this.modalRef.close(result);
  }
}
```

## Advanced Examples

### Modal with Data Injection

```typescript
// Opening component
export class ParentComponent {
  constructor(private modalService: FinModalService) {}

  openModalWithData(): void {
    const modalRef = this.modalService.open(DataModalComponent, {
      size: FinSize.M,
      data: {
        title: 'Edit User',
        user: { id: 1, name: 'John Doe', email: '<EMAIL>' },
      },
    });

    modalRef.afterClosed().subscribe((result) => {
      if (result) {
        console.log('User updated:', result);
      }
    });
  }
}

// Modal component
@Component({
  selector: 'app-data-modal',
  template: `
    <fin-modal-header>
      <fin-header>
        {{ data.title }}

        <button fin-button-action size="l" fin-modal-close>
          <fin-icon name="close"></fin-icon>
        </button>
      </fin-header>
    </fin-modal-header>

    <fin-modal-content>
      <form [formGroup]="userForm">
        <fin-input label="Name" [formControl]="userForm.controls.name"> </fin-input>

        <fin-input label="Email" [formControl]="userForm.controls.email"> </fin-input>
      </form>
    </fin-modal-content>

    <fin-modal-footer>
      <fin-footer>
        <button fin-button fin-modal-close appearance="secondary">Cancel</button>

        <button fin-button appearance="primary" (click)="save()">Save Changes</button>
      </fin-footer>
    </fin-modal-footer>
  `,
})
export class DataModalComponent {
  userForm = new FormGroup({
    name: new FormControl(this.data.user.name),
    email: new FormControl(this.data.user.email),
  });

  constructor(
    @Inject(FIN_MODAL_DATA) public data: any,
    @Inject(FIN_MODAL_REF) private modalRef: FinModalRef<any>,
  ) {}

  save(): void {
    if (this.userForm.valid) {
      this.modalRef.close({
        ...this.data.user,
        ...this.userForm.value,
      });
    }
  }
}
```

### Modal with Slots

```html
<fin-modal-header>
  <fin-header>
    Complex Layout Modal

    <button fin-button-action size="l" fin-modal-close>
      <fin-icon name="close"></fin-icon>
    </button>
  </fin-header>
</fin-modal-header>

<fin-modal-slots-container>
  <fin-modal-slot [size]="size.S">
    <h3>Sidebar Content</h3>
    <p>Navigation or additional info</p>
  </fin-modal-slot>

  <fin-modal-slot [size]="size.L">
    <h3>Main Content</h3>
    <p>Primary content area with more space</p>
  </fin-modal-slot>
</fin-modal-slots-container>

<fin-modal-footer separator>
  <fin-footer>
    <button fin-button fin-modal-close appearance="secondary">Close</button>

    <button fin-button appearance="primary">Action</button>
  </fin-footer>
</fin-modal-footer>
```

### Confirmation Modal

```typescript
@Component({
  selector: 'app-confirmation-modal',
  template: `
    <fin-modal-content>
      <div class="fin-text-center">
        <fin-icon
          src="/assets/icons/warning.svg"
          class="fin-mx-auto fin-mb-4">
        </fin-icon>

        <h2 class="fin-text-xl fin-font-semibold fin-mb-4">
          {{ data.title }}
        </h2>

        <p class="fin-text-gray-600 fin-mb-6">
          {{ data.message }}
        </p>

        <div class="fin-flex fin-gap-4 fin-justify-center">
          <button fin-button fin-modal-close appearance="secondary">
            {{ data.cancelText || 'Cancel' }}
          </button>

          <button
            fin-button
            appearance="primary"
            [fin-modal-close]="true">
            {{ data.confirmText || 'Confirm' }}
          </button>
        </div>
      </div>
    </fin-modal-content>
  `
})
export class ConfirmationModalComponent {
  constructor(@Inject(FIN_MODAL_DATA) public data: {
    title: string;
    message: string;
    confirmText?: string;
    cancelText?: string;
  }) {}
}

// Usage
openConfirmation(): void {
  const modalRef = this.modalService.open(ConfirmationModalComponent, {
    size: FinSize.S,
    data: {
      title: 'Delete Item',
      message: 'Are you sure you want to delete this item? This action cannot be undone.',
      confirmText: 'Delete',
      cancelText: 'Keep'
    }
  });

  modalRef.afterClosed().subscribe(confirmed => {
    if (confirmed) {
      this.deleteItem();
    }
  });
}
```

## Best Practices

### Do's

- ✅ Use appropriate modal sizes based on content complexity
- ✅ Always provide a way to close the modal (close button or escape key)
- ✅ Use data injection for passing data to modal components
- ✅ Handle modal results with the afterClosed() observable
- ✅ Use semantic HTML structure with header, content, and footer
- ✅ Provide clear action buttons with descriptive text
- ✅ Use slots for complex layouts with multiple content areas
- ✅ Add separators to footers when needed for visual clarity

### Don'ts

- ❌ Don't create modals without close functionality
- ❌ Don't use modals for simple notifications (use toasts instead)
- ❌ Don't nest modals deeply (limit to 2-3 levels)
- ❌ Don't make modals too large for mobile screens
- ❌ Don't forget to handle modal dismissal in parent components
- ❌ Don't use modals for primary navigation

### Accessibility Guidelines

- Ensure modals are keyboard navigable
- Provide proper ARIA labels and roles
- Focus management when opening and closing modals
- Support screen readers with descriptive content
- Ensure sufficient color contrast for all modal elements
- Provide alternative ways to access modal content

### Performance Tips

- Use OnPush change detection strategy in modal components
- Lazy load modal components when possible
- Avoid heavy computations in modal constructors
- Use trackBy functions for lists within modals
- Consider using template refs for simple modals

## Troubleshooting

### Common Issues

#### Issue: Modal not opening

**Solution:** Ensure the FinModalService is properly injected and the component is available.

```typescript
// Correct injection
constructor(private modalService: FinModalService) {}

// Ensure component is imported
@Component({
  imports: [FinModalModule],
  // ...
})
```

#### Issue: Data not being passed to modal

**Solution:** Verify the data is passed in the config and properly injected in the modal component.

```typescript
// Opening modal with data
this.modalService.open(MyModalComponent, {
  data: { key: 'value' }
});

// Injecting data in modal
constructor(@Inject(FIN_MODAL_DATA) public data: any) {}
```

#### Issue: Modal not closing with fin-modal-close

**Solution:** Ensure the directive is properly applied and the modal reference is available.

```html
<!-- Correct usage -->
<button fin-button fin-modal-close>Close</button>
<button fin-button [fin-modal-close]="result">Close with result</button>
```

#### Issue: Slots not displaying correctly

**Solution:** Ensure slots are wrapped in fin-modal-slots-container and have proper size attributes.

```html
<fin-modal-slots-container>
  <fin-modal-slot [size]="size.S">Content</fin-modal-slot>
</fin-modal-slots-container>
```

#### Issue: Modal backdrop not working

**Solution:** Check the hasBackdrop configuration and ensure it's not disabled.

```typescript
this.modalService.open(MyModalComponent, {
  hasBackdrop: true, // Default is true
  disableClose: false, // Allow backdrop close
});
```

## Related Components

- [Button](../button/README.md) - For modal action buttons
- [Icon](../icon/README.md) - For close buttons and visual elements
- [Header and Footer](../header-and-footer/README.md) - For modal header and footer structure
- [Input](../input/README.md) - For form inputs within modals
- [Toast](../toast/README.md) - For simple notifications (alternative to modals)

## Storybook

View interactive examples and documentation in Storybook:

- [Modal Component](https://lib-ui.neoshare.dev/?path=/docs/components-modal--docs)

## Changelog

See [CHANGELOG.md](../../CHANGELOG.md) for version history and breaking changes.

## Contributing

See [CONTRIBUTING.md](../../CONTRIBUTING.md) for guidelines on contributing to this component.

## License

This component is part of the @fincloud/ui library and is licensed under [MIT License](../../LICENSE).
