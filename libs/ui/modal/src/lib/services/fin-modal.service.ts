import {
  ComponentType,
  Overlay,
  OverlayContainer,
  ScrollStrategy,
} from '@angular/cdk/overlay';
import { Location } from '@angular/common';
import { Inject, Injectable, Injector, TemplateRef } from '@angular/core';
import {
  MAT_DIALOG_SCROLL_STRATEGY,
  MatDialog,
} from '@angular/material/dialog';
import { FinModalConfig } from '../models/fin-modal-config';
import { FinModalRef } from '../models/fin-modal-ref';
import { FIN_MODAL_DEFAULT_OPTIONS } from '../utils/fin-modal-injection-token';

@Injectable({ providedIn: 'root' })
export class FinModalService extends MatDialog {
  constructor(
    overlay: Overlay,
    injector: Injector,
    location: Location,
    @Inject(FIN_MODAL_DEFAULT_OPTIONS) defaultOptions: FinModalConfig,
    @Inject(MAT_DIALOG_SCROLL_STRATEGY) scrollStrategy: ScrollStrategy,
    parentDialog: MatDialog,
    overlayContainer: OverlayContainer,
  ) {
    super(
      overlay,
      injector,
      location,
      defaultOptions,
      scrollStrategy,
      parentDialog,
      overlayContainer,
    );
  }

  override open<R, C>(
    component: ComponentType<C> | TemplateRef<C>,
    config?: FinModalConfig,
  ): FinModalRef<C, R> {
    return super.open(component, {
      ...config,
      panelClass: this.getFinModalClasses(config),
    });
  }

  private getFinModalClasses(config?: FinModalConfig) {
    const classes = ['fin-modal'];

    if (config?.size) {
      const { size } = config;
      classes.push(`fin-modal-${size}`);
    } else {
      classes.push('fin-modal-s');
    }

    return classes;
  }
}
