import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinSize } from '@fincloud/ui/types';
import { filter, take } from 'rxjs';
import { FinModalService } from '../../services/fin-modal.service';
import { FinBasicUsageComponent } from '../basic-usage/basic-usage.component';
import { FinConfirmationComponent } from '../confirmation/confirmation.component';
import { FinFullSizeComponent } from '../full-size/full-size.component';
import { FinModalConfigsComponent } from '../modal-configs/modal-configs.component';
import { FinSlotExampleComponent } from '../slot-example/slot-example.component';

@Component({
  selector: 'fin-layout',
  standalone: true,
  imports: [CommonModule, FinButtonModule],
  templateUrl: './layout.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinLayoutComponent {
  @Input() exampleNumber = 1;

  constructor(private modal: FinModalService) {}

  openModal() {
    switch (this.exampleNumber) {
      // Basic Usage
      case 1:
        return this.modal.open(FinBasicUsageComponent);

      // Confirmation Example
      case 2:
        return this.actionAfterConfirmation();

      // Modal Config
      case 3:
        return this.modal.open(FinModalConfigsComponent, {
          data: 'Something provided from outside!',
          size: FinSize.XL,
        });

      // Slot Example
      case 4:
        return this.modal.open(FinSlotExampleComponent, {
          size: FinSize.XL,
        });

      // Full Size
      case 5:
        return this.modal.open(FinFullSizeComponent, {
          size: FinSize.XXL,
        });
    }
  }

  private actionAfterConfirmation() {
    const modalRef = this.modal.open(FinConfirmationComponent);

    modalRef
      .afterClosed()
      .pipe(
        // isConfirmation will come from [fin-modal-close]="true"
        filter((isConfirmation) => !!isConfirmation),
      )
      .subscribe(() => {
        alert('Confirmation button has been pressed!');
      });
  }
}
