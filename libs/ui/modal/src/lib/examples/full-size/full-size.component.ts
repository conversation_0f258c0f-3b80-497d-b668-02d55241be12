import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FinButtonAppearance, FinButtonModule } from '@fincloud/ui/button';
import { FinHeaderAndFooterModule } from '@fincloud/ui/header-and-footer';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';
import { FinModalModule } from '../../modal.module';

@Component({
  selector: 'fin-full-size',
  standalone: true,
  imports: [
    CommonModule,
    FinModalModule,
    FinButtonModule,
    FinIconModule,
    FinHeaderAndFooterModule,
  ],
  templateUrl: './full-size.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinFullSizeComponent {
  size = FinSize;
  appearance = FinButtonAppearance;
}
