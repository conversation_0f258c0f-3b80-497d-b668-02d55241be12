<fin-modal-header>
  <fin-header>
    Header

    <button fin-button-action [size]="size.L" fin-modal-close>
      <fin-icon name="close"></fin-icon>
    </button>
  </fin-header>
</fin-modal-header>

<fin-modal-content>
  {{ providedData }}
</fin-modal-content>

<fin-modal-footer>
  <fin-footer>
    <button fin-button fin-modal-close [appearance]="appearance.SECONDARY">
      Close
    </button>

    <button fin-button [appearance]="appearance.PRIMARY">Action</button>
  </fin-footer>
</fin-modal-footer>
