import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  Inject,
  OnInit,
} from '@angular/core';
import { FinButtonAppearance, FinButtonModule } from '@fincloud/ui/button';
import { FinHeaderAndFooterModule } from '@fincloud/ui/header-and-footer';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinModalModule } from '../../modal.module';
import { FIN_MODAL_DATA } from '../../utils/fin-modal-data-token';
import { FinSize } from '@fincloud/ui/types';

@Component({
  selector: 'fin-modal-configs',
  standalone: true,
  imports: [
    CommonModule,
    FinModalModule,
    FinButtonModule,
    FinIconModule,
    FinHeaderAndFooterModule,
  ],
  templateUrl: './modal-configs.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinModalConfigsComponent implements OnInit {
  size = FinSize;
  appearance = FinButtonAppearance;

  providedData = '';

  constructor(@Inject(FIN_MODAL_DATA) private _providedData: string) {}

  ngOnInit(): void {
    this.providedData = this._providedData;
  }
}
