import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FinButtonAppearance, FinButtonModule } from '@fincloud/ui/button';
import { FinHeaderAndFooterModule } from '@fincloud/ui/header-and-footer';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinModalModule } from '../../modal.module';

@Component({
  selector: 'fin-confirmation',
  standalone: true,
  imports: [
    CommonModule,
    FinModalModule,
    FinButtonModule,
    FinIconModule,
    FinHeaderAndFooterModule,
  ],
  templateUrl: './confirmation.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinConfirmationComponent {
  appearance = FinButtonAppearance;
}
