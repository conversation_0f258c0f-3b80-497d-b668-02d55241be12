import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FinButtonAppearance, FinButtonModule } from '@fincloud/ui/button';
import { FinHeaderAndFooterModule } from '@fincloud/ui/header-and-footer';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinModalModule } from '../../modal.module';
import { FinSize } from '@fincloud/ui/types';

@Component({
  selector: 'fin-basic-usage',
  standalone: true,
  imports: [
    CommonModule,
    FinModalModule,
    FinButtonModule,
    FinIconModule,
    FinHeaderAndFooterModule,
  ],
  templateUrl: './basic-usage.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinBasicUsageComponent {
  size = FinSize;
  appearance = FinButtonAppearance;
}
