import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FinButtonAppearance, FinButtonModule } from '@fincloud/ui/button';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';
import { FinModalModule } from '../../modal.module';
import { FinHeaderAndFooterModule } from '@fincloud/ui/header-and-footer';

@Component({
  selector: 'fin-slot-example',
  standalone: true,
  imports: [
    CommonModule,
    FinModalModule,
    FinButtonModule,
    FinIconModule,
    FinHeaderAndFooterModule,
  ],
  templateUrl: './slot-example.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinSlotExampleComponent {
  size = FinSize;
  appearance = FinButtonAppearance;
}
