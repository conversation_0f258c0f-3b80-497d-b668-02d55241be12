import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FinModalSlotsContainerComponent } from './components/fin-modal-slots-container/fin-modal-slots-container.component';
import { FinModalCloseDirective } from './directives/fin-modal-close.directive';
import { FinModalContentDirective } from './directives/fin-modal-content.directive';
import { FinModalFooterDirective } from './directives/fin-modal-footer.directive';
import { FinModalHeaderDirective } from './directives/fin-modal-header.directive';
import { FinModalSlotDirective } from './directives/fin-modal-slot.directive';
import { FinModalService } from './services/fin-modal.service';
import { FIN_MODAL_DEFAULT_OPTIONS } from './utils/fin-modal-injection-token';

@NgModule({
  imports: [
    CommonModule,
    FinModalSlotsContainerComponent,
    FinModalHeaderDirective,
    FinModalCloseDirective,
    FinModalContentDirective,
    FinModalFooterDirective,
    FinModalSlotDirective,
  ],
  exports: [
    FinModalSlotsContainerComponent,
    FinModalHeaderDirective,
    FinModalCloseDirective,
    FinModalContentDirective,
    FinModalFooterDirective,
    FinModalSlotDirective,
  ],
  providers: [
    FinModalService,
    { provide: FIN_MODAL_DEFAULT_OPTIONS, useValue: {} },
  ],
})
export class FinModalModule {}
