import { Directive, ElementRef } from '@angular/core';
import {
  MatDialog,
  MatDialogRef,
  MatDialogTitle,
} from '@angular/material/dialog';

@Directive({
  // eslint-disable-next-line @angular-eslint/directive-selector
  selector: 'fin-modal-header',
  standalone: true,
  host: {
    class: 'fin-modal-header',
  },
})
export class FinModalHeaderDirective extends MatDialogTitle {
  // The constructor is necessary because this component extends MatDialogTitle.
  // Without it, Storybook throws an error due to missing dependencies.
  // To be removed in feature versions.
  constructor(
    dialogRef: MatDialogRef<unknown>,
    _elementRef: ElementRef<HTMLElement>,
    _dialog: MatDialog,
  ) {
    super(dialogRef, _elementRef, _dialog);
  }
}
