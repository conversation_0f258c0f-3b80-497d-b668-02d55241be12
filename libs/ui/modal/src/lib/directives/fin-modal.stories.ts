import { CommonModule } from '@angular/common';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinIconModule } from '@fincloud/ui/icon';
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { FinLayoutComponent } from '../examples/layout/layout.component';
import { FinModalModule } from '../modal.module';

const meta: Meta = {
  title: 'Components/Modal',
  parameters: {
    docs: {
      description: {
        component: '`import { FinModalModule } from "@fincloud/ui/modal"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6547-21485&m=dev',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        FinModalModule,
        FinIconModule,
        FinButtonModule,
        FinLayoutComponent,
      ],
    }),
  ],
};

export default meta;
type Story = StoryObj;

export const BasicUsage: Story = {
  argTypes: {
    FinModalConfig: {
      description:
        'Configuration that can be provided as the second (optional) parameter to the `open()` method.<br/><br/>`data`: Data being injected into the child component (`@Inject(FIN_MODAL_DATA) private data: <T>`).<br/><br/>`size: FinSize`: The size of the modal<br/>`S`: 410px,<br/>`M`: 600px,<br/>`L`: 800px,<br/>`XL`: 1024px,<br/>`XXl`: 100% width and height.<br/><br/>`hasBackdrop: boolean`: Whether the modal has a backdrop.<br/><br/>`disableClose: boolean`: Whether the user can use escape or clicking on the backdrop to close the modal.',
      control: false,
      table: {
        category: 'Modal configuration',
      },
    },

    open: {
      description: 'Opens a modal dialog containing the given component.',
      control: false,
      table: {
        category: 'Modal service methods',
      },
    },
    afterOpened: {
      description:
        'Gets an observable that is notified when the modal is finished opening.',
      control: false,
      table: {
        category: 'Modal service methods',
      },
    },
    afterClosed: {
      description:
        'Gets an observable that is notified when the modal is finished closing.',
      control: false,
      table: {
        category: 'Modal service methods',
      },
    },
    closeAll: {
      description: 'Closes all of the currently-open modals.',
      control: false,
      table: {
        category: 'Modal service methods',
      },
    },

    'fin-modal-header': {
      description: 'Defines the header section of the modal.',
      control: false,
      table: {
        category: 'Directives',
      },
    },
    'fin-modal-content': {
      description: 'Specifies the main content area of the modal.',
      control: false,
      table: {
        category: 'Directives',
      },
    },
    'fin-modal-footer': {
      description:
        'Defines the footer section of the modal.<br/><br/>`@Input({ transform: booleanAttribute }) separator`: If set to `true`, a top border will be applied.',
      control: false,
      table: {
        category: 'Directives',
      },
    },
    'fin-modal-slot': {
      description:
        'Slots that can be added inside `<fin-modal-slots-container></fin-modal-slots-container>` component.<br/><br/>`@Input() size: FinSize`:<br/>`S`: 360px<br/>`M`: 440px<br/>`L`: 664px<br/>`XL`: width 100%',
      control: false,
      table: {
        category: 'Directives',
      },
    },
    '[fin-modal-close]': {
      description:
        'Directive that adds a close functionality to an element inside the modal.',
      control: false,
      table: {
        category: 'Directives',
      },
    },
  },
  parameters: {
    docs: {
      source: {
        code: `
          <fin-modal-header>
            <fin-header>
              Header

              <button fin-button-action size="l" fin-modal-close>
                <fin-icon name="close"></fin-icon>
              </button>
            </fin-header>
          </fin-modal-header>

          <fin-modal-content>
            <img
              class="fin-mx-auto fin-mb-[0.5rem]"
              src="/assets/storybook/icons/modal-icon-1.svg"
              alt="modal icon"
            />

            <div
              class="fin-text-[1.6rem] fin-leading-[2.4rem] fin-font-semibold fin-text-color-text-primary fin-text-center fin-mb-[2.4rem]"
            >
              Sind Sie der Meinung, dass Neoloan das beste Fintech-Unternehmen auf dem
              Markt ist?
            </div>

            <div
              class="fin-text-[1.2rem] fin-leading-[1.8rem] fin-text-center fin-text-color-text-tertiary"
            >
              <div class="fin-mb-[2.4rem]">
                Klicken Sie auf Weiter, wenn Sie sich sicher sind, dass Sie die Gruppen
                für den Teilnehmer sichtbar machen wollen und die Informationen mit ihm
                teilen möchten, sobald er Fallinhaber dieses Finanzierungsfalls ist.
              </div>

              <div>
                Klicken Sie auf Überprüfen, um die betreffenden Gruppen einzusehen.
              </div>
            </div>
          </fin-modal-content>

          <fin-modal-footer separator>
            <fin-footer>
              <button fin-button fin-modal-close [appearance]="appearance.SECONDARY">
                Close
              </button>

              <button fin-button [appearance]="appearance.PRIMARY">Action</button>
            </fin-footer>
          </fin-modal-footer>
        `,
      },
    },
  },
  render: () => ({
    template: `
      <fin-layout [exampleNumber]="1"></fin-layout>
    `,
  }),
};

export const ConfirmationExample: Story = {
  argTypes: {
    ...BasicUsage.args,
  },
  parameters: {
    docs: {
      source: {
        code: `
          <fin-modal-content>
            <fin-icon
              src="/assets/storybook/icons/modal-icon-1.svg"
              class="fin-w-auto fin-h-auto fin-mx-auto fin-mb-[0.5rem]"
            ></fin-icon>

            <div
              class="fin-text-[1.6rem] fin-leading-[2.4rem] fin-font-semibold fin-text-color-text-primary fin-text-center fin-mb-[2.4rem]"
            >
              Sind Sie der Meinung, dass Neoloan das beste Fintech-Unternehmen auf dem
              Markt ist?
            </div>

            <div
              class="fin-text-[1.2rem] fin-leading-[1.8rem] fin-text-center fin-text-color-text-tertiary"
            >
              <div class="fin-mb-[2.4rem]">
                Klicken Sie auf Weiter, wenn Sie sich sicher sind, dass Sie die Gruppen
                für den Teilnehmer sichtbar machen wollen und die Informationen mit ihm
                teilen möchten, sobald er Fallinhaber dieses Finanzierungsfalls ist.
              </div>

              <div>
                Klicken Sie auf Überprüfen, um die betreffenden Gruppen einzusehen.
              </div>
            </div>
          </fin-modal-content>

          <fin-modal-footer>
            <fin-footer>
              <button fin-button fin-modal-close [appearance]="appearance.SECONDARY">
                Close
              </button>

              <button
                fin-button
                [fin-modal-close]="true"
                [appearance]="appearance.PRIMARY"
                attention
              >
                Action
              </button>
            </fin-footer>
          </fin-modal-footer>
        `,
      },
    },
  },
  render: () => ({
    template: `
      <fin-layout [exampleNumber]="2"></fin-layout>
    `,
  }),
};

export const ModalConfig: Story = {
  name: 'Modal Config: data, size, etc.',
  argTypes: {
    ...BasicUsage.args,
  },
  parameters: {
    docs: {
      source: {
        code: `
          <fin-modal-header>
            <fin-header>
              Header

              <button fin-button-action size="l" fin-modal-close>
                <fin-icon name="close"></fin-icon>
              </button>
            </fin-header>
          </fin-modal-header>

          <fin-modal-content>
            {{ providedData }}
          </fin-modal-content>

          <fin-modal-footer>
            <fin-footer>
              <button fin-button fin-modal-close [appearance]="appearance.SECONDARY">
                Close
              </button>

              <button fin-button [appearance]="appearance.PRIMARY">Action</button>
            </fin-footer>
          </fin-modal-footer>
        `,
      },
    },
  },
  render: () => ({
    template: `
      <fin-layout [exampleNumber]="3"></fin-layout>
    `,
  }),
};

export const SlotExample: Story = {
  argTypes: {
    ...BasicUsage.args,
  },
  parameters: {
    docs: {
      source: {
        code: `
          <fin-modal-header>
            <fin-header>
              Header

              <button fin-button-action size="l" fin-modal-close>
                <fin-icon name="close"></fin-icon>
              </button>
            </fin-header>
          </fin-modal-header>

          <fin-modal-slots-container>
            <fin-modal-slot [size]="size.S">Slot size S</fin-modal-slot>
            <fin-modal-slot [size]="size.L">Slot size L</fin-modal-slot>
          </fin-modal-slots-container>

          <fin-modal-footer separator>
            <fin-footer>
              <button fin-button fin-modal-close [appearance]="appearance.SECONDARY">
                Close
              </button>

              <button fin-button [appearance]="appearance.PRIMARY">Action</button>
            </fin-footer>
          </fin-modal-footer>
        `,
      },
    },
  },
  render: () => ({
    template: `
      <fin-layout [exampleNumber]="4"></fin-layout>
    `,
  }),
};

export const FullSize: Story = {
  argTypes: {
    ...BasicUsage.args,
  },
  parameters: {
    docs: {
      source: {
        code: `
          <fin-modal-header>
            <fin-header>
              <div class="fin-w-full">
                <div class="fin-flex fin-justify-between">
                  Header

                  <button fin-button-action size="l" fin-modal-close>
                    <fin-icon name="close"></fin-icon>
                  </button>
                </div>

                <div class="fin-flex fin-items-center fin-gap-[0.8rem]">
                  <span class="fin-text-body-3-moderate">
                    DEQ-309-6f40&#64;dataroom.neoshare.de
                  </span>
                  <button fin-button-action size="l">
                    <fin-icon name="content_copy"></fin-icon>
                  </button>
                </div>
              </div>
            </fin-header>
          </fin-modal-header>

          <fin-modal-slots-container>
            <fin-modal-slot [size]="size.XL">
              Custom content that uses the full size.
            </fin-modal-slot>
          </fin-modal-slots-container>
        `,
      },
    },
  },
  render: () => ({
    template: `
      <fin-layout [exampleNumber]="5"></fin-layout>
    `,
  }),
};
