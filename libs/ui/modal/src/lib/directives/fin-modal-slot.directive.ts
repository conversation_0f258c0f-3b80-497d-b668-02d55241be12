import { Directive, Input } from '@angular/core';
import { FinSize } from '@fincloud/ui/types';

@Directive({
  // eslint-disable-next-line @angular-eslint/directive-selector
  selector: 'fin-modal-slot',
  standalone: true,
  host: {
    class: 'fin-modal-slot',
    '[class]': 'modalSlotClasses',
  },
})
export class FinModalSlotDirective {
  // If set to `true` the top border will be set.
  @Input() size: FinSize.S | FinSize.M | FinSize.L | FinSize.XL = FinSize.S;

  protected get modalSlotClasses(): string {
    return `fin-modal-slot-${this.size}`;
  }
}
