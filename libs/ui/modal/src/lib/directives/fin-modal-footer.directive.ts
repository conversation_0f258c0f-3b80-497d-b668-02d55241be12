import { booleanAttribute, Directive, ElementRef, Input } from '@angular/core';
import {
  MatDialog,
  MatDialogActions,
  MatDialogRef,
} from '@angular/material/dialog';

@Directive({
  // eslint-disable-next-line @angular-eslint/directive-selector
  selector: 'fin-modal-footer',
  standalone: true,
  host: {
    '[class]': 'footerClasses',
  },
})
export class FinModalFooterDirective extends MatDialogActions {
  // If set to `true` the top border will be set.
  @Input({ transform: booleanAttribute }) separator = false;

  protected get footerClasses(): string {
    let classes = 'fin-modal-footer';

    if (this.separator) {
      classes += ' fin-border-t fin-border-t-color-border-default-inactive';
    }

    return classes;
  }

  // The constructor is necessary because this component extends MatDialogActions.
  // Without it, Storybook throws an error due to missing dependencies.
  // To be removed in feature versions.
  constructor(
    dialogRef: MatDialogRef<unknown>,
    _elementRef: ElementRef<HTMLElement>,
    _dialog: MatDialog,
  ) {
    super(dialogRef, _elementRef, _dialog);
  }
}
