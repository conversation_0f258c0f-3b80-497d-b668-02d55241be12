import {
  AfterViewInit,
  Directive,
  ElementRef,
  Input,
  Renderer2,
} from '@angular/core';
import {
  MatDialog,
  MatDialogClose,
  MatDialogRef,
} from '@angular/material/dialog';

@Directive({
  // eslint-disable-next-line @angular-eslint/directive-selector
  selector: '[fin-modal-close]',
  standalone: true,
})
export class FinModalCloseDirective
  extends MatDialogClose
  implements AfterViewInit
{
  // Overrides

  /** Dialog close input. */
  @Input('fin-modal-close') override dialogResult: unknown;

  // The constructor is necessary because this component extends MatDialogClose.
  // Without it, Storybook throws an error due to missing dependencies.
  // To be removed in feature versions.
  constructor(
    dialogRef: MatDialogRef<unknown>,
    private elementRef: ElementRef<HTMLElement>,
    _dialog: MatDialog,
    private renderer: Renderer2,
  ) {
    super(dialogRef, elementRef, _dialog);
  }

  // TODO: To be refactored, this is a temporary fix
  // that prevents triggering input blur validations when clicking close button.
  ngAfterViewInit() {
    this.renderer.listen(
      this.elementRef.nativeElement,
      'mousedown',
      (event: MouseEvent) => {
        event.preventDefault();
      },
    );
  }
}
