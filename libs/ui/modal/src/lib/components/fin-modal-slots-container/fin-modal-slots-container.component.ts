import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FinModalContentDirective } from '../../directives/fin-modal-content.directive';

@Component({
  selector: 'fin-modal-slots-container',
  standalone: true,
  imports: [CommonModule, FinModalContentDirective],
  templateUrl: './fin-modal-slots-container.component.html',
  styleUrl: './fin-modal-slots-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'fin-modal-slots-container',
  },
})
export class FinModalSlotsContainerComponent {}
