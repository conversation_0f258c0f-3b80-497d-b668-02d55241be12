/* eslint-disable @fincloud/ns/no-declare-interface */
import { CommonModule } from '@angular/common';
import {
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  numberAttribute,
  Output,
} from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatSliderModule } from '@angular/material/slider';
import { FinExecuteFuncPipe } from '@fincloud/utils/pipes';

/**
 * A component that allows the user to select a single value or a range of values using a slider.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-range-slider--docs Storybook Reference}
 */

@Component({
  selector: 'fin-range-slider',
  standalone: true,
  imports: [
    CommonModule,
    MatSliderModule,
    ReactiveFormsModule,
    FinExecuteFuncPipe,
  ],
  templateUrl: './range-slider.component.html',
  styleUrl: './range-slider.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    '[class]': 'rangeSliderCssClasses',
  },
})
export class FinRangeSliderComponent {
  /** Start form control. */
  @Input({ required: true }) startThumbControl!: FormControl<number | null>;

  /** End form control. */
  @Input({ required: true }) endThumbControl!: FormControl<number | null>;

  /** The minimum value that the slider can have. */
  @Input({ transform: numberAttribute }) min = 0;

  /** The maximum value that the slider can have. */
  @Input({ transform: numberAttribute }) max = 100;

  /** The values at which the thumb will snap. */
  @Input({ transform: numberAttribute }) step = 1;

  /** Whether the slider is disabled. */
  @Input({ transform: booleanAttribute }) disabled = false;

  /** Whether ti display tooltip with the current value */
  @Input({ transform: booleanAttribute }) showTooltip = false;

  /** The showTooltip will be always visible. */
  @Input({ transform: booleanAttribute }) tooltipAlwaysVisible = false;

  /**
   * Function that will be used to format the value before it is displayed in the thumb label. */
  @Input() displayWith: (value: number) => string = (value: number) =>
    `${value}`;

  /** Event emitted when the start slider thumb starts being dragged. */
  @Output() startThumbDragStart = new EventEmitter<number | null>();

  /** Event emitted when the start slider thumb stops being dragged. */
  @Output() startThumbDragEnd = new EventEmitter<number | null>();

  /** Event emitted when the end slider thumb starts being dragged. */
  @Output() endThumbDragStart = new EventEmitter<number | null>();

  /** Event emitted when the end slider thumb stops being dragged. */
  @Output() endThumbDragEnd = new EventEmitter<number | null>();

  protected onStartThumbDragStart() {
    this.startThumbDragStart.emit(this.startThumbControl.value);
  }

  protected onStartThumbDragEnd() {
    this.startThumbDragEnd.emit(this.startThumbControl.value);
  }

  protected onEndThumbDragStart() {
    this.endThumbDragStart.emit(this.endThumbControl.value);
  }

  protected onEndThumbDragEnd() {
    this.endThumbDragEnd.emit(this.endThumbControl.value);
  }

  protected get rangeSliderCssClasses(): string {
    let classes = 'fin-range-slider';

    if (this.tooltipAlwaysVisible) {
      classes += ' fin-range-slider-tooltips-visible';
    }

    return classes;
  }
}
