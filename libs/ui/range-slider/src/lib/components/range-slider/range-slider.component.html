<mat-slider
  class="fin-w-full fin-mx-0"
  [min]="min"
  [max]="max"
  [step]="step"
  [disabled]="disabled"
  [discrete]="showTooltip || tooltipAlwaysVisible"
  [displayWith]="displayWith"
>
  <input
    [formControl]="startThumbControl"
    matSliderStartThumb
    (dragStart)="onStartThumbDragStart()"
    (dragEnd)="onStartThumbDragEnd()"
  />

  <input
    [formControl]="endThumbControl"
    matSliderEndThumb
    (dragStart)="onEndThumbDragStart()"
    (dragEnd)="onEndThumbDragEnd()"
  />
</mat-slider>

<div class="fin-flex fin-justify-between -fin-mx-2">
  <div
    class="fin-rounded-[0.4rem] fin-px-[0.8rem] fin-py-[0.4rem] fin-mt-[0.8rem] fin-text-body-3-strong fin-text-color-text-secondary fin-bg-color-background-tertiary-minimal"
  >
    {{ min | finExecuteFunc: displayWith }}
  </div>

  <div
    class="fin-rounded-[0.4rem] fin-px-[0.8rem] fin-py-[0.4rem] fin-mt-[0.8rem] fin-text-body-3-strong fin-text-color-text-secondary fin-bg-color-background-tertiary-minimal"
  >
    {{ max | finExecuteFunc: displayWith }}
  </div>
</div>
