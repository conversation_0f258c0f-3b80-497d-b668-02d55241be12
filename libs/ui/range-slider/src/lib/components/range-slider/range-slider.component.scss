:host.fin-range-slider {
  &::ng-deep {
    --mdc-slider-inactive-track-color: theme(
      'colors.color-background-disabled'
    );
    --mdc-slider-active-track-color: theme(
      'colors.color-background-secondary-strong'
    );
    --mdc-slider-handle-color: theme(
      'colors.color-background-secondary-strong'
    );
    --mdc-slider-focus-handle-color: theme(
      'colors.color-background-secondary-strong'
    );
    --mdc-slider-handle-width: 1.6rem;
    --mdc-slider-handle-height: 1.6rem;
    --mdc-slider-handle-elevation: none;
    --mdc-slider-label-container-color: theme(
      'colors.color-background-tertiary-strong'
    );

    display: block;

    .mat-mdc-slider {
      .mdc-slider {
        &__track--inactive {
          opacity: 1;
        }

        &__value-indicator {
          opacity: 1;
          padding: 0.8rem;

          &-text {
            @apply fin-text-body-3-moderate;
            text-wrap: nowrap;
          }
          &-container {
            bottom: 3rem;
          }
        }

        &__thumb-knob {
          &::before {
            content: '';
            width: 1rem;
            height: 1rem;
            background-color: theme('colors.white');
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }

      .mat-ripple-element {
        display: none;
      }
    }

    .mat-mdc-slider,
    .mdc-slider__input,
    .mdc-slider .mdc-slider__thumb {
      height: 1.6rem;
    }
  }
  &-tooltips-visible::ng-deep {
    .mat-mdc-slider {
      .mdc-slider__value-indicator {
        transform: scale(1) !important;
      }
    }
  }
}
