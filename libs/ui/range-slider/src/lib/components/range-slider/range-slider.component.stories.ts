import { CommonModule } from '@angular/common';
import { FormControl } from '@angular/forms';
import {
  componentWrapperDecorator,
  moduleMetadata,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { FinRangeSliderModule } from '../../range-slider.module';
import { FinRangeSliderComponent } from './range-slider.component';

const meta: Meta = {
  component: FinRangeSliderComponent,
  title: 'Components/Range Slider',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinRangeSliderModule } from "@fincloud/ui/range-slider"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6150-693&m=dev',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [CommonModule, FinRangeSliderModule],
    }),
    componentWrapperDecorator(
      (story) => `<div class="fin-w-[60rem] fin-mt-[2rem]">${story}</div>`,
    ),
  ],
};
export default meta;
type Story = StoryObj<FinRangeSliderComponent>;

const startControl = new FormControl(0);
const endControl = new FormControl(50);
const template = `
  <fin-range-slider
    [min]="min"
    [max]="max"
    [step]="step"
    [disabled]="disabled"
    [showTooltip]="showTooltip"
    [tooltipAlwaysVisible]="tooltipAlwaysVisible"
    [startThumbControl]="startControl"
    [endThumbControl]="endControl"
  ></fin-range-slider>
`;

export const Default: Story = {
  args: {
    min: 0,
    max: 100,
    step: 1,
    disabled: false,
    showTooltip: false,
    tooltipAlwaysVisible: false,
  },
  argTypes: {
    displayWith: {
      control: false,
    },
    startThumbControl: {
      control: false,
    },
    endThumbControl: {
      control: false,
    },
    startThumbDragStart: {
      control: false,
    },
    startThumbDragEnd: {
      control: false,
    },
    endThumbDragStart: {
      control: false,
    },
    endThumbDragEnd: {
      control: false,
    },
  },
  render: (args) => ({
    props: {
      ...args,
      startControl: startControl,
      endControl: endControl,
    },
    template,
  }),
};

export const Disabled: Story = {
  args: {
    ...Default.args,
    disabled: true,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: {
      ...args,
      startControl: startControl,
      endControl: endControl,
    },
    template,
  }),
};

export const TooltipOnHover: Story = {
  args: {
    ...Default.args,
    showTooltip: true,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: {
      ...args,
      startControl: startControl,
      endControl: endControl,
    },
    template,
  }),
};

export const AlwaysVisibleTooltip: Story = {
  args: {
    ...Default.args,
    tooltipAlwaysVisible: true,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: {
      ...args,
      startControl: startControl,
      endControl: endControl,
    },
    template,
  }),
};

export const CustomDisplayWithFunctionEx1: Story = {
  args: {
    ...Default.args,
    tooltipAlwaysVisible: true,
    displayWith: (value: number) => {
      return value.toFixed(2);
    },
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: {
      ...args,
      startControl: startControl,
      endControl: endControl,
    },
    template: `
      <fin-range-slider
        tooltipAlwaysVisible
        [displayWith]="displayWith"
        [startThumbControl]="startControl"
        [endThumbControl]="endControl"
      ></fin-range-slider>
    `,
  }),
};

export const CustomDisplayWithFunctionEx2: Story = {
  args: {
    ...Default.args,
    tooltipAlwaysVisible: true,
    step: 1000,
    min: 1000,
    max: 100000,
    displayWith: (value: number) => {
      if (value >= 1000) {
        return Math.round(value / 1000) + 'k';
      }

      return `${value}`;
    },
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: {
      ...args,
      startControl: new FormControl(1000),
      endControl: new FormControl(30000),
    },
    template: `
      <fin-range-slider
        tooltipAlwaysVisible
        [min]="min"
        [max]="max"
        [displayWith]="displayWith"
        [startThumbControl]="startControl"
        [endThumbControl]="endControl"
      ></fin-range-slider>
    `,
  }),
};
