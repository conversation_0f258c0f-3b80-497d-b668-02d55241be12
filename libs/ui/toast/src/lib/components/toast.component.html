<ng-template #toastTemplate let-data>
  <div class="fin-flex fin-items-center fin-gap-[1.2rem]">
    <div class="fin-grid fin-rounded-full fin-bg-color-border-default-light/20">
      <mat-spinner
        class="[grid-area:stack]"
        diameter="36"
        strokeWidth="3"
        mode="determinate"
        [value]="progressValue$ | async"
      ></mat-spinner>
      <fin-icon
        class="[grid-area:stack] fin-text-color-icons-light"
        [size]="data.iconType === finIconType.INFO ? finSize.M : finSize.L"
        [name]="data.iconType"
      ></fin-icon>
    </div>

    <div
      class="fin-text-body-2-moderate fin-leading-[1.8rem] fin-text-color-text-light"
    >
      {{ data.message }}
    </div>
  </div>
</ng-template>
