import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { FinBasicComponent } from '../examples/basic/basic.component';
import { FinToastComponent } from './toast.component';

const meta: Meta<FinToastComponent> = {
  title: 'Components/Toast',
  component: FinToastComponent,
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinToastModule, FinToastService } from "@fincloud/ui/toast"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6547-20615&m=dev',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [FinBasicComponent],
    }),
  ],
};

export default meta;

type Story = StoryObj;

export const Success: Story = {
  argTypes: {
    FinToastService: {
      description:
        'Service used to programmatically trigger toast notifications.',
      control: false,
    },
  },
  parameters: {
    docs: {
      source: {
        code: ``,
      },
    },
  },
  render: () => ({
    template: `
      <fin-basic [toast]="{type: 'success', message: 'Success message'}"></fin-basic>
    `,
  }),
};

export const Error: Story = {
  argTypes: {
    ...Success.argTypes,
  },
  parameters: {
    ...Success.parameters,
  },
  render: () => ({
    template: `
      <fin-basic [toast]="{type: 'error', message: 'Error message'}"></fin-basic>
    `,
  }),
};

export const Info: Story = {
  argTypes: {
    ...Success.argTypes,
  },
  parameters: {
    ...Success.parameters,
  },
  render: () => ({
    template: `
      <fin-basic [toast]="{type: 'info', message: 'Info message'}"></fin-basic>
    `,
  }),
};
