import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import {
  ChangeDetectionStrategy,
  Component,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';
import { FinAngularMaterialModule } from '@fincloud/utils/angular-material';
import { interval, map, takeWhile } from 'rxjs';
import { FinToastIconType } from '../enums/fin-toast-icon-type';
import { FinToast } from '../models/fin-toast';
import { FinToastService } from '../services/toast.service';

/**
 * A small, temporary notification message.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-toast--docs Storybook Reference}
 */
@Component({
  selector: 'fin-toast',
  standalone: true,
  imports: [
    CommonModule,
    MatProgressSpinnerModule,
    FinAngularMaterialModule,
    HttpClientModule,
    FinIconModule,
  ],
  templateUrl: './toast.component.html',
  styleUrl: './toast.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinToastComponent implements OnInit {
  @ViewChild('toastTemplate', { static: true })
  private toastTemplate!: TemplateRef<FinToast>;
  protected finSize = FinSize;
  protected finIconType = FinToastIconType;

  protected progressValue$ = interval(30).pipe(
    map((val) => val + 1),
    takeWhile((val) => val <= 100),
  );

  constructor(private toastService: FinToastService) {}

  ngOnInit(): void {
    this.toastService.templateRef = this.toastTemplate;
  }
}
