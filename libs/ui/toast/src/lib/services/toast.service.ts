import { Injectable, TemplateRef, ViewContainerRef } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FinToastIconType } from '../enums/fin-toast-icon-type';
import { FinToastType } from '../enums/fin-toast-type';
import { FinToast } from '../models/fin-toast';

@Injectable({ providedIn: 'root' })
export class FinToastService {
  templateRef!: TemplateRef<FinToast>;

  constructor(private snackBar: MatSnackBar) {}

  show(toast: FinToast, viewContainerRef?: ViewContainerRef): void {
    const { type, message } = toast;

    this.snackBar.openFromTemplate(this.templateRef, {
      panelClass: ['fin-toast', type],
      verticalPosition: 'top',
      duration: 3000,
      data: {
        message,
        iconType: this.getIconType(type),
      },
      viewContainerRef,
    });
  }

  private getIconType(toastType: FinToastType): string {
    return FinToastIconType[
      toastType.toUpperCase() as keyof typeof FinToastIconType
    ];
  }
}
