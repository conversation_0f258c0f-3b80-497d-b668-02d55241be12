import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinToast } from '../../models/fin-toast';
import { FinToastService } from '../../services/toast.service';
import { FinToastModule } from '../../toast.module';

@Component({
  selector: 'fin-basic',
  standalone: true,
  imports: [CommonModule, FinToastModule, FinButtonModule],
  templateUrl: './basic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinBasicComponent {
  @Input() toast!: FinToast;

  constructor(private finToastService: FinToastService) {}

  openToast() {
    this.finToastService.show(this.toast);
  }
}
