import {
  FullscreenOverlayContainer,
  OverlayContainer,
} from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FinToastComponent } from './components/toast.component';

@NgModule({
  imports: [CommonModule, FinToastComponent],
  exports: [FinToastComponent],
  providers: [
    { provide: OverlayContainer, useClass: FullscreenOverlayContainer },
  ],
})
export class FinToastModule {}
