import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';
import { FinBadgeAppComponent } from './components/badge-app/badge-app.component';
import { FinBadgeIconComponent } from './components/badge-icon/badge-icon.component';
import { FinBadgeIndicatorComponent } from './components/badge-indicator/badge-indicator.component';
import { FinBadgeInvitationComponent } from './components/badge-invitation/badge-invitation.component';
import { FinBadgeStatusComponent } from './components/badge-status/badge-status.component';

@NgModule({
  imports: [
    CommonModule,
    FinBadgeIndicatorComponent,
    FinBadgeInvitationComponent,
    FinBadgeStatusComponent,
    FinBadgeIconComponent,
    FinBadgeAppComponent,
  ],
  exports: [
    FinBadgeIndicatorComponent,
    FinBadgeInvitationComponent,
    FinBadgeStatusComponent,
    FinBadgeIconComponent,
    FinBadgeAppComponent,
  ],
})
export class FinBadgesModule {
  constructor(
    private iconRegistry: MatIconRegistry,
    private sanitizer: DomSanitizer,
  ) {
    this.iconRegistry.addSvgIcon(
      'icon-badge',
      this.sanitizer.bypassSecurityTrustResourceUrl(
        '/assets/badges/icon-badge.svg',
      ),
      {
        viewBox: '0 0 24 8',
      },
    );
  }
}
