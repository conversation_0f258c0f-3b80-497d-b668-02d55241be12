::ng-deep {
  .fin-badge-app {
    &-success {
      path:first-of-type {
        fill: theme('colors.color-background-success-subtle');
      }

      path:last-of-type {
        fill: theme('colors.color-icons-success');
      }
    }
    &-error {
      path:first-of-type {
        fill: theme('colors.color-background-attention-subtle');
      }

      path:last-of-type {
        fill: theme('colors.color-icons-error');
      }
    }
    &-pending {
      path:first-of-type {
        fill: theme('colors.color-background-warning-subtle');
      }

      path:last-of-type {
        fill: theme('colors.color-icons-warning');
      }
    }
    &-information {
      path:first-of-type {
        fill: theme('colors.color-background-tertiary-minimal');
      }

      path:last-of-type {
        fill: theme('colors.color-icons-primary');
      }
    }
  }
}
