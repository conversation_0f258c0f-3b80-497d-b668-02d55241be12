import { FinIconModule } from '@fincloud/ui/icon';
import { moduleMetadata, type Meta, type StoryObj } from '@storybook/angular';
import { FinBadgesModule } from '../../badges.module';
import { FinBadgeAppStatus } from '../../enums/fin-badge-app-status';
import { FinBadgeAppType } from '../../enums/fin-badge-app-type';
import { FinBadgeAppComponent } from './badge-app.component';

const meta: Meta<FinBadgeAppComponent> = {
  component: FinBadgeAppComponent,
  title: 'Components/Badges/Badge App',
  parameters: {
    docs: {
      description: {
        component: '`import { FinBadgesModule } from "@fincloud/ui/badges"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6531-157&m=dev',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [FinBadgesModule, FinIconModule],
    }),
  ],
};
export default meta;
type Story = StoryObj<FinBadgeAppComponent>;

export const NextfolderSuccess: Story = {
  argTypes: {
    type: {
      options: Object.values(FinBadgeAppType),
      control: { type: 'select' },
    },
    status: {
      options: Object.values(FinBadgeAppStatus),
      control: { type: 'select' },
    },
  },
  args: {
    status: FinBadgeAppStatus.SUCCESS,
    type: FinBadgeAppType.NEXTFOLDER,
  },
};

export const NextfolderInformation: Story = {
  argTypes: {
    ...NextfolderSuccess.argTypes,
  },
  args: {
    ...NextfolderSuccess.args,
    status: FinBadgeAppStatus.INFORMATION,
  },
};

export const NextfolderError: Story = {
  argTypes: {
    ...NextfolderSuccess.argTypes,
  },
  args: {
    ...NextfolderSuccess.args,
    status: FinBadgeAppStatus.ERROR,
  },
};

export const NextfolderPending: Story = {
  argTypes: {
    ...NextfolderSuccess.argTypes,
  },
  args: {
    ...NextfolderSuccess.args,
    status: FinBadgeAppStatus.PENDING,
  },
};

export const DracoonInformation: Story = {
  argTypes: {
    ...NextfolderSuccess.argTypes,
  },
  args: {
    status: FinBadgeAppStatus.INFORMATION,
    type: FinBadgeAppType.DRACCOON,
  },
};

export const DracoonError: Story = {
  argTypes: {
    ...NextfolderSuccess.argTypes,
  },
  args: {
    status: FinBadgeAppStatus.ERROR,
    type: FinBadgeAppType.DRACCOON,
  },
};
