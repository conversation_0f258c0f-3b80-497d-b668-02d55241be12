import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';
import { FinBadgeAppStatus } from '../../enums/fin-badge-app-status';
import { FinBadgeAppType } from '../../enums/fin-badge-app-type';

/**
 * A component that displays a badge with an icon based on the provided type and status.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-badge-app--docs Storybook Reference}
 */
@Component({
  selector: 'fin-badge-app',
  standalone: true,
  imports: [CommonModule, FinIconModule],
  templateUrl: './badge-app.component.html',
  styleUrl: './badge-app.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    '[class]': 'badgeCssClasses',
  },
})
export class FinBadgeAppComponent {
  /** Type of the badge */
  @Input() type: FinBadgeAppType = FinBadgeAppType.NEXTFOLDER;

  /** Status of the badge */
  @Input() status: FinBadgeAppStatus = FinBadgeAppStatus.INFORMATION;

  protected size = FinSize;

  protected get badgeCssClasses(): string {
    return `fin-badge-app-${this.status}`;
  }
}
