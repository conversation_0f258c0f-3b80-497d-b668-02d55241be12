import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';
import { FinBadgeStatus } from '../../enums/fin-badge-statuses';

/**
 * A badge that represents the status of an item, such as success, warning, or error.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-badge-status--docs Storybook Reference}
 */
@Component({
  selector: 'fin-badge-status',
  standalone: true,
  imports: [CommonModule, FinIconModule],
  templateUrl: './badge-status.component.html',
  styleUrl: './badge-status.component.scss',
  host: {
    class: 'fin-badge-status',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinBadgeStatusComponent {
  /** Type of the badge. */
  @Input() type: FinBadgeStatus = FinBadgeStatus.SIGNED;
  /** Content of the badge. */
  @Input() text = '';
  /** Size of the badge. */
  @Input() size: FinSize.S | FinSize.M = FinSize.S;
  /** Name of an icon within a [Material Icons](https://fonts.google.com/icons) font set. */
  @Input() iconName = '';
  /** URL pointing to the icon source. */
  @Input() iconSrc = '';

  protected statuses: typeof FinBadgeStatus = FinBadgeStatus;
}
