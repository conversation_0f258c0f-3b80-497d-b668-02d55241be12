import { FinSize } from '@fincloud/ui/types';
import type { Meta, StoryObj } from '@storybook/angular';
import { FinBadgeStatus } from '../../enums/fin-badge-statuses';
import { FinBadgeStatusComponent } from './badge-status.component';

const meta: Meta<FinBadgeStatusComponent> = {
  component: FinBadgeStatusComponent,
  title: 'Components/Badges/Badge Status',
  parameters: {
    docs: {
      description: {
        component: '`import { FinBadgeModule } from "@fincloud/ui/badges"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6531-157&m=dev',
    },
  },
};
export default meta;
type Story = StoryObj<FinBadgeStatusComponent>;

export const Draft: Story = {
  argTypes: {
    type: {
      options: Object.values(FinBadgeStatus),
      control: { type: 'select' },
    },
    size: {
      options: [FinSize.S, FinSize.M],
      control: { type: 'select' },
    },
  },
  args: {
    type: FinBadgeStatus.DRAFT,
    text: 'Simple text',
    iconName: '',
    iconSrc: '',
    size: FinSize.S,
  },
};

export const Pending: Story = {
  argTypes: {
    ...Draft.argTypes,
  },
  args: {
    ...Draft.args,
    type: FinBadgeStatus.PENDING,
  },
  parameters: {
    ...Draft.parameters,
  },
};

export const Signed: Story = {
  argTypes: {
    ...Draft.argTypes,
  },
  args: {
    ...Draft.args,
    type: FinBadgeStatus.SIGNED,
  },
  parameters: {
    ...Draft.parameters,
  },
};

export const Cancelled: Story = {
  argTypes: {
    ...Draft.argTypes,
  },
  args: {
    ...Draft.args,
    type: FinBadgeStatus.CANCELLED,
  },
  parameters: {
    ...Draft.parameters,
  },
};

export const InProgress: Story = {
  argTypes: {
    ...Draft.argTypes,
  },
  args: {
    ...Draft.args,
    type: FinBadgeStatus.IN_PROGRESS,
  },
  parameters: {
    ...Draft.parameters,
  },
};

export const WithIcon: Story = {
  argTypes: {
    ...Draft.argTypes,
  },
  args: {
    ...Draft.args,
    type: FinBadgeStatus.IN_PROGRESS,
    iconName: 'account_circle',
    size: FinSize.M,
  },
  parameters: {
    ...Draft.parameters,
  },
};
