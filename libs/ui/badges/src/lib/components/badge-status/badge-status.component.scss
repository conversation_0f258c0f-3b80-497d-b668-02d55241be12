.fin-badge-status {
  &-draft {
    background-color: theme('colors.color-background-tertiary-minimal');
    color: theme('colors.color-text-secondary');
  }
  &-pending {
    background-color: theme('colors.color-background-warning-minimal');
    color: theme('colors.color-text-warning');
  }
  &-signed {
    background-color: theme('colors.color-background-success-minimal');
    color: theme('colors.color-text-success');
  }
  &-cancelled {
    background-color: theme('colors.color-background-attention-minimal');
    color: theme('colors.color-text-error');
  }
  &-inprogress {
    background-color: theme('colors.color-background-secondary-minimal');
    color: theme('colors.color-text-interactive');
  }

  &-s {
    @apply fin-text-body-3-strong;
  }
  &-m {
    @apply fin-py-[0.4rem];
    @apply fin-text-body-2-strong;
  }
}
