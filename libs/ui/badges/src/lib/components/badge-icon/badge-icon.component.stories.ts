import { FinSize } from '@fincloud/ui/types';
import type { Meta, StoryObj } from '@storybook/angular';
import { FinBadgeType } from '../../enums/fin-badge-type';
import { FinBadgeIconComponent } from './badge-icon.component';

const meta: Meta<FinBadgeIconComponent> = {
  component: FinBadgeIconComponent,
  title: 'Components/Badges/Badge Icon',
  parameters: {
    docs: {
      description: {
        component: '`import { FinBadgesModule } from "@fincloud/ui/badges"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=20345-76026&t=WDX9LA8ChseIsTm1-4',
    },
    controls: {
      include: ['type', 'size', 'name', 'src'],
    },
  },
};
export default meta;
type Story = StoryObj<FinBadgeIconComponent>;

export const Default: Story = {
  argTypes: {
    type: {
      options: [
        FinBadgeType.DEFAULT,
        FinBadgeType.ACTIVE,
        FinBadgeType.ATTENTION,
        FinBadgeType.INACTIVE,
        FinBadgeType.ELLIPSE,
      ],
      control: { type: 'select' },
    },
    size: {
      options: [FinSize.XS, FinSize.S, FinSize.M],
      control: { type: 'select' },
    },
  },
  args: {
    type: FinBadgeType.DEFAULT,
    size: FinSize.M,
    name: 'message',
    src: '',
  },
};

export const Active: Story = {
  argTypes: {
    ...Default.argTypes,
  },
  args: {
    ...Default.args,
    type: FinBadgeType.ACTIVE,
  },
  parameters: {
    ...Default.parameters,
  },
};

export const Attention: Story = {
  argTypes: {
    ...Default.argTypes,
  },
  args: {
    ...Default.args,
    type: FinBadgeType.ATTENTION,
  },
  parameters: {
    ...Default.parameters,
  },
};
export const Inactive: Story = {
  argTypes: {
    ...Default.argTypes,
  },
  args: {
    ...Default.args,
    type: FinBadgeType.INACTIVE,
  },
  parameters: {
    ...Default.parameters,
  },
};

export const Ellipse: Story = {
  argTypes: {
    ...Default.argTypes,
  },
  args: {
    ...Default.args,
    type: FinBadgeType.ELLIPSE,
  },
  parameters: {
    ...Default.parameters,
  },
};
