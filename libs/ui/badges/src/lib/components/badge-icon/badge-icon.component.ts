import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';
import { FinBadgeType } from '../../enums/fin-badge-type';

/**
 * A badge component that displays an icon to represent a status or notification.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-badge-icon--docs Storybook Reference}
 */
@Component({
  selector: 'fin-badge-icon',
  standalone: true,
  imports: [CommonModule, FinIconModule],
  templateUrl: './badge-icon.component.html',
  styleUrl: './badge-icon.component.scss',
  host: {
    class: 'fin-badge-icon',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinBadgeIconComponent {
  /** Type of the badge */
  @Input() type:
    | FinBadgeType.DEFAULT
    | FinBadgeType.ACTIVE
    | FinBadgeType.ATTENTION
    | FinBadgeType.INACTIVE
    | FinBadgeType.ELLIPSE = FinBadgeType.DEFAULT;

  /** Size of the badge */
  @Input() size: FinSize.XS | FinSize.S | FinSize.M = FinSize.M;

  /** Name of an icon within a [Material Icons](https://fonts.google.com/icons) font set. */
  @Input() name = '';

  /** URL pointing to the icon source. */
  @Input() src = '';
}
