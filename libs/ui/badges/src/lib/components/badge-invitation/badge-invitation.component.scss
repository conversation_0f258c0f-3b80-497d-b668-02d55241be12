:host {
  @apply fin-inline-block;

  ::ng-deep {
    .mat-icon {
      width: 2.3rem;
      height: auto;
    }
    .fin-badge-invitation {
      &-regular {
        background-color: theme('colors.color-background-primary-strong');

        path {
          fill: theme('colors.color-background-dark-strong');
        }
      }

      &-unregistered-guest {
        background-color: theme('colors.color-background-disabled');

        path {
          fill: theme('colors.color-background-dark-moderate');
        }
      }

      &-registered-guest {
        background-color: theme('colors.color-background-warning-subtle');

        path {
          fill: theme('colors.color-background-attention-strong');
        }
      }
    }
  }
}
