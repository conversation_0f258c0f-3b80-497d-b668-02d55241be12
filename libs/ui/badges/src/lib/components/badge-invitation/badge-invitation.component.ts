import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { FinBadgeCustomerStatus } from '../../enums/fin-badge-customer-status';
/**
 * A badge indicating invitations or pending actions requiring user attention.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-badge-invitation--docs Storybook Reference}
 */
@Component({
  selector: 'fin-badge-invitation',
  standalone: true,
  imports: [CommonModule, MatIconModule],
  templateUrl: './badge-invitation.component.html',
  styleUrl: './badge-invitation.component.scss',
  host: {
    class: 'fin-badge-invitation',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinBadgeInvitationComponent {
  /** Type of the badge */
  @Input() type: FinBadgeCustomerStatus = FinBadgeCustomerStatus.REGULAR;
}
