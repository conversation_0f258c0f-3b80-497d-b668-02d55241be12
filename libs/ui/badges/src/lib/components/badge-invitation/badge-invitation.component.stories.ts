import { HttpClientModule } from '@angular/common/http';
import { MatIconModule } from '@angular/material/icon';
import { moduleMetadata, type Meta, type StoryObj } from '@storybook/angular';
import { FinBadgesModule } from '../../badges.module';
import { FinBadgeCustomerStatus } from '../../enums/fin-badge-customer-status';
import { FinBadgeInvitationComponent } from './badge-invitation.component';

const meta: Meta<FinBadgeInvitationComponent> = {
  component: FinBadgeInvitationComponent,
  title: 'Components/Badges/Badge Invitation',
  parameters: {
    docs: {
      description: {
        component: '`import { FinBadgesModule } from "@fincloud/ui/badges"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6531-157&m=dev',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [HttpClientModule, FinBadgesModule, MatIconModule],
    }),
  ],
};
export default meta;
type Story = StoryObj<FinBadgeInvitationComponent>;

export const Regular: Story = {
  argTypes: {
    type: {
      options: Object.values(FinBadgeCustomerStatus),
      control: { type: 'select' },
    },
  },
  args: {
    type: FinBadgeCustomerStatus.REGULAR,
  },
};

export const UnregisteredGuest: Story = {
  argTypes: {
    ...Regular.argTypes,
  },
  args: {
    ...Regular.args,
    type: FinBadgeCustomerStatus.UNREGISTERED_GUEST,
  },
  parameters: {
    ...Regular.parameters,
  },
};

export const RegisteredGuest: Story = {
  argTypes: {
    ...Regular.argTypes,
  },
  args: {
    ...Regular.args,
    type: FinBadgeCustomerStatus.REGISTERED_GUEST,
  },
  parameters: {
    ...Regular.parameters,
  },
};
