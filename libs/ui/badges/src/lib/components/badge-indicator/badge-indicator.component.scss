.fin-badge-indicator {
  &-active {
    background-color: theme('colors.color-background-secondary-strong');
    color: theme('colors.color-text-light');
  }
  &-default {
    background-color: theme('colors.color-background-secondary-subtle');
    color: theme('colors.color-text-interactive');
  }
  &-attention {
    background-color: theme('colors.color-background-attention-strong');
    color: theme('colors.color-text-light');
  }
  &-inactive {
    background-color: theme('colors.color-background-disabled');
    color: theme('colors.color-text-secondary');
  }
  &-ellipse {
    background-color: theme('colors.color-background-primary-strong');
    color: theme('colors.color-text-primary');
  }
  &-transparent {
    color: theme('colors.color-text-interactive');
  }

  &-xs {
    width: 0.8rem;
    height: 0.8rem;
  }
  &-l {
    min-width: 1.4rem;
    height: 1.4rem;
    padding: 0 0.35rem;
  }
  &-m {
    min-width: 2rem;
    height: 2rem;
    padding: 0 0.6rem;
  }
}
