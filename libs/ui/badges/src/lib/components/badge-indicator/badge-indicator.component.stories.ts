import type { Meta, StoryObj } from '@storybook/angular';
import { FinBadgeIndicatorComponent } from './badge-indicator.component';

import { FinSize } from '@fincloud/ui/types';
import { FinBadgeType } from '../../enums/fin-badge-type';

const meta: Meta<FinBadgeIndicatorComponent> = {
  component: FinBadgeIndicatorComponent,
  title: 'Components/Badges/Badge Indicator',
  parameters: {
    docs: {
      description: {
        component: '`import { FinBadgesModule } from "@fincloud/ui/badges"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=5200-48668&m=dev',
    },
  },
};
export default meta;
type Story = StoryObj<FinBadgeIndicatorComponent>;

export const Default: Story = {
  argTypes: {
    type: {
      options: Object.values(FinBadgeType),
      control: { type: 'select' },
    },
    count: {
      control: { type: 'number' },
    },
    maxCount: {
      control: { type: 'number' },
    },
    showMaxCount: {
      description:
        '<span style="color:red">**Deprecated**</span><br> Use `maxCount` instead',
      control: false,
    },
    size: {
      options: [FinSize.XS, FinSize.L, FinSize.M],
      control: { type: 'select' },
    },
  },
  args: {
    type: FinBadgeType.DEFAULT,
    count: 9,
    size: FinSize.M,
    maxCount: null,
  },
};

export const Active: Story = {
  argTypes: {
    ...Default.argTypes,
  },
  args: {
    ...Default.args,
    type: FinBadgeType.ACTIVE,
  },
  parameters: {
    ...Default.parameters,
  },
};

export const Attention: Story = {
  argTypes: {
    ...Default.argTypes,
  },
  args: {
    ...Default.args,
    type: FinBadgeType.ATTENTION,
  },
  parameters: {
    ...Default.parameters,
  },
};
export const Inactive: Story = {
  argTypes: {
    ...Default.argTypes,
  },
  args: {
    ...Default.args,
    type: FinBadgeType.INACTIVE,
  },
  parameters: {
    ...Default.parameters,
  },
};

export const Ellipse: Story = {
  argTypes: {
    ...Default.argTypes,
  },
  args: {
    ...Default.args,
    type: FinBadgeType.ELLIPSE,
  },
  parameters: {
    ...Default.parameters,
  },
};

export const Transparent: Story = {
  argTypes: {
    ...Default.argTypes,
  },
  args: {
    ...Default.args,
    type: FinBadgeType.TRANSPARENT,
  },
  parameters: {
    ...Default.parameters,
  },
};
