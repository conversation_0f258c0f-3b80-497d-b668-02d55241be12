import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FinSize } from '@fincloud/ui/types';
import { FinBadgeType } from '../../enums/fin-badge-type';

/**
 * A small indicator badge used to highlight counts or alerts on an item.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-badge-indicator--docs Storybook Reference}
 */
@Component({
  selector: 'fin-badge-indicator',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './badge-indicator.component.html',
  styleUrl: './badge-indicator.component.scss',
  host: {
    class: 'fin-badge-indicator',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinBadgeIndicatorComponent {
  /** The counter of the badge */
  @Input() count = 0;

  /** Type of the badge */
  @Input() type: FinBadgeType = FinBadgeType.DEFAULT;

  /** Size of the badge */
  @Input() size: FinSize.XS | FinSize.L | FinSize.M = FinSize.M;

  /**
   *  @deprecated Use `maxCount` instead.
   */
  @Input() showMaxCount: number | null = null;

  /** Shows its value with `+` if `count` exceeds it, e.g. `99+` */
  @Input() maxCount: number | null = this.showMaxCount;

  protected sizes = FinSize;
}
