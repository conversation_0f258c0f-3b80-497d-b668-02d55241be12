<fin-icon
  [name]="iconName"
  [src]="iconSrc"
  [size]="size === sizes.M ? sizes.S : sizes.L"
></fin-icon>

@if (!compact) {
  <div class="fin-text-start fin-grow">
    <div
      [ngClass]="active ? 'fin-text-body-2-strong' : 'fin-text-body-2-moderate'"
    >
      <ng-content select="[finMenuItemTitle]"></ng-content>
    </div>

    <div
      class="fin-menu-item-description fin-flex fin-flex-col fin-text-body-3-moderate fin-text-color-text-secondary"
      [ngClass]="{ 'fin-text-color-text-secondary': !attention }"
    >
      <ng-content select="[finMenuItemDescription]"></ng-content>
    </div>
  </div>
}

<ng-content select="[finMenuItemSuffix]"></ng-content>
