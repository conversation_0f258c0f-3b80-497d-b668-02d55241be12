import { CommonModule } from '@angular/common';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinSize } from '@fincloud/ui/types';
import {
  Args,
  moduleMetadata,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { FinMenuItemModule } from '../../menu-item.module';
import { FinMenuItemComponent } from './menu-item.component';

const meta: Meta = {
  component: FinMenuItemComponent,
  title: 'Components/Menu Item',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinMenuItemModule } from "@fincloud/ui/menu-item"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=21697-2547&t=nVUGsvd2kGGPyEu8-4',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        FinMenuItemModule,
        FinButtonModule,
        FinIconModule,
        FinTruncateTextModule,
      ],
    }),
  ],
};
export default meta;
type Story = StoryObj<
  FinMenuItemComponent & {
    finMenuItemTitle: string;
    finMenuItemDescription: string;
    finMenuItemSuffix: string;
  }
>;

const renderTemplate = (args: Args, disabled?: boolean) => ({
  props: { ...args, disabled },
  template: `
  <button
       fin-menu-item
       [size]="size"
       [compact]="compact"
       [disabled]="disabled"
       [attention]="attention"
       [active]="active"
       [iconName]="iconName"
       [iconSrc]="iconSrc"
     >
       <ng-container finMenuItemTitle>
         Title
       </ng-container>
       <ng-container finMenuItemDescription>
         Description
       </ng-container>
       <ng-container finMenuItemSuffix>
         <button fin-button-icon size="xs" appearance="informative">
           <fin-icon name="close"></fin-icon>
         </button>
       </ng-container>
     </button>
   `,
});

export const DefaultButton: Story = {
  args: {
    compact: false,
    active: false,
    attention: false,
    size: FinSize.L,
    iconName: 'edit_document',
    iconSrc: '',
  },
  argTypes: {
    size: {
      options: [FinSize.M, FinSize.L],
      control: { type: 'select' },
    },
    finMenuItemTitle: {
      description: 'Title template.',
    },
    finMenuItemDescription: {
      description: 'Description template.',
    },
    finMenuItemSuffix: {
      description: 'Suffix template.',
    },
  },
  render: (args) => renderTemplate(args, false),
};

export const DefaultAnchorLink: Story = {
  args: {
    ...DefaultButton.args,
  },
  argTypes: {
    ...DefaultButton.argTypes,
  },
  render: (args) => ({
    props: args,
    template: `
    <a
      fin-menu-item
      [compact]="compact"
      iconName="edit_document"
    >
      <ng-container finMenuItemTitle>
        Title
      </ng-container>
      <ng-container finMenuItemDescription>
        Description
      </ng-container>
      <ng-container finMenuItemSuffix>
        <button fin-button-icon size="s" appearance="informative">
          <fin-icon name="close"></fin-icon>
        </button>
      </ng-container>
    </a>
    `,
  }),
};

export const DefaultDisabledAttentionButton: Story = {
  args: {
    compact: false,
    active: false,
    attention: true,
    size: FinSize.L,
    iconName: 'edit_document',
    iconSrc: '',
  },
  argTypes: {
    ...DefaultButton.argTypes,
  },
  render: (args) => renderTemplate(args, true),
};

export const DefaultDisabledButton: Story = {
  args: {
    compact: false,
    active: false,
    attention: false,
    size: FinSize.L,
    iconName: 'edit_document',
    iconSrc: '',
  },
  argTypes: {
    ...DefaultButton.argTypes,
  },
  render: (args) => renderTemplate(args, true),
};

export const DefaultExample: Story = {
  args: {
    ...DefaultButton.args,
  },
  argTypes: {
    ...DefaultButton.argTypes,
  },
  parameters: {
    backgrounds: {
      default: 'dark',
      values: [{ name: 'dark', value: '#bdbdbd' }],
    },
  },
  render: (args) => {
    const menuItems = [
      {
        iconName: 'article',
        title: 'Title',
        description: 'Description',
        active: false,
      },
      {
        iconName: 'psychiatry',
        title: 'Energy analyse',
        description: 'Description',
        active: true,
      },
      {
        iconName: 'account_balance_wallet',
        title: 'Financing application status',
        description: 'Description',
        active: false,
      },
    ];

    return {
      props: { ...args, menuItems: menuItems },
      template: `
        <div class="fin-w-[30rem] fin-p-[2.4rem] fin-bg-white">
          <div class="fin-mb-[2.4rem] fin-px-[1.6rem] fin-flex fin-items-center fin-gap-[1.6rem]">
            <button
              fin-button-icon
            >
              <fin-icon name="add"></fin-icon>
            </button>

            <div class="fin-text-color-text-primary fin-text-body-1-strong">
              KPIs
            </div>
          </div>

          @for (menuItem of menuItems; track $index) {
            <a
              fin-menu-item
              [compact]="compact"
              [iconName]="menuItem.iconName"
              [active]="menuItem.active"
              class="fin-mb-[0.4rem]"
            >
              <ng-container finMenuItemTitle>
                <div finTruncateText class="fin-max-w-[17.2rem]">
                  {{ menuItem.title }}
                </div>
              </ng-container>
              <ng-container finMenuItemDescription>
                {{ menuItem.description }}
              </ng-container>
            </a>
          }
        </div>
      `,
    };
  },
};
