import { CommonModule } from '@angular/common';
import {
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  Input,
} from '@angular/core';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';

/**
 * A customizable side item component designed for presenting dynamic navigation content.
 *
 * For detailed documentation visit
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-menu-item--docs https://lib-ui.neoshare.dev/?path=/docs/components-menu-item--docs}.
 */

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: `
    button[fin-menu-item],
    a[fin-menu-item]
  `,
  standalone: true,
  imports: [CommonModule, FinIconModule],
  templateUrl: './menu-item.component.html',
  styleUrl: './menu-item.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    '[class]': 'menuItemCssClasses',
    class: 'fin-menu-item ',
  },
})
export class FinMenuItemComponent {
  /** FinSize of the menuItem. It should be from type FinSize. Default is Large (FinSize.Large). */
  @Input() size: FinSize.M | FinSize.L = FinSize.L;

  /** Name of an icon within a [Material Icons](https://fonts.google.com/icons) font set. */
  @Input() iconName = '';

  /** URL pointing to the icon source. */
  @Input() iconSrc = '';

  /** Show only icons and hide title and description. */
  @Input({ transform: booleanAttribute }) compact = false;

  /** Changes styles according to attention status. */
  @Input({ transform: booleanAttribute }) attention = false;

  /** Will apply the active style. */
  @Input({ transform: booleanAttribute }) active = false;

  protected sizes = FinSize;

  protected get menuItemCssClasses(): string {
    let classes = `fin-menu-item-${this.size}`;

    if (this.attention) {
      classes += ' fin-menu-item-attention';
    }

    if (this.active) {
      classes += ' fin-menu-item-active';
    }

    if (this.compact) {
      classes += ' fin-menu-item-compact';
    }

    return classes;
  }
}
