import {
  CDK_DRAG_PLACEHOLDER,
  CdkDragPlaceholder,
} from '@angular/cdk/drag-drop';
import { Directive, TemplateRef } from '@angular/core';

@Directive({
  selector: '[finDragPlaceholder]',
  standalone: true,
  providers: [
    { provide: CDK_DRAG_PLACEHOLDER, useExisting: FinDragPlaceHolderDirective },
  ],
})
export class FinDragPlaceHolderDirective extends CdkDragPlaceholder {
  // The constructor is necessary because this component extends CdkDragPlaceholder.
  // Without it, Storybook throws an error due to missing dependencies.
  // To be removed in feature versions.
  constructor(templateRef: TemplateRef<unknown>) {
    super(templateRef);
  }
}
