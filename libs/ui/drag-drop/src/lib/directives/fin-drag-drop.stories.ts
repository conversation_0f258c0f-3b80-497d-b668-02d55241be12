import { BrowserModule } from '@angular/platform-browser';
import { FinIconModule } from '@fincloud/ui/icon';
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { FinDragDropModule } from '../drag-drop.module';
import { FinDragDrop } from '../models/fin-drag-drop';
import { finMoveItemInArray } from '../utils/fin-move-item-in-array';
import { finTransferArrayItem } from '../utils/fin-transfer-array-item';

const meta: Meta = {
  title: 'Directives/Drag and drop',
  parameters: {
    docs: {
      description: {
        component: `
        Use fin drag and drop for smaller logic implementations. For more complex use cases, use cdk drag and drop directly.

        import { FinDragDropModule } from "@fincloud/ui/drag-drop"
        `,
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=24574-7918&t=wu6ygI47IVIUQuMs-4',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [BrowserModule, FinDragDropModule, FinIconModule],
    }),
  ],
};

export default meta;
type Story = StoryObj;

const items = ['Apple', 'Banana', 'Orange', 'Mango', 'Pineapple'];
const items2 = ['Strawberry', 'Blueberry'];
const items3 = ['Cherry', 'Kiwi', 'Papaya'];
// Keep the args outside of the story so they are not visible in it.
const args = {
  items: structuredClone(items),
  items2: structuredClone(items2),
  items3: structuredClone(items3),
};

// Functions
function drop(event: FinDragDrop<string[]>) {
  const { container, previousIndex, currentIndex } = event;
  finMoveItemInArray(container.data, previousIndex, currentIndex);
}

function transfer(event: FinDragDrop<string[]>) {
  const { previousContainer, container, previousIndex, currentIndex } = event;
  finTransferArrayItem(
    previousContainer.data,
    container.data,
    previousIndex,
    currentIndex,
  );
}

export const BasicUsage: Story = {
  argTypes: {
    finDrag: {
      description:
        'Element that can be moved inside a `finDropList` container.',
      control: false,
      table: {
        category: 'Drag',
      },
    },
    finDragDisabled: {
      description:
        '`@Input()` <br/> Whether starting to drag this element is disabled.',
      table: {
        category: 'Drag',
      },
    },
    finDragStarted: {
      description:
        '`@Output(): EventEmitter<FdkDragStart>` <br/> Emits when the user starts dragging the item.',
      table: {
        category: 'Drag',
      },
    },
    finDragEnded: {
      description:
        '`@Output(): EventEmitter<FinDragEnd>` <br/> Emits when the user stops dragging an item in the container.',
      table: {
        category: 'Drag',
      },
    },

    finDropList: {
      description: 'Container that wraps a set of draggable items.',
      control: false,
      table: {
        category: 'Drop List',
      },
    },
    finDropListData: {
      description:
        '`@Input()` <br/> Arbitrary data to attach to this container.',
      control: false,
      table: {
        category: 'Drop List',
      },
    },
    finDropListSortingDisabled: {
      description:
        '`@Input()` <br/> Whether sorting within this drop list is disabled.',
      control: false,
      table: {
        category: 'Drop List',
      },
    },
    finDropListDropped: {
      description:
        '`@Output(): EventEmitter<FinDragDrop<T>>` <br/> Emits when the user drops an item inside the container.',
      control: false,
      table: {
        category: 'Drop List',
      },
    },
    finDropListEntered: {
      description:
        '`@Output(): EventEmitter<FinDragEnter<T>>` <br/> Emits when the user has moved a new drag item into this container.',
      control: false,
      table: {
        category: 'Drop List',
      },
    },
    finDropListExited: {
      description:
        '`@Output(): EventEmitter<FinDragExit<T>>` <br/> Emits when the user removes an item from the container by dragging it into another container.',
      control: false,
      table: {
        category: 'Drop List',
      },
    },

    finDropListGroup: {
      description:
        'Declaratively connects sibling `finDropList` instances together. All of the `finDropList` elements that are placed inside a `finDropListGroup` will be connected to each other automatically.',
      control: false,
      table: {
        category: 'Drop List Group',
      },
    },

    finDragHandle: {
      description: 'Handle that can be used to drag a `finDrag` instance.',
      control: false,
      table: {
        category: 'Drag Handle',
      },
    },

    '*finDragPlaceholder': {
      description:
        'Element that will be used as a template for the placeholder of a `finDrag` when it is being dragged. The placeholder is displayed in place of the element being dragged.',
      control: false,
      table: {
        category: 'Drag Placeholder',
      },
    },

    'fin-drag-drop-item': {
      description: 'Directive that adds styles for the drag-and-drop item.',
      control: false,
      table: {
        category: 'Attributes',
      },
    },

    finMoveItemInArray: {
      description:
        'Moves an item one index in an array to another. <br/> Function arguments: <br/> `array` - Array in which to move the item. <br/> `fromIndex` - Starting index of the item <br/> `toIndex` - Index to which the item should be moved.',
      control: false,
      table: {
        category: 'Functions',
      },
    },
    finTransferArrayItem: {
      description:
        'Moves an item from one array to another. <br/> Function arguments: <br/> `currentArray` - Array from which to transfer the item. <br/> `targetArray` - Array into which to put the item. <br/> `currentIndex` - Index of the item in its current array. <br/> `targetIndex` - Index at which to insert the item.',
      control: false,
      table: {
        category: 'Functions',
      },
    },
  },
  render: () => ({
    props: { ...args, drop },
    template: `
      <div
        class="fin-w-[30rem]"
        finDropList
        [finDropListData]="items"
        (finDropListDropped)="drop($event)"
      >
        @for (item of items; track $index) {
          <div fin-drag-drop-item finDrag class="fin-flex fin-justify-center fin-items-center fin-gap-[0.8rem] fin-mb-[0.4rem] fin-cursor-grabbing">
            <fin-icon name="local_grocery_store" size="s"></fin-icon>

            <div class="fin-flex-grow fin-text-color-text-primary fin-text-body-3-strong">
              {{ item }}
            </div>

            <fin-icon name="drag_indicator" size="s" class="fin-text-color-icons-tertiary"></fin-icon>
          </div>
        }
      </div>
    `,
  }),
};

export const DragHandle: Story = {
  argTypes: {
    ...BasicUsage.argTypes,
  },
  render: () => ({
    props: { ...args, drop },
    template: `
      <div class="fin-mb-[1rem]">The drag item can only be dragged through the handler.</div>
  
      <div
        class="fin-w-[30rem]"
        finDropList
        [finDropListData]="items"
        (finDropListDropped)="drop($event)"
      >
        @for (item of items; track $index) {
          <div fin-drag-drop-item finDrag class="fin-flex fin-justify-center fin-items-center fin-gap-[0.8rem] fin-mb-[0.4rem]">
            <fin-icon name="local_grocery_store" size="s"></fin-icon>

            <div class="fin-flex-grow fin-text-color-text-primary fin-text-body-3-strong">
              {{ item }}
            </div>

            <fin-icon finDragHandle name="drag_indicator" size="s" class="fin-text-color-icons-tertiary fin-cursor-grabbing"></fin-icon>
          </div>
        }
      </div>
    `,
  }),
};

export const CustomDropPreview: Story = {
  argTypes: {
    ...BasicUsage.argTypes,
  },
  render: () => ({
    props: { ...args, drop },
    template: `
      <div
        class="fin-w-[30rem]"
        finDropList
        [finDropListData]="items"
        (finDropListDropped)="drop($event)"
      >
        @for (item of items; track $index) {
          <div fin-drag-drop-item finDrag class="fin-flex fin-justify-center fin-items-center fin-gap-[0.8rem] fin-mb-[0.4rem] fin-cursor-grabbing">
            <fin-icon name="local_grocery_store" size="s"></fin-icon>

            <div class="fin-flex-grow fin-text-color-text-primary fin-text-body-3-strong">
              {{ item }}
            </div>

            <div *finDragPlaceholder class="fin-flex fin-items-center fin-px-[1.6rem] fin-border-[0.2rem] fin-border-dotted fin-border-color-border-default-interactive fin-rounded-[0.4rem] fin-h-[4rem]">
              Custom template for <span class="fin-font-semibold fin-underline fin-ms-[0.4rem]">{{ item }}</span>
            </div>
          </div>
        }
      </div>
    `,
  }),
};

export const DropGroup: Story = {
  argTypes: {
    ...BasicUsage.argTypes,
  },
  render: () => ({
    props: { ...args, transfer },
    template: `
      <div finDropListGroup class="fin-grid fin-grid-cols-3 fin-gap-[1rem] fin-w-[80rem]">
        <div>
          <div class="fin-mb-[1rem]">Basket 1 (disabled dragging)</div>

          <div
            finDropList
            [finDropListData]="items2"
            (finDropListDropped)="transfer($event)"
          >
            @for (item of items2; track $index) {
              <div
                fin-drag-drop-item
                finDrag
                finDragDisabled
                class="fin-flex fin-justify-center fin-items-center fin-gap-[0.8rem] fin-mb-[0.4rem]"
              >
                <div
                  class="fin-flex-grow fin-text-color-text-primary fin-text-body-3-strong"
                >
                  {{ item }}
                </div>
              </div>
            }
          </div>
        </div>

        <div>
          <div class="fin-mb-[1rem]">Basket 2 (disabled dragging)</div>

          <div
            finDropList
            [finDropListData]="items3"
            (finDropListDropped)="transfer($event)"
          >
            @for (item of items3; track $index) {
              <div
                fin-drag-drop-item
                finDrag
                finDragDisabled
                class="fin-flex fin-justify-center fin-items-center fin-gap-[0.8rem] fin-mb-[0.4rem]"
              >
                <div
                  class="fin-flex-grow fin-text-color-text-primary fin-text-body-3-strong"
                >
                  {{ item }}
                </div>
              </div>
            }
          </div>
        </div>

        <div>
          <div class="fin-mb-[1rem]">You can drag only from here (disabled sorting)</div>

          <div
            finDropList
            finDropListSortingDisabled
            [finDropListData]="items"
            (finDropListDropped)="transfer($event)"
          >
            @for (item of items; track $index) {
              <div
                fin-drag-drop-item
                finDrag
                class="fin-flex fin-justify-center fin-items-center fin-gap-[0.8rem] fin-mb-[0.4rem] fin-cursor-grabbing"
              >
                <div
                  class="fin-flex-grow fin-text-color-text-primary fin-text-body-3-strong"
                >
                  {{ item }}
                </div>

                <fin-icon
                  name="drag_indicator"
                  size="s"
                  class="fin-text-color-icons-tertiary"
                ></fin-icon>
              </div>
            }
          </div>
        </div>
      </div>
    `,
  }),
};
