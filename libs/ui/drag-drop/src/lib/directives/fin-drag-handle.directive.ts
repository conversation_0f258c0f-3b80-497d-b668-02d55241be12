import { CDK_DRAG_HANDLE, CdkDragHandle } from '@angular/cdk/drag-drop';
import { Directive, ElementRef, Inject, Optional } from '@angular/core';
import { FinDragDirective } from './fin-drag.directive';

@Directive({
  selector: '[finDragHandle]',
  standalone: true,
  providers: [
    { provide: CDK_DRAG_HANDLE, useExisting: FinDragHandleDirective },
  ],
})
export class FinDragHandleDirective extends CdkDragHandle {
  // The constructor is necessary because this component extends CdkDragHandle.
  // Without it, Storybook throws an error due to missing dependencies.
  // To be removed in feature versions.
  constructor(
    element: ElementRef<HTMLElement>,
    @Optional() @Inject(FinDragDirective) parentDrag?: FinDragDirective,
  ) {
    super(element, parentDrag);
  }
}
