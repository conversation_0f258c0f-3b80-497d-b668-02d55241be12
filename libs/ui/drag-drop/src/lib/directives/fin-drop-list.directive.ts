import { Directionality } from '@angular/cdk/bidi';
import {
  CDK_DROP_LIST,
  CDK_DROP_LIST_GROUP,
  CdkDropList,
  DragDrop,
} from '@angular/cdk/drag-drop';
import { ScrollDispatcher } from '@angular/cdk/scrolling';
import {
  ChangeDetectorRef,
  Directive,
  ElementRef,
  EventEmitter,
  Input,
  Optional,
  Output,
  booleanAttribute,
} from '@angular/core';
import { FinDrag } from '../models/fin-drag';
import { FinDragDrop } from '../models/fin-drag-drop';
import { FinDragEnter } from '../models/fin-drag-enter';
import { FinDragExit } from '../models/fin-drag-exit';
import { FinDropList } from '../models/fin-drop-list';
import { FinDropListGroupDirective } from './fin-drop-list-group.directive';

@Directive({
  selector: '[finDropList]',
  standalone: true,
  providers: [
    // Prevent child drop lists from picking up the same group as their parent.
    { provide: CDK_DROP_LIST_GROUP, useValue: undefined },
    { provide: CDK_DROP_LIST, useExisting: FinDropListDirective },
  ],
  host: {
    class: 'fin-drop-list',
    '[class.fin-drag-sort-disabled]': 'sortingDisabled',
    '[class.fin-drag-custom-placeholder]': 'customPlaceholder',
  },
})
export class FinDropListDirective<T = unknown> extends CdkDropList {
  // fin prefix overrides
  @Input('finDropListId') override id!: string;

  @Input('finDropListConnectedTo')
  override connectedTo: (CdkDropList | string)[] | CdkDropList | string = [];

  /** Arbitrary data to attach to this container. */
  @Input('finDropListData') override data!: T;

  /** Whether sorting within this drop list is disabled. */
  @Input({ alias: 'finDropListSortingDisabled', transform: booleanAttribute })
  override sortingDisabled = false;

  @Input({ alias: 'finDropListCustomPlaceholder', transform: booleanAttribute })
  customPlaceholder = false;

  @Input('finDropListEnterPredicate')
  override enterPredicate: (drag: FinDrag, drop: FinDropList) => boolean = () =>
    true;

  /** Emits when the user drops an item inside the container. */
  @Output('finDropListDropped')
  override dropped = new EventEmitter<FinDragDrop<T>>();

  /** Emits when the user has moved a new drag item into this container. */
  @Output('finDropListEntered')
  override entered = new EventEmitter<FinDragEnter<T>>();

  /** Emits when the user removes an item from the container by dragging it into another container. */
  @Output('finDropListExited')
  override exited = new EventEmitter<FinDragExit<T>>();

  // The constructor is necessary because this component extends CdkDropList.
  // Without it, Storybook throws an error due to missing dependencies.
  // To be removed in feature versions.
  constructor(
    element: ElementRef<HTMLElement>,
    dragDrop: DragDrop,
    _changeDetectorRef: ChangeDetectorRef,
    _scrollDispatcher: ScrollDispatcher,
    @Optional() _dir?: Directionality,
    @Optional() _group?: FinDropListGroupDirective<FinDropListDirective>,
  ) {
    super(
      element,
      dragDrop,
      _changeDetectorRef,
      _scrollDispatcher,
      _dir,
      _group,
      {},
    );
  }
}
