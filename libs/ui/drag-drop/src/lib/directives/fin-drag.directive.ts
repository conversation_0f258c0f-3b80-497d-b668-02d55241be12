import { Directionality } from '@angular/cdk/bidi';
import {
  CDK_DRAG_PARENT,
  CdkDrag,
  CdkDragHandle,
  DragDrop,
  DragDropConfig,
} from '@angular/cdk/drag-drop';
import { DOCUMENT } from '@angular/common';
import {
  ChangeDetectorRef,
  Directive,
  ElementRef,
  EventEmitter,
  Inject,
  Input,
  NgZone,
  OnChanges,
  Optional,
  Output,
  ViewContainerRef,
  booleanAttribute,
} from '@angular/core';
import { FinDragEnd } from '../models/fin-drag-end';
import { FinDragStart } from '../models/fin-drag-start';
import { FinDropListDirective } from './fin-drop-list.directive';

@Directive({
  selector: '[finDrag]',
  standalone: true,
  providers: [{ provide: CDK_DRAG_PARENT, useExisting: FinDragDirective }],
})
export class FinDragDirective extends CdkDrag implements OnChanges {
  // fin prefix overrides

  /** Whether starting to drag this element is disabled. */
  @Input({ alias: 'finDragDisabled', transform: booleanAttribute })
  override get disabled(): boolean {
    return this.__disabled || !!this.dropContainer?.disabled;
  }
  override set disabled(value: boolean) {
    this.__disabled = value;
    this._dragRef.disabled = this.__disabled;
  }
  private __disabled!: boolean;

  /** Emits when the user starts dragging the item. */
  @Output('finDragStarted') override started = new EventEmitter<FinDragStart>();

  /** Emits when the user stops dragging an item in the container. */
  // eslint-disable-next-line @angular-eslint/no-output-native
  @Output('finDragEnded') override ended = new EventEmitter<FinDragEnd>();

  // The constructor is necessary because this component extends CdkDrag.
  // Without it, Storybook throws an error due to missing dependencies.
  // To be removed in feature versions.
  constructor(
    element: ElementRef<HTMLElement>,
    @Inject(FinDropListDirective) dropContainer: FinDropListDirective,
    @Inject(DOCUMENT) _document: any,
    _ngZone: NgZone,
    _viewContainerRef: ViewContainerRef,
    @Optional() _dir: Directionality,
    dragDrop: DragDrop,
    _changeDetectorRef: ChangeDetectorRef,
    @Optional() _selfHandle?: CdkDragHandle,
    @Optional() _parentDrag?: CdkDrag,
  ) {
    super(
      element,
      dropContainer,
      _document,
      _ngZone,
      _viewContainerRef,
      {} as DragDropConfig,
      _dir,
      dragDrop,
      _changeDetectorRef,
      _selfHandle,
      _parentDrag,
    );
  }
}
