import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FinDragDropItemDirective } from './directives/fin-drag-drop-item.directive';
import { FinDragHandleDirective } from './directives/fin-drag-handle.directive';
import { FinDragPlaceHolderDirective } from './directives/fin-drag-placeholder.directive';
import { FinDragDirective } from './directives/fin-drag.directive';
import { FinDropListGroupDirective } from './directives/fin-drop-list-group.directive';
import { FinDropListDirective } from './directives/fin-drop-list.directive';

@NgModule({
  imports: [
    CommonModule,
    FinDragDropItemDirective,
    FinDragDirective,
    FinDropListDirective,
    FinDropListGroupDirective,
    FinDragHandleDirective,
    FinDragPlaceHolderDirective,
  ],
  exports: [
    FinDragDropItemDirective,
    FinDragDirective,
    FinDropListDirective,
    FinDropListGroupDirective,
    FinDragHandleDirective,
    FinDragPlaceHolderDirective,
  ],
})
export class FinDragDropModule {}
