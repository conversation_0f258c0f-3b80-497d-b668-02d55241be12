import { moveItemInArray } from '@angular/cdk/drag-drop';

/**
 * Moves an item one index in an array to another.
 * @param array Array in which to move the item.
 * @param fromIndex Starting index of the item.
 * @param toIndex Index to which the item should be moved.
 */
export function finMoveItemInArray(
  array: string[],
  fromIndex: number,
  toIndex: number,
): void {
  moveItemInArray(array, fromIndex, toIndex);
}
