import { transferArrayItem } from '@angular/cdk/drag-drop';

/**
 * Moves an item from one array to another.
 * @param currentArray Array from which to transfer the item.
 * @param targetArray Array into which to put the item.
 * @param currentIndex Index of the item in its current array.
 * @param targetIndex Index at which to insert the item.
 */
export function finTransferArrayItem(
  currentArray: string[],
  targetArray: string[],
  currentIndex: number,
  targetIndex: number,
): void {
  transferArrayItem(currentArray, targetArray, currentIndex, targetIndex);
}
