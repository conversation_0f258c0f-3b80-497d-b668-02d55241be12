<label
  class="fin-label fin-flex fin-justify-between fin-items-center"
  [ngClass]="{
    'fin-text-color-text-disabled': control.disabled,
    'fin-text-color-text-primary': readonly,
  }"
  [for]="control"
>
  @if (label) {
    <span class="fin-w-full" finTruncateText> {{ label }} </span>
  }
</label>
<mat-form-field
  class="fin-block fin-field fin-field-stepper fin-field-size-{{ size }}"
  [ngClass]="{
    'fin-field-readonly': readonly,
    'fin-field-warning':
      (externalFieldMessages?.type || (getMessage$ | async)?.type) ===
      'warning',
    'fin-field-success':
      (externalFieldMessages?.type || (getMessage$ | async)?.type) ===
      'success',
  }"
>
  <div matPrefix class="fin-pe-size-spacing-8">
    <button
      class="fin-field-stepper-button"
      type="button"
      (click)="decrease()"
      [disabled]="
        control.disabled ||
        (min && (control.value === min || control.value < min))
      "
    >
      <fin-icon name="remove"></fin-icon>
    </button>
  </div>

  <input
    matInput
    type="number"
    [formControl]="control"
    (input)="onInputChange()"
    (blur)="blur($event)"
  />

  <div matSuffix class="fin-ps-size-spacing-8 fin-pe-size-spacing-4">
    <button
      class="fin-field-stepper-button"
      type="button"
      (click)="increase()"
      [disabled]="
        control.disabled ||
        (max && (control.value === max || control.value > max))
      "
    >
      <fin-icon name="add"></fin-icon>
    </button>
  </div>

  <mat-hint class="fin-w-full">
    @if (
      control.valid && control.touched && (getMessage$ | async);
      as message
    ) {
      @if (message.type === 'success') {
        <ng-container *ngTemplateOutlet="message.template"></ng-container>
      }
    } @else {
      <div class="fin-hint">
        <ng-content select="[finInputHint]"></ng-content>
      </div>
    }
  </mat-hint>

  @if ((getMessage$ | async)?.template; as messageTemplate) {
    <mat-error>
      <div class="fin-inline-block fin-w-full">
        <ng-container *ngTemplateOutlet="messageTemplate"></ng-container>
      </div>
    </mat-error>
  }
</mat-form-field>
