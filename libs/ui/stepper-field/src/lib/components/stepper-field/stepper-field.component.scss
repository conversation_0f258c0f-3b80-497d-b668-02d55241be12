::ng-deep .fin-field-stepper {
  --mat-form-field-container-text-size: 1.6rem;

  &-button {
    &:disabled {
      color: theme('colors.color-icons-disabled');
      cursor: not-allowed;
    }
  }
  .mat-mdc-input-element {
    text-align: center;
  }

  .mdc-notched-outline {
    &__notch {
      border-radius: 0;
      border-left: none !important;
    }
  }

  &:hover:not(:focus-within) {
    .mdc-notched-outline {
      &__leading,
      &__trailing,
      &__notch {
        background-color: theme('colors.color-hover-neutral');
        border-color: theme('colors.color-border-default-primary') !important;
      }
    }
  }
}
