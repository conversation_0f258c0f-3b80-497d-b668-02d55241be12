import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  booleanAttribute,
  forwardRef,
} from '@angular/core';
import { NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatRadioChange, MatRadioModule } from '@angular/material/radio';
import { FinSize, LabelPosition } from '@fincloud/ui/types';
import { FinAngularMaterialModule } from '@fincloud/utils/angular-material';
import { FinControlValueAccessor } from '@fincloud/utils/control-value-accessor';
import { FinRadioOption } from '../models/fin-radio-option';

/**
 * A component that allows users to select one option from a set.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-radio--docs Storybook Reference}
 */
@Component({
  selector: 'fin-radio-button',
  standalone: true,
  imports: [
    CommonModule,
    MatRadioModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    FinAngularMaterialModule,
  ],
  templateUrl: './radio.component.html',
  styleUrl: './radio.component.scss',
  host: {
    class: 'fin-radio-button',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FinRadioComponent),
      multi: true,
    },
  ],
})
export class FinRadioComponent extends FinControlValueAccessor {
  /** All of the defined radio options. */
  @Input() options: FinRadioOption[] = [];

  /** Size of the button. */
  @Input() size: FinSize.S | FinSize.M = FinSize.M;

  /** A custom property name which holds the label. */
  @Input() labelPropertyName = 'label';

  /** A custom property name which holds the value. */
  @Input() valuePropertyName = 'value';

  /** Use only with standalone radio button mode. */
  @Input() value = '';

  /** Use only with standalone radio button mode. */
  @Input() label = '';

  /** Whether the labels should appear after or before the radio-buttons. */
  @Input() labelPosition: LabelPosition.AFTER | LabelPosition.BEFORE =
    LabelPosition.AFTER;

  /** Allows the use of single radio button at a time. */
  @Input({ transform: booleanAttribute }) standalone = false;

  /** Event emitted due to user interaction with the radio button. */
  @Output() selectionChanged = new EventEmitter<any>();

  protected trackByLabel(_: number, option: FinRadioOption): string {
    return option[this.labelPropertyName] as string;
  }

  protected change(change: MatRadioChange) {
    this.selectionChanged.emit(change.value);
    if (this.standalone) {
      this.control.setValue(change.value);
    }
  }
}
