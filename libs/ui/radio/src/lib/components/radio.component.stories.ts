import { FormControl, Validators } from '@angular/forms';
import { FinSize, LabelPosition } from '@fincloud/ui/types';
import type { Meta, StoryObj } from '@storybook/angular';
import { moduleMetadata } from '@storybook/angular';
import { FinRadioOption } from '../models/fin-radio-option';
import { FinRadioModule } from '../radio.module';
import { FinRadioComponent } from './radio.component';

const meta: Meta<FinRadioComponent> = {
  component: FinRadioComponent,
  title: 'Fields/Radio Button',
  parameters: {
    docs: {
      description: {
        component: '`import { FinRadioModule } from "@fincloud/ui/radio"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6547-16923&t=6TX7dCqEYc2cpsKy-4',
    },
  },
};
export default meta;

type Story = StoryObj<FinRadioComponent>;

const options: FinRadioOption[] = [
  { value: 'first', label: 'First option' },
  { value: 'second', label: 'Second option' },
];
const invalidField = new FormControl('', Validators.required);
invalidField.markAsTouched();

export const Primary: Story = {
  args: {
    size: FinSize.M,
    options: options,
  },
  argTypes: {
    size: {
      control: { type: 'select' },
      options: [FinSize.S, FinSize.M],
    },
    labelPosition: {
      control: { type: 'select' },
      options: Object.values(LabelPosition),
    },
    options: {
      control: { type: 'select' },
    },
    selectionChanged: {
      control: false,
      type: 'function',
    },
  },
  render: (args) => ({
    props: {
      ...args,
      formControl: new FormControl(options[0]['value']),
    },
  }),
};

export const Disabled: Story = {
  argTypes: {
    ...Primary.argTypes,
  },
  args: {
    ...Primary.args,
  },
  render: (args) => ({
    props: {
      ...args,
      formControl: new FormControl({
        value: options[0]['value'],
        disabled: true,
      }),
    },
  }),
  parameters: {
    ...Primary.parameters,
  },
};

export const SizeS: Story = {
  argTypes: {
    ...Primary.argTypes,
  },
  args: {
    ...Primary.args,
    size: FinSize.S,
  },
  render: Primary.render,
  parameters: {
    ...Primary.parameters,
  },
};

export const Standalone: Story = {
  argTypes: {
    ...Primary.argTypes,
  },
  args: {
    ...Primary.args,
    size: FinSize.S,
  },
  decorators: [
    moduleMetadata({
      imports: [FinRadioModule],
    }),
  ],
  render: (args) => ({
    props: {
      ...args,
      formControl: new FormControl(options[0]['value']),
    },
    template: `
      <fin-radio-button
        standalone
        [value]="1"
        [formControl]="formControl"
        class="fin-block fin-mb-3"
      ></fin-radio-button>

      <fin-radio-button
        standalone
        [value]="2"
        [formControl]="formControl"
      ></fin-radio-button>    
    `,
  }),
  parameters: {
    ...Primary.parameters,
  },
};

export const Invalid: Story = {
  argTypes: {
    ...Primary.argTypes,
  },
  args: {
    ...Primary.args,
  },
  render: (args) => ({
    props: {
      ...args,
      formControl: invalidField,
    },
  }),
};
