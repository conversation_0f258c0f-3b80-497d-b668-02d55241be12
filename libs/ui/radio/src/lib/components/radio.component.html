@if (!standalone) {
  <mat-radio-group
    (change)="change($event)"
    [formControl]="control"
    class="fin-flex fin-flex-col fin-gap-[1.2rem]"
    [ngClass]="['fin-radio-' + size]"
    [labelPosition]="labelPosition"
  >
    @for (option of options; track trackByLabel($index, option)) {
      <mat-radio-button [value]="option[valuePropertyName]">
        @if (option[labelPropertyName]) {
          <mat-label>
            {{ option[labelPropertyName] }}
          </mat-label>
        }
      </mat-radio-button>
    }
  </mat-radio-group>
} @else {
  <div [ngClass]="['fin-radio-' + size]">
    <mat-radio-button
      [value]="value"
      (change)="change($event)"
      [checked]="control.value === value"
    >
      @if (label) {
        <mat-label>
          {{ label }}
        </mat-label>
      }
    </mat-radio-button>
  </div>
}
