<label
  class="fin-label fin-flex fin-justify-between fin-items-center"
  [ngClass]="{
    'fin-text-color-text-disabled': control.disabled,
    'fin-text-color-text-primary': readonly,
  }"
  [for]="control"
>
  @if (label) {
    <span class="fin-w-full" finTruncateText> {{ label }} </span>
  } @else {
    <ng-content select="[finInputLabel]"></ng-content>
  }
</label>
<mat-form-field
  [@.disabled]="true"
  class="fin-field fin-block fin-field-size-{{ size }}"
  [ngClass]="{
    'fin-field-with-progress': showProgress && type === finInputType.PASSWORD,
    'fin-field-readonly': readonly,
    'fin-field-warning':
      (externalFieldMessages?.type || (getMessage$ | async)?.type) ===
      'warning',
    'fin-field-success':
      (externalFieldMessages?.type || (getMessage$ | async)?.type) ===
      'success',
  }"
  [subscriptSizing]="dynamicErrorSpace"
>
  <div
    matPrefix
    class="fin-prefix fin-flex fin-items-center fin-justify-center"
  >
    <ng-content select="[finInputPrefix]"></ng-content>
  </div>
  @if (aiEnabled) {
    <fin-ai-suggestion
      [aiEnabled]="aiEnabled"
      [formControl]="control"
      (aiSuggestionReady)="onAiSuggestionReady()"
    >
    </fin-ai-suggestion>
  }
  @if (type === finInputType.PASSWORD && !control.disabled) {
    <input
      matInput
      [readonly]="readonly"
      [type]="!passwordTypeToggle ? finInputType.PASSWORD : finInputType.TEXT"
      [formControl]="control"
      (blur)="blur($event)"
      [placeholder]="readonly ? '' : placeholder"
      [attr.maxlength]="maxLength ? maxLength : null"
    />
  } @else if (
    type === finInputType.CURRENCY ||
    type === finInputType.PERCENTAGE ||
    type === finInputType.DECIMAL ||
    type === finInputType.MONTHS ||
    type === finInputType.INTEGER
  ) {
    <input
      matInput
      [readonly]="readonly"
      type="text"
      [formControl]="control"
      (blur)="blur($event)"
      [placeholder]="readonly ? '' : placeholder"
      currencyMask
      [options]="mask"
      [attr.maxlength]="maxLength ? maxLength : null"
    />
  } @else {
    <input
      #finInput
      matInput
      [readonly]="readonly"
      [type]="type"
      [formControl]="control"
      (blur)="blur($event)"
      [placeholder]="readonly ? '' : placeholder"
      [attr.maxlength]="maxLength ? maxLength : null"
    />
  }
  <div matSuffix class="fin-suffix fin-flex fin-items-center fin-justify-end">
    @if (type === finInputType.PASSWORD && showPasswordButton) {
      <button
        mat-icon-button
        (click)="passwordTypeToggle = !passwordTypeToggle"
        [attr.aria-pressed]="passwordTypeToggle"
        [disabled]="control.disabled"
        [ngClass]="{
          'fin-password-toggle-disabled': control.disabled,
          'fin-invisible': showLoader,
        }"
      >
        <fin-icon
          class="fin-text-color-icons-tertiary"
          [name]="passwordTypeToggle ? 'visibility' : 'visibility_off'"
        ></fin-icon>
      </button>
    }
    <ng-content
      select="[finInputSuffix]"
      [class.fin-invisible]="showLoader"
    ></ng-content>
    @if (showLoader) {
      <fin-loader [hide]="!showLoader"></fin-loader>
    }
  </div>

  <mat-hint class="fin-w-full">
    <div class="fin-absolute fin-bottom-[0.3rem] fin-w-full">
      <ng-container *ngTemplateOutlet="progressPlaceholder"></ng-container>
    </div>

    @if (
      control.valid && control.touched && (getMessage$ | async);
      as message
    ) {
      @if (message.type === 'success') {
        <ng-container *ngTemplateOutlet="message.template"></ng-container>
      }
    } @else {
      <div class="fin-hint">
        <ng-content select="[finInputHint]"></ng-content>
      </div>
    }
  </mat-hint>
  @if (showProgress || (getMessage$ | async)?.template) {
    <mat-error [@.disabled]="true">
      <div class="fin-inline-block fin-w-full">
        @if (control.invalid && control.touched) {
          <div class="fin-absolute fin-bottom-[2.4rem] fin-w-full">
            <ng-container
              *ngTemplateOutlet="progressPlaceholder"
            ></ng-container>
          </div>
        }
        @if ((getMessage$ | async)?.template; as messageTemplate) {
          <ng-container *ngTemplateOutlet="messageTemplate"></ng-container>
        }
      </div>
    </mat-error>
  }
</mat-form-field>

<ng-template #progressPlaceholder>
  @if (showProgress && type === finInputType.PASSWORD) {
    <ng-content select="[finInputProgress]"></ng-content>
  }
</ng-template>
