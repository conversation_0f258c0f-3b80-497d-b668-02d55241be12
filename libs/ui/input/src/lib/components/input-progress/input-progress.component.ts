import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

@Component({
  selector: 'fin-input-progress',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './input-progress.component.html',
  styleUrl: './input-progress.component.scss',
  host: {
    class: 'fin-input-progress',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinInputProgressComponent {
  /** Current progress percentage. */
  @Input() progress = 0;

  /** Percentage required to reach success. */
  @Input() successPercentage = 50;
}
