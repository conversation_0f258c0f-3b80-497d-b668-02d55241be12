import { CommonModule } from '@angular/common';
import { FormControl } from '@angular/forms';
import { moduleMetadata, type Meta, type StoryObj } from '@storybook/angular';
import { NgxCurrencyInputMode } from 'ngx-currency';
import { LocaleId } from '../../enums/fin-locale-id';
import { FinInputModule } from '../../input.module';
import { FIN_CURRENCY_MASK } from '../../utils/fin-currency-mask-token';
import { FIN_DECIMAL_MASK } from '../../utils/fin-decimal-mask-token';
import { FIN_INTEGER_MASK } from '../../utils/fin-integer-mask-token';
import { FIN_MONTHS_MASK } from '../../utils/fin-months-mask-token';
import { FIN_PERCENTAGE_MASK } from '../../utils/fin-percentage-mask-token';
import { FIN_REGION_LOCALE_ID } from '../../utils/fin-region-locale-id';
import { FinInputProgressComponent } from '../input-progress/input-progress.component';

const MASK_CONFIG_BASE = {
  [LocaleId.DE]: {
    align: 'left',
    allowNegative: false,
    allowZero: true,
    decimal: ',',
    thousands: '.',
    nullable: true,
    min: null,
    max: null,
    inputMode: NgxCurrencyInputMode.Natural,
    precision: 0,
    prefix: '',
    suffix: '',
  },
  [LocaleId.EN]: {
    align: 'left',
    allowNegative: false,
    allowZero: true,
    decimal: '.',
    thousands: ',',
    nullable: true,
    min: null,
    max: null,
    inputMode: NgxCurrencyInputMode.Natural,
    precision: 0,
    prefix: '',
    suffix: '',
  },
};
const CURRENCY_MASK_CONFIG = {
  [LocaleId.DE]: {
    ...MASK_CONFIG_BASE[LocaleId.DE],
    precision: 2,
    suffix: ' €',
  },
  [LocaleId.EN]: {
    ...MASK_CONFIG_BASE[LocaleId.EN],
    precision: 2,
    prefix: '€',
  },
};
const PERCENTAGE_MASK_CONFIG = {
  [LocaleId.DE]: {
    ...MASK_CONFIG_BASE[LocaleId.DE],
    precision: 3,
    suffix: ' %',
  },
  [LocaleId.EN]: {
    ...MASK_CONFIG_BASE[LocaleId.EN],
    precision: 3,
    suffix: ' %',
  },
};
const DECIMAL_MASK_CONFIG = {
  [LocaleId.DE]: {
    ...MASK_CONFIG_BASE[LocaleId.DE],
    precision: 2,
  },
  [LocaleId.EN]: {
    ...MASK_CONFIG_BASE[LocaleId.EN],
    precision: 2,
  },
};

const meta: Meta<FinInputProgressComponent> = {
  component: FinInputProgressComponent,
  title: 'Fields/Input Progress',
  parameters: {
    docs: {
      description: {
        component: '`import { FinInputModule } from "@fincloud/ui/input"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6547-13078&m=dev',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [CommonModule, FinInputModule],
      providers: [
        {
          provide: FIN_CURRENCY_MASK,
          useValue: CURRENCY_MASK_CONFIG,
        },
        {
          provide: FIN_PERCENTAGE_MASK,
          useValue: PERCENTAGE_MASK_CONFIG,
        },
        {
          provide: FIN_DECIMAL_MASK,
          useValue: DECIMAL_MASK_CONFIG,
        },
        {
          provide: FIN_MONTHS_MASK,
          useValue: MASK_CONFIG_BASE,
        },
        {
          provide: FIN_INTEGER_MASK,
          useValue: MASK_CONFIG_BASE,
        },
        {
          provide: FIN_REGION_LOCALE_ID,
          useValue: LocaleId.DE,
        },
      ],
    }),
  ],
};

export default meta;
type Story = StoryObj<FinInputProgressComponent>;

export const PasswordWithProgressBar: Story = {
  argTypes: {
    progress: {
      control: {
        type: 'number',
      },
    },
    successPercentage: {
      control: {
        type: 'number',
      },
    },
  },
  args: {
    progress: 30,
    successPercentage: 50,
  },

  render: (args) => {
    return {
      props: {
        formControl: new FormControl(''),
        ...args,
      },
      template: `
        <fin-input
          label="Input with progress"
          [formControl]="formControl"
          type="password"
          showProgress
        >
          <ng-container finInputProgress>
            <fin-input-progress
              [progress]="progress"
              [successPercentage]="successPercentage"
            ></fin-input-progress>
          </ng-container>
        </fin-input>
      `,
    };
  },
};
