import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { FinFieldService } from '@fincloud/utils/services';
import { map } from 'rxjs';

@Component({
  selector: 'fin-input-countdown',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './input-countdown.component.html',
  host: {
    class: 'fin-input-countdown',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinInputCountdownComponent implements OnInit {
  maxLength = 0;

  protected getValue$ = this.finFieldService.value$.pipe(
    map((value) => {
      const length =
        this.maxLength - value.length > 0 ? this.maxLength - value.length : 0;

      return {
        length,
      };
    }),
  );

  constructor(private finFieldService: FinFieldService) {}
  ngOnInit(): void {
    this.maxLength = this.finFieldService.maxLength;

    if (this.maxLength === 0) {
      console.warn(
        'fin-input-countdown -',
        'FormControl is missing maxlength="" attribute.',
      );
    }
  }
}
