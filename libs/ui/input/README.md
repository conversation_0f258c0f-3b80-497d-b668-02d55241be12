# Input Components

A comprehensive collection of input components for data entry and form interactions. The input module provides three main components: standard input fields, input with countdown, and input with progress indicators.

## Installation

```bash
npm install @fincloud/ui
```

```typescript
import { FinInputModule } from '@fincloud/ui/input';

@Component({
  imports: [FinInputModule],
  // ...
})
export class YourComponent {}
```

## Basic Usage

### Standard Input

```html
<fin-input label="Email Address" placeholder="Enter your email" [formControl]="emailControl"> </fin-input>
```

```typescript
import { Component } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { FinInputModule } from '@fincloud/ui/input';

@Component({
  selector: 'app-example',
  imports: [FinInputModule, ReactiveFormsModule],
  template: ` <fin-input label="Email Address" placeholder="Enter your email" [formControl]="emailControl"> </fin-input> `,
})
export class ExampleComponent {
  emailControl = new FormControl('');
}
```

## API Reference

### FinInputComponent

#### Selector

- `fin-input`

#### Inputs

| Name                    | Type                                   | Default             | Description                                  |
| ----------------------- | -------------------------------------- | ------------------- | -------------------------------------------- |
| `label`                 | `string`                               | `''`                | Label for the input field                    |
| `placeholder`           | `string`                               | `''`                | Placeholder text for the input               |
| `size`                  | `FinSize.M \| FinSize.L`               | `FinSize.M`         | Size of the input field                      |
| `type`                  | `FinInputType`                         | `FinInputType.TEXT` | Type of input (text, password, number, etc.) |
| `showPasswordButton`    | `boolean`                              | `true`              | Show/hide eye icon for password fields       |
| `showProgress`          | `boolean`                              | `false`             | Show progress bar under the input            |
| `showLoader`            | `boolean`                              | `false`             | Show loader in the input suffix              |
| `readonly`              | `boolean`                              | `false`             | Switch input to readonly mode                |
| `aiEnabled`             | `boolean`                              | `false`             | Enable AI suggestion animations              |
| `maxLength`             | `number`                               | `undefined`         | Maximum character length                     |
| `min`                   | `number`                               | `undefined`         | Minimum value for numeric inputs             |
| `max`                   | `number`                               | `undefined`         | Maximum value for numeric inputs             |
| `dynamicErrorSpace`     | `FinErrorSpace`                        | `undefined`         | Error space configuration                    |
| `externalFieldMessages` | `FinFieldMessageBodyDirective \| null` | `null`              | External field messages                      |

#### Outputs

| Name                | Type                 | Description                                    |
| ------------------- | -------------------- | ---------------------------------------------- |
| `aiSuggestionReady` | `EventEmitter<void>` | Emitted when AI suggestion animation completes |

#### Content Projection

| Slot                 | Description                              |
| -------------------- | ---------------------------------------- |
| `[finInputPrefix]`   | Content displayed as prefix in the input |
| `[finInputSuffix]`   | Content displayed as suffix in the input |
| `[finInputHint]`     | Hint text displayed below the input      |
| `[finInputProgress]` | Progress component for password strength |

### FinInputCountdownComponent

#### Selector

- `fin-input-countdown`

#### Description

Displays a character countdown for input fields with `maxLength` attribute.

#### Usage

Must be used with `finInputSuffix` directive inside a `fin-input` component.

### FinInputProgressComponent

#### Selector

- `fin-input-progress`

#### Inputs

| Name                | Type     | Default | Description                          |
| ------------------- | -------- | ------- | ------------------------------------ |
| `progress`          | `number` | `0`     | Current progress percentage          |
| `successPercentage` | `number` | `50`    | Percentage required to reach success |

#### Description

Displays a progress bar for password strength or other progress indicators.

## Configuration Options

### Enums

#### FinInputType

| Value        | Description                           |
| ------------ | ------------------------------------- |
| `TEXT`       | Standard text input                   |
| `PASSWORD`   | Password input with toggle visibility |
| `NUMBER`     | Numeric input                         |
| `CURRENCY`   | Currency input with formatting        |
| `PERCENTAGE` | Percentage input with formatting      |
| `INTEGER`    | Integer-only input                    |
| `MONTHS`     | Months input with formatting          |
| `DECIMAL`    | Decimal number input                  |

#### FinSize

| Value | Description           |
| ----- | --------------------- |
| `M`   | Medium size (default) |
| `L`   | Large size            |

### Mask Configuration

The input component supports various mask configurations for different input types:

#### Currency Mask

```typescript
import { FIN_CURRENCY_MASK } from '@fincloud/ui/input';

// Provide currency mask configuration
providers: [
  {
    provide: FIN_CURRENCY_MASK,
    useValue: {
      en: { prefix: '$', thousands: ',', decimal: '.' },
      de: { prefix: '€', thousands: '.', decimal: ',' },
    },
  },
];
```

#### Date Mask

```typescript
import { FIN_DATE_MASK } from '@fincloud/ui/input';

// Provide date mask configuration
providers: [
  {
    provide: FIN_DATE_MASK,
    useValue: {
      en: { dateFormat: 'dd/MM/yyyy' },
      de: { dateFormat: 'dd.MM.yyyy' },
    },
  },
];
```

## Advanced Examples

### Input with Prefix and Suffix

```html
<fin-input label="Amount" placeholder="0.00" [formControl]="amountControl" [type]="'currency'">
  <fin-icon finInputPrefix name="attach_money"></fin-icon>
  <span finInputSuffix>USD</span>
</fin-input>
```

### Password Input with Progress

```html
<fin-input label="Password" placeholder="Enter password" [formControl]="passwordControl" [type]="'password'" [showProgress]="true">
  <fin-input-progress finInputProgress [progress]="passwordStrength" [successPercentage]="75"> </fin-input-progress>
</fin-input>
```

### Input with Character Countdown

```html
<fin-input label="Description" placeholder="Enter description" [formControl]="descriptionControl" [maxLength]="100">
  <fin-input-countdown finInputSuffix></fin-input-countdown>
</fin-input>
```

### Input with AI Suggestions

```html
<fin-input label="Smart Input" placeholder="Start typing..." [formControl]="smartControl" [aiEnabled]="true" (aiSuggestionReady)="onAiSuggestionReady()">
  <span finInputHint>AI will suggest completions as you type</span>
</fin-input>
```

```typescript
import { Component } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { FinInputModule, FinInputType } from '@fincloud/ui/input';
import { FinIconModule } from '@fincloud/ui/icon';

@Component({
  selector: 'app-advanced-input',
  imports: [FinInputModule, FinIconModule],
  template: `
    <form [formGroup]="form">
      <!-- Currency Input -->
      <fin-input label="Amount" [formControl]="form.controls.amount" [type]="inputTypes.CURRENCY" [min]="0" [max]="10000">
        <fin-icon finInputPrefix name="attach_money"></fin-icon>
      </fin-input>

      <!-- Password with Strength -->
      <fin-input label="Password" [formControl]="form.controls.password" [type]="inputTypes.PASSWORD" [showProgress]="true">
        <fin-input-progress finInputProgress [progress]="passwordStrength" [successPercentage]="75"> </fin-input-progress>
      </fin-input>

      <!-- Text with Countdown -->
      <fin-input label="Bio" [formControl]="form.controls.bio" [maxLength]="200">
        <fin-input-countdown finInputSuffix></fin-input-countdown>
        <span finInputHint>Tell us about yourself</span>
      </fin-input>
    </form>
  `,
})
export class AdvancedInputComponent {
  inputTypes = FinInputType;
  passwordStrength = 0;

  form = new FormGroup({
    amount: new FormControl(0, [Validators.required, Validators.min(1)]),
    password: new FormControl('', [Validators.required, Validators.minLength(8)]),
    bio: new FormControl('', [Validators.maxLength(200)]),
  });

  onAiSuggestionReady(): void {
    console.log('AI suggestion animation completed');
  }
}
```

## Best Practices

### Do's

- ✅ Use reactive forms with FormControl for proper validation and state management
- ✅ Provide clear, descriptive labels for all input fields
- ✅ Use appropriate input types for different data (currency, password, number)
- ✅ Include helpful hint text to guide users
- ✅ Use prefix/suffix content to enhance user understanding
- ✅ Implement proper validation with meaningful error messages
- ✅ Use character countdown for inputs with length limits
- ✅ Show progress indicators for password strength

### Don'ts

- ❌ Don't use template-driven forms for complex validation
- ❌ Don't forget to provide labels for accessibility
- ❌ Don't use generic placeholder text like "Enter text here"
- ❌ Don't ignore input validation and error states
- ❌ Don't overuse AI suggestions - reserve for appropriate contexts
- ❌ Don't make required fields unclear to users

### Accessibility Guidelines

- Ensure all inputs have associated labels
- Use proper ARIA attributes for error states
- Support keyboard navigation and screen readers
- Provide sufficient color contrast for all states
- Include descriptive error messages
- Use semantic HTML input types

### Performance Tips

- Use OnPush change detection strategy (already implemented)
- Debounce input events for expensive operations
- Lazy load mask configurations when needed
- Use trackBy functions for dynamic input lists

## Troubleshooting

### Common Issues

#### Issue: Input mask not working

**Solution:** Ensure the appropriate mask configuration token is provided.

```typescript
// For currency inputs
import { FIN_CURRENCY_MASK } from '@fincloud/ui/input';

providers: [
  {
    provide: FIN_CURRENCY_MASK,
    useValue: { en: { prefix: '$', thousands: ',', decimal: '.' } },
  },
];
```

#### Issue: AI suggestions not animating

**Solution:** Verify that `aiEnabled` is set to true and the form control value is changing.

```html
<!-- Correct -->
<fin-input [aiEnabled]="true" [formControl]="control"></fin-input>

<!-- Ensure the control value changes trigger the animation -->
```

#### Issue: Progress bar not displaying

**Solution:** Ensure `showProgress` is true and the progress component is properly configured.

```html
<fin-input [showProgress]="true">
  <fin-input-progress finInputProgress [progress]="currentProgress"> </fin-input-progress>
</fin-input>
```

#### Issue: Character countdown not showing

**Solution:** Verify that `maxLength` is set on the input and the countdown component is in the suffix slot.

```html
<fin-input [maxLength]="100">
  <fin-input-countdown finInputSuffix></fin-input-countdown>
</fin-input>
```

#### Issue: Validation errors not displaying

**Solution:** Ensure proper form validation setup and error handling.

```typescript
// Set up validation
control = new FormControl('', [Validators.required, Validators.email]);

// Check for errors in template
<fin-input [formControl]="control">
  <span finInputHint *ngIf="control.errors?.['required']">
    This field is required
  </span>
</fin-input>
```

## Related Components

- [Icon](../icon/README.md) - For adding icons to input prefix/suffix
- [Button](../button/README.md) - For form submission buttons
- [Dropdown](../dropdown/README.md) - For select inputs
- [Date Picker](../date-picker/README.md) - For date inputs
- [Checkbox](../checkbox/README.md) - For boolean inputs
- [Radio](../radio/README.md) - For single selection inputs
- [AI Suggestion](../ai-suggestion/README.md) - For AI-powered input suggestions

## Storybook

View interactive examples and documentation in Storybook:

- [Input Component](https://lib-ui.neoshare.dev/?path=/docs/components-input--docs)
- [Input Countdown Component](https://lib-ui.neoshare.dev/?path=/docs/components-input-countdown--docs)
- [Input Progress Component](https://lib-ui.neoshare.dev/?path=/docs/components-input-progress--docs)

## Changelog

See [CHANGELOG.md](../../CHANGELOG.md) for version history and breaking changes.

## Contributing

See [CONTRIBUTING.md](../../CONTRIBUTING.md) for guidelines on contributing to this component.

## License

This component is part of the @fincloud/ui library and is licensed under [MIT License](../../LICENSE).
