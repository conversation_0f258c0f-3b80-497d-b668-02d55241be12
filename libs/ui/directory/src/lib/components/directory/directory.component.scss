:host {
  @apply fin-flex;
  @apply fin-min-w-[25.6rem];
  @apply fin-w-full;
  @apply fin-flex-col;
  @apply fin-gap-[0.4rem];
  @apply fin-py-[1.4rem];
  @apply fin-px-[1.6rem];
  @apply fin-bg-color-background-neutral-minimal;
  @apply fin-border;
  @apply fin-border-color-border-default-primary;
  @apply fin-rounded-[0.4rem];
  
  &:hover {
    @apply fin-bg-color-hover-neutral;
    @apply fin-cursor-pointer;
  }

  &.fin-directory {
    &-selected {
      @apply fin-border-color-border-default-interactive;
      @apply fin-bg-color-background-secondary-minimal;
    }
    &-disabled {
      @apply fin-bg-color-background-disabled;
      @apply fin-pointer-events-none;
    }
  }
}
