import { CommonModule } from '@angular/common';
import {
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  Input,
} from '@angular/core';
import { FinIconModule } from '@fincloud/ui/icon';

/**
 * A component that represents a directory container for folders.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-directory--docs Storybook Reference}
 */
@Component({
  selector: 'fin-directory',
  standalone: true,
  imports: [CommonModule, FinIconModule],
  templateUrl: './directory.component.html',
  styleUrl: './directory.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    '[class.fin-directory-selected]': 'selected',
    '[class.fin-directory-disabled]': 'disabled',
  },
})
export class FinDirectoryComponent {
  /** Whether the directory is selected. */
  @Input({ transform: booleanAttribute }) selected = false;

  /** Whether the directory is disabled. */
  @Input({ transform: booleanAttribute }) disabled = false;

  protected selectDirectory() {
    //TODO - PHASE 2
    // this.selected = !this.selected;
  }
}
