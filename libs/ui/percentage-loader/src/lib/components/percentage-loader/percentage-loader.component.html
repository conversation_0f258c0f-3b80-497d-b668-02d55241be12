<div class="fin-relative">
  <svg
    xmlns="http://www.w3.org/2000/svg"
    focusable="false"
    width="64"
    height="64"
  >
    <circle
      fill="none"
      cx="32"
      cy="32"
      transform="rotate(-90 32 32)"
      class="fin-stroke-color-background-primary-strong"
      stroke-linecap="round"
      [attr.r]="radius"
      [style.stroke-dasharray]="circumference | finPxToRem"
      [style.stroke-dashoffset]="
        (circumference * (maxStrokeOffset - percentage)) / maxStrokeOffset
          | finPxToRem
      "
      [style.stroke-width.%]="strokeWidth"
    />

    <circle
      fill="none"
      cx="32"
      cy="32"
      class="fin-stroke-color-transparency-primary-subtle"
      [attr.r]="radius"
      [style.stroke-width.%]="strokeWidth"
    />
    <text
      x="50%"
      y="50%"
      class="fin-fill-color-text-light fin-text-body-2-strong"
      text-anchor="middle"
      dy=".35em"
    >
      {{ percentage }}%
    </text>
  </svg>
</div>
