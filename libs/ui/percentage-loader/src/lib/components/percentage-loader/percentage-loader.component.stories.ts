import type { Meta, StoryObj } from '@storybook/angular';
import { FinPercentageLoaderComponent } from './percentage-loader.component';

const meta: Meta<FinPercentageLoaderComponent> = {
  component: FinPercentageLoaderComponent,
  title: 'Components/Percentage Loader',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinPercentageLoaderModule  } from "@fincloud/ui/percentage-loader"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6547-16746&t=bQDNmHrYi2cLv8V5-4',
    },
  },
};
export default meta;
type Story = StoryObj<FinPercentageLoaderComponent>;

export const Primary: Story = {
  args: {
    percentage: 25,
  },
  parameters: {
    backgrounds: {
      default: 'dark',
      values: [{ name: 'dark', value: '#26283E' }],
    },
  },
};
