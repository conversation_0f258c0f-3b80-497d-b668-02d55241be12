import {
  ChangeDetectionStrategy,
  Component,
  Input,
  numberAttribute,
} from '@angular/core';
import { FinPxToRemPipe } from '@fincloud/utils/pipes';

/**
 * A loader that shows progress as a percentage.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-percentage-loader--docs Storybook Reference}
 */
@Component({
  selector: 'fin-percentage-loader',
  standalone: true,
  imports: [FinPxToRemPipe],
  templateUrl: './percentage-loader.component.html',
  styleUrl: './percentage-loader.component.scss',
  host: {
    class: 'fin-percentage-loader',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinPercentageLoaderComponent {
  private _percentage = 0;
  get percentage() {
    return this._percentage;
  }
  /** Value of the progress bar. Defaults to zero. */
  @Input({ transform: numberAttribute }) set percentage(value: number) {
    this._percentage = this.clamp(value);
  }

  private maxPercentage = 100;
  private minPercentage = 0;

  protected radius = 29;
  protected circumference = 2 * Math.PI * this.radius;
  protected strokeWidth = 10;
  // Extra 2 points are needed for linecap effect
  protected maxStrokeOffset = 102;

  private clamp(value: number | undefined | null): number {
    return Math.max(
      this.minPercentage,
      Math.min(value ?? 0, this.maxPercentage),
    );
  }
}
