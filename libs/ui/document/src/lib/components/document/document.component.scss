:host {
  @apply fin-min-w-[25.6rem];
  @apply fin-h-[12.6rem];
  @apply fin-flex;
  @apply fin-border;
  @apply fin-border-color-border-default-primary;
  @apply fin-bg-color-background-neutral-minimal;
  @apply fin-rounded-[0.4rem];
  @apply fin-box-content;
  @apply fin-overflow-hidden;

  &:hover {
    @apply fin-cursor-pointer;
    @apply fin-bg-color-hover-neutral;
  }

  &.fin-document {
    &-selected {
      @apply fin-bg-color-background-secondary-minimal;
      @apply fin-border-color-border-default-interactive;
    }
    &-disabled {
      @apply fin-bg-color-background-disabled;
      @apply fin-border-color-border-default-inactive;
      @apply fin-cursor-not-allowed;
    }
    &-error {
      @apply fin-border-color-border-default-error;
    }
    &-warning {
      @apply fin-border-color-border-default-warning;
    }
  }
}
