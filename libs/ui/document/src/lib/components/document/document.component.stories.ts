import { CommonModule } from '@angular/common';
import { FinIconModule } from '@fincloud/ui/icon';
import {
  Args,
  componentWrapperDecorator,
  moduleMetadata,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { FinDocumentComponent } from './document.component';

const meta: Meta<FinDocumentComponent> = {
  component: FinDocumentComponent,
  title: 'Components/Document',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinDocumentModule } from "@fincloud/ui/document"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=28132-1823&t=hVljdxDT9COnYFOi-4',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [CommonModule, FinIconModule],
    }),
    componentWrapperDecorator(
      (story) => `<div class="fin-w-[25.6rem]">${story}</div>`,
    ),
  ],
};

export default meta;
type Story = StoryObj<
  FinDocumentComponent & {
    finFieldLabel: string;
    finActions: string;
  }
>;

const imageSrc = 'assets/document/neoshare.png';

const finActionsTemplate = `
  <ng-container finActions>
    <div class="fin-flex fin-flex-row fin-justify-between fin-w-full">
      <div class="fin-flex fin-flex-row fin-gap-3">
        <fin-icon size="s" name="chat_bubble"></fin-icon>
        <fin-icon size="s" name="download"></fin-icon>
        <fin-icon size="s" name="upload"></fin-icon>
        <fin-icon size="s" name="more_vert"></fin-icon>
      </div>
    </div>
  </ng-container>
`;

const renderDocument = (args: Args, missingDocument?: boolean) => ({
  props: {
    ...args,
    imageSrc:
      args['showLoader'] || args['error'] || missingDocument ? '' : imageSrc,
    dateAndTime: new Date(args['dateAndTime']).toLocaleString('de-DE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    }),
  },
  template: `
    <fin-document
      [label]="label"
      [description]="description"
      [warning]="warning"
      [showLoader]="showLoader"
      [dateAndTime]="dateAndTime"
      [selected]="selected"
      [disabled]="disabled"
      [imageSrc]="imageSrc"
      [error]="error"
    >
    <ng-container finIcon>
      <fin-icon
        *ngIf="!error"
        size="l"
        src="assets/document/svgPlaceholderIcon.svg"
      ></fin-icon>
      <fin-icon
        *ngIf="error"
        size="l"
        name="warning"
        class="fin-text-color-icons-minimal"
      ></fin-icon>
    </ng-container>
         ${args['disabled'] ? '' : finActionsTemplate}
    </fin-document>
    `,
});

export const Default: Story = {
  argTypes: {
    dateAndTime: {
      control: { type: 'date' },
    },
    finFieldLabel: {
      description: 'Custom template for the document label. If a template is passed, the `label` input should be skipped.',
      defaultValue: { summary: 'attribute' },
      control: false,
      table: {
        category: 'Containers',
      },
    },
    finActions: {
      description: 'Document actions.',
      defaultValue: { summary: 'attribute' },
      control: false,
      table: {
        category: 'Containers',
      },
    },
  },
  args: {
    label: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do',
    description:
      'Lorem ipsum dolor sit amet consectetur adipisicing elit. Dolore libero nulla quas dignissimos, quia rerum repellendus',
    disabled: false,
    selected: false,
    warning: false,
    showLoader: false,
    error: false,
    dateAndTime: '3.04.2025 12:13:14',
  },
  render: (args) => renderDocument(args),
};

export const Selected: Story = {
  args: {
    ...Default.args,
    selected: true,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => renderDocument(args),
};

export const Error: Story = {
  args: {
    ...Default.args,
    error: true,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => renderDocument(args),
};

export const Warning: Story = {
  args: {
    ...Default.args,
    warning: true,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => renderDocument(args),
};

export const Loading: Story = {
  args: {
    ...Default.args,
    showLoader: true,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => renderDocument(args),
};

export const Disabled: Story = {
  args: {
    ...Default.args,
    disabled: true,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => renderDocument(args),
};

export const EmptyOrMissingDocumentPreview: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => renderDocument(args, true),
};

export const DocumentWithCustomLabel: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        imageSrc: imageSrc,
      },
      template: `
        <fin-document
          [description]="description"
          [warning]="warning"
          [showLoader]="showLoader"
          [dateAndTime]="dateAndTime"
          [selected]="selected"
          [disabled]="disabled"
          [imageSrc]="imageSrc"
          [error]="error"
        >
          <ng-container finFieldLabel>
              <mark>Highlighted document title</mark>
          </ng-container>
          ${finActionsTemplate}
        </fin-document>
    `,
    }
  }
};
