import { CommonModule } from '@angular/common';
import {
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  Input,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { FinContainerModule } from '@fincloud/ui/container';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinLoaderModule } from '@fincloud/ui/loader';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';

/**
 * A document component used to display structured information.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-document--docs Storybook Reference}
 */
@Component({
  selector: 'fin-document',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatCardModule,
    FinTruncateTextModule,
    FinContainerModule,
    FinLoaderModule,
    FinIconModule,
  ],
  templateUrl: './document.component.html',
  styleUrl: './document.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    '[class.fin-document-selected]': 'selected',
    '[class.fin-document-disabled]': 'disabled',
    '[class.fin-document-error]': 'error',
    '[class.fin-document-warning]': 'warning',
  },
})
export class FinDocumentComponent {
  /** Whether the document is disabled. */
  @Input({ transform: booleanAttribute }) disabled = false;

  /** Whether the document is uploading. */
  @Input({ transform: booleanAttribute }) showLoader = false;

  /** Whether the document is in warning state. */
  @Input({ transform: booleanAttribute }) warning = false;

  /** Whether the document is in error state. */
  @Input({ transform: booleanAttribute }) error = false;

  /** Whether the document is in selected state. */
  @Input({ transform: booleanAttribute }) selected = false;

  /** Label for the the document. */
  @Input() label = '';

  /** Description for the document. */
  @Input() description = '';

  /** Date And Time for the document. */
  @Input() dateAndTime = '';

  /** Image for the document. */
  @Input() imageSrc!: string | ArrayBuffer;

  protected selectDocument() {
    //TODO - PHASE 2
    // this.selected = !this.selected;
  }
}
