<div
  class="fin-flex fin-justify-center fin-items-center fin-bg-color-background-disabled fin-w-36"
>
  @if (imageSrc && !showLoader) {
    <img
      class="fin-h-full fin-object-cover fin-w-full"
      [class.fin-opacity-40]="disabled"
      [src]="imageSrc"
    />
  } @else if (showLoader) {
    <fin-loader
      class="fin-flex fin-justify-center"
      [hide]="!showLoader"
    ></fin-loader>
  } @else {
    <ng-content select="[finIcon]"></ng-content>
  }
</div>

<div
  class="fin-p-[1.6rem] fin-flex fin-flex-col fin-w-[72%]"
  [class.fin-opacity-40]="disabled"
>
  <div class="fin-pb-[0.8rem]">
    <div finTruncateText class="fin-text-body-2-strong fin-pb-[0.2rem]">
      @if (label) {
        {{ label }}
      } @else {
        <ng-content select="[finFieldLabel]"></ng-content>
      }
    </div>

    <div
      finTruncateText
      class="fin-text-body-3-moderate fin-pb-[1.2rem] fin-text-color-text-secondary"
    >
      {{ description }}
    </div>

    <div class="fin-text-body-3-moderate">
      {{ dateAndTime }}
    </div>
  </div>

  <ng-content select="[finActions]"></ng-content>
</div>
