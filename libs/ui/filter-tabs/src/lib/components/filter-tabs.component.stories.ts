import { FormControl } from '@angular/forms';
import { Meta, StoryObj } from '@storybook/angular';
import { FinFilterTabsComponent } from './filter-tabs.component';

const meta: Meta<FinFilterTabsComponent> = {
  component: FinFilterTabsComponent,
  title: 'Components/Filter Tabs',
  parameters: {
    docs: {
      description: {
        component: '`import { FinFilterTabs } from "@fincloud/ui/filter-tabs"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=18200-1023&node-type=canvas&t=QuvK4M5zDvcOr8DK-0',
    },
  },
};
export default meta;
type Story = StoryObj<FinFilterTabsComponent>;

export const Selected: Story = {
  args: {
    count: 99,
    label: 'Pending',
    value: 'Pending',
    isSelected: false,
  },
  argTypes: {
    selectedTabChange: {
      control: false,
      type: 'function',
    },
  },
  render: (args) => ({
    props: {
      ...args,
      formControl: new FormControl('Pending'),
    },
  }),
};

export const Default: Story = {
  args: {
    count: 99,
    label: 'Closed',
    value: 'Closed',
  },
  argTypes: {
    ...Selected.argTypes,
  },
  render: (args) => ({
    props: {
      ...args,
      formControl: new FormControl(),
    },
  }),
};

export const Multi: Story = {
  argTypes: {},
  args: {
    ...Default.args,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl('Pending'),
      },
      template: `
      <div class="fin-flex fin-gap-4">
         <fin-filter-tabs label="Pending" [count]="100"  value="Pending"  [formControl]="formControl"></fin-filter-tabs>

        <fin-filter-tabs label="Closed" [count]="80"  value="Closed"  [formControl]="formControl"></fin-filter-tabs>

        <fin-filter-tabs label="Delegated" [count]="90"  value="Delegated"  [formControl]="formControl"></fin-filter-tabs>
      </div>
      `,
    };
  },
};
