import { CommonModule } from '@angular/common';
import {
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  forwardRef,
  Injector,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { FinBadgeIndicatorComponent, FinBadgeType } from '@fincloud/ui/badges';
import { FinAngularMaterialModule } from '@fincloud/utils/angular-material';
import { FinControlValueAccessor } from '@fincloud/utils/control-value-accessor';
import { Observable, shareReplay, startWith } from 'rxjs';

/**
 * Tabs used to filter content or data views.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-filter-tabs--docs Storybook Reference}
 */
@Component({
  selector: 'fin-filter-tabs',
  standalone: true,
  imports: [
    CommonModule,
    FinAngularMaterialModule,
    FinBadgeIndicatorComponent,
    ReactiveFormsModule,
  ],
  templateUrl: './filter-tabs.component.html',
  styleUrl: './filter-tabs.component.scss',
  host: {
    class: 'fin-filter-tabs',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FinFilterTabsComponent),
      multi: true,
    },
  ],
})
export class FinFilterTabsComponent
  extends FinControlValueAccessor
  implements OnInit
{
  /** Label for the tab. */
  @Input({ required: true }) label = '';

  /** Number shown in the badge */
  @Input() count = 0;

  /** Value for the tab. */
  @Input({ required: true }) value = '';

  /** Whether the tab is selected. */
  @Input({ transform: booleanAttribute }) isSelected = false;

  /** Event emitted when the tab selection has changed. */
  @Output() selectedTabChange = new EventEmitter<string>();

  protected controlValue$ = new Observable();

  protected get getBadgeType() {
    if (this.control?.value === this.value || this.isSelected) {
      return FinBadgeType.ELLIPSE;
    }
    return FinBadgeType.DEFAULT;
  }

  constructor(injector: Injector) {
    super(injector);
  }

  ngOnInit() {
    this.controlValue$ = this.control?.valueChanges.pipe(
      startWith(this.control?.value),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  protected onSelectedTabChange() {
    this.selectedTabChange.emit(this.value);
  }
}
