<label
  class="fin-filter-tab fin-flex fin-w-full fin-items-center fin-justify-between fin-gap-4 fin-rounded-[0.4rem] fin-px-[1.6rem] fin-py-[1rem]"
  [ngClass]="{
    'fin-bg-color-background-dark-strong':
      (controlValue$ | async) === value || isSelected,
    'fin-bg-color-background-tertiary-minimal hover:fin-bg-color-background-tertiary-subtle':
      (controlValue$ | async) !== value && !isSelected,
  }"
>
  <input
    type="radio"
    [value]="value"
    [formControl]="control"
    class="fin-hidden"
    (change)="onSelectedTabChange()"
  />
  <span
    class="fin-text-body-2-strong"
    [ngClass]="{
      'fin-text-color-text-light':
        (controlValue$ | async) === value || isSelected,
      'fin-text-color-text-primary':
        (controlValue$ | async) !== value && !isSelected,
    }"
  >
    {{ label }}
  </span>
  <fin-badge-indicator
    [count]="count"
    [type]="getBadgeType"
  ></fin-badge-indicator>
</label>
