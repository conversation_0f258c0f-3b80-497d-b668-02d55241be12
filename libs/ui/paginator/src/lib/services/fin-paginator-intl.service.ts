import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class FinPaginatorIntlService {
  showLabel = 'Show:';
  goToPageLabel = 'Go to page:';

  getRangeLabel: (page: number, pageSize: number, length: number) => string = (
    page: number,
    pageSize: number,
    length: number,
  ) => {
    const startItem = (page - 1) * pageSize + 1;
    const endItem = Math.min(page * pageSize, length);

    return `Showing ${startItem} - ${endItem} of ${length} entries`;
  };

  getCompactRangeLabel: (
    page: number,
    pageSize: number,
    length: number,
  ) => string = (page: number, pageSize: number, length: number) => {
    const startItem = (page - 1) * pageSize + 1;

    return `${startItem} of ${length} entries`;
  };
}
