import { Optional, SkipSelf } from '@angular/core';
import { FinPaginatorIntlService } from '../services/fin-paginator-intl.service';
import { FIN_PAGINATOR_INTL_PROVIDER_FACTORY } from './fin-paginator-intl-provider-factory';

export const FIN_PAGINATOR_INTL_PROVIDER = {
  // If there is already an FinPaginatorIntlService available, use that. Otherwise, provide a new one.
  provide: FinPaginatorIntlService,
  deps: [[new Optional(), new SkipSelf(), FinPaginatorIntlService]],
  useFactory: FIN_PAGINATOR_INTL_PROVIDER_FACTORY,
};
