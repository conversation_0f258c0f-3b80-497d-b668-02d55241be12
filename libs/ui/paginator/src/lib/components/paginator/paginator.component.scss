::ng-deep {
  .fin-select-page-field {
    width: 6.6rem;

    .mat-mdc {
      &-text-field-wrapper,
      &-form-field-flex {
        height: 2.4rem;
      }

      &-form-field-flex {
        display: flex;
        align-items: center;

        .mat-mdc-select-value {
          color: theme('colors.color-text-primary');
        }
      }

      &-text-field-wrapper {
        @apply fin-px-[0.8rem] #{!important};
      }
    }
  }

  .mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled)
    span.mdc-list-item__primary-text {
    @apply fin-text-color-text-primary;
    @apply fin-font-text-body-3-strong-weight;
  }

  mat-option:hover.mat-mdc-option.mdc-list-item--selected:not(
      .mdc-list-item--disabled
    ) {
    @apply fin-bg-color-hover-tertiary;
  }

  mat-option:hover.mat-mdc-option:not(.mdc-list-item--disabled) {
    @apply fin-bg-color-hover-tertiary;
  }
}

button.fin-page-button:hover {
  @apply fin-bg-color-hover-tertiary;
}
