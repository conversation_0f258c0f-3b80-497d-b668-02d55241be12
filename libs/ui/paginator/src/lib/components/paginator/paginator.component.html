<div class="fin-flex fin-items-center fin-justify-between">
  <div
    class="fin-flex fin-items-center fin-text-body-2-moderate fin-text-color-text-primary"
  >
    <div>
      {{ entriesDescription }}
    </div>

    @if (showPageSize) {
      <div class="fin-flex fin-items-center fin-justify-center fin-ms-[2.4rem]">
        <span class="fin-me-[0.8rem]">{{ showLabel }}</span>

        <ng-container
          [ngTemplateOutlet]="finSelect"
          [ngTemplateOutletContext]="{
            formControl: pageSizeOptionsField,
            options: pageSizeOptions,
            type: selectionType.SELECT_PAGE_SIZE,
          }"
        ></ng-container>
      </div>
    }

    @if (showGoToPage) {
      <div class="fin-flex fin-items-center fin-justify-center fin-ms-[2.4rem]">
        <span class="fin-me-[0.8rem]">{{ goToPageLabel }}</span>

        <ng-container
          [ngTemplateOutlet]="finSelect"
          [ngTemplateOutletContext]="{
            formControl: selectPageField,
            options: pages,
            type: selectionType.GO_TO_PAGE,
          }"
        ></ng-container>
      </div>
    }
  </div>

  <div class="fin-flex fin-items-center">
    <button
      fin-button-icon
      [appearance]="appearance.STEALTH"
      [shape]="shape.RECTANGLE"
      class="fin-mb-[0.4rem] fin-me-[1.0rem] fin-h-[2.4rem] fin-w-[2.4rem]"
      (click)="previousPage()"
      [disabled]="pageNumber === 1"
    >
      <fin-icon name="navigate_before" [size]="size.L"></fin-icon>
    </button>

    @for (page of availablePages; track $index) {
      <button
        class="fin-page-button fin-text-body-3-moderate fin-me-[1.0rem] fin-min-h-[2.4rem] fin-min-w-[2.4rem] fin-rounded-[0.4rem]"
        [ngClass]="{
          'fin-pointer-events-none': page === pagesSeparator,
          'fin-bg-color-background-dark-strong fin-font-text-body-3-moderate-weight fin-text-color-text-light':
            page === pageNumber,
        }"
        (click)="goToPage(+page)"
      >
        {{ page }}
      </button>
    }

    <button
      fin-button-icon
      [appearance]="appearance.STEALTH"
      [shape]="shape.RECTANGLE"
      class="fin-mb-[0.4rem] fin-h-[2.4rem] fin-w-[2.4rem]"
      (click)="nextPage()"
      [disabled]="pageNumber === totalPages"
    >
      <fin-icon name="navigate_next" [size]="size.L"></fin-icon>
    </button>
  </div>
</div>

<!-- templates -->
<ng-template
  #finSelect
  let-formControl="formControl"
  let-options="options"
  let-type="type"
>
  <mat-form-field class="fin-select-page-field">
    <mat-select
      #select
      [formControl]="formControl"
      hideSingleSelectionIndicator
      (selectionChange)="onSelectionChange($event.value, type)"
    >
      @for (option of options; track $index) {
        <mat-option class="fin-text-center" [value]="option">
          {{ option }}
        </mat-option>
      }
    </mat-select>

    <fin-icon
      matSuffix
      [name]="select.panelOpen ? 'keyboard_arrow_up' : 'keyboard_arrow_down'"
      [size]="size.M"
    ></fin-icon>
  </mat-form-field>
</ng-template>
