import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
} from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import {
  FinButtonAppearance,
  FinButtonModule,
  FinButtonShape,
} from '@fincloud/ui/button';
import { FinIconComponent } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';
import { FinSelectionType } from '../../enums/fin-selection-type';
import { FinPaginatorEvent } from '../../models/fin-paginator-event';
import { FinPaginatorIntlService } from '../../services/fin-paginator-intl.service';

/**
 * A component that allows navigation through paginated content.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-paginator--docs Storybook Reference}
 */
@Component({
  selector: 'fin-paginator',
  standalone: true,
  imports: [
    CommonModule,
    FinIconComponent,
    FinButtonModule,
    ReactiveFormsModule,
    MatSelectModule,
  ],
  templateUrl: './paginator.component.html',
  styleUrl: './paginator.component.scss',
  host: {
    class: 'fin-paginator',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinPaginatorComponent implements OnChanges {
  /** Total items count. */
  @Input() totalItems = 0;

  /** Items per page. */
  @Input() pageSize = 10;

  /** Current page number. */
  @Input() pageNumber = 1;

  /** Page size options. */
  @Input() pageSizeOptions: number[] = [10, 20, 50, 100];

  /** Show page size dropdown. */
  @Input() showPageSize = false;

  /** Show go to page dropdown. */
  @Input() showGoToPage = false;

  /** Event emitted when the paginator changes the page size or page index. */
  @Output() pageChange: EventEmitter<FinPaginatorEvent> =
    new EventEmitter<FinPaginatorEvent>();

  protected size = FinSize;
  protected appearance = FinButtonAppearance;
  protected shape = FinButtonShape;
  protected selectionType = FinSelectionType;
  protected pagesSeparator = '...';
  protected entriesDescription = '';
  protected totalPages = 0;
  protected pages: number[] = [];
  protected availablePages: (string | number)[] = []; // example: [1, "...", 3, 4, 5, "...", 9]
  protected pageSizeOptionsField = new FormControl();
  protected selectPageField = new FormControl();
  protected showLabel = this.finPaginatorIntlService.showLabel;
  protected goToPageLabel = this.finPaginatorIntlService.goToPageLabel;

  constructor(private finPaginatorIntlService: FinPaginatorIntlService) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes['totalItems'] ||
      changes['pageSize'] ||
      changes['pageNumber'] ||
      changes['pageSizeOptions']
    ) {
      this.updatePaginator();
    }
  }

  protected onSelectionChange(page: number, type: FinSelectionType) {
    if (type === FinSelectionType.GO_TO_PAGE) {
      this.goToPage(page);
    }

    if (type === FinSelectionType.SELECT_PAGE_SIZE) {
      this.selectPageSize(page);
    }
  }

  protected goToPage(pageNumber: number) {
    this.onPageChange(this.pageSize, pageNumber);
  }

  protected previousPage() {
    this.onPageChange(this.pageSize, this.pageNumber - 1);
  }

  protected nextPage() {
    this.onPageChange(this.pageSize, this.pageNumber + 1);
  }

  private updatePaginator() {
    this.totalPages = Math.ceil(this.totalItems / this.pageSize);
    this.entriesDescription = this.finPaginatorIntlService.getRangeLabel(
      this.pageNumber,
      this.pageSize,
      this.totalItems,
    );
    this.pages = this.getPages();
    this.availablePages = this.getAvailablePages();
    this.pageSizeOptionsField.setValue(this.pageSize);
    this.selectPageField.setValue(this.pageNumber);
  }

  private getAvailablePages(): (number | string)[] {
    const pages: (number | string)[] = [];

    // Case 1: show all pages if totalPages <= 6
    if (this.totalPages <= 6) {
      pages.push(...this.addPages(1, this.totalPages));

      // Case 2: totalPages > 6
    } else {
      pages.push(1);

      // Case 2.1: if any of the first 3 pages is selected (example: 1 2 3? 4? ... 9)
      if (this.pageNumber <= 3) {
        pages.push(
          ...this.addPages(2, this.pageNumber + 1),
          this.pagesSeparator,
          this.totalPages,
        );

        // Case 2.2: if any of the last 3 pages is selected (example: 1 ... 6? 7? 8 9)
      } else if (this.pageNumber >= this.totalPages - 2) {
        pages.push(
          this.pagesSeparator,
          ...this.addPages(this.totalPages - 3, this.totalPages),
        );

        // Case 2.3: if any of the middle pages is selected (example: 1 ... 4 5 6 ... 9)
      } else {
        pages.push(
          this.pagesSeparator,
          this.pageNumber - 1,
          this.pageNumber,
          this.pageNumber + 1,
          this.pagesSeparator,
          this.totalPages,
        );
      }
    }

    return pages;
  }

  private addPages(start: number, end: number): number[] {
    const pages: number[] = [];

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    return pages;
  }

  private getPages(): number[] {
    return Array.from(
      { length: this.totalPages },
      (currentValue, index) => index + 1,
    );
  }

  private onPageChange(pageSize: number, pageNumber: number) {
    this.pageChange.emit({ pageSize, pageNumber });
  }

  private selectPageSize(pageSize: number) {
    this.onPageChange(pageSize, 1);
  }
}
