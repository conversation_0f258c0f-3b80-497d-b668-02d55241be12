import {
  componentWrapperDecorator,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { useArgs } from '@storybook/preview-api';
import { FinPaginatorEvent } from '../../models/fin-paginator-event';
import { FinPaginatorComponent } from './paginator.component';

const meta: Meta<FinPaginatorComponent> = {
  component: FinPaginatorComponent,
  title: 'Components/Paginator',
  decorators: [
    componentWrapperDecorator(
      (story) => `<div class="fin-min-w-[900px]">${story}</div>`,
    ),
  ],
  argTypes: {
    showPageSize: {
      control: { type: 'boolean' },
    },
    showGoToPage: {
      control: { type: 'boolean' },
    },
    pageChange: {
      control: { type: 'boolean' },
    },
  },
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinPaginatorModule } from "@fincloud/ui/paginator"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6127-45189&t=r2TvIAzplZo8vPDp-0',
    },
  },
};
export default meta;
type Story = StoryObj<FinPaginatorComponent>;

export const Primary: Story = {
  args: {
    totalItems: 500,
    pageSize: 20,
    pageNumber: 5,
    pageSizeOptions: [10, 20, 50, 100],
    showPageSize: true,
    showGoToPage: true,
  },
  render: (args) => {
    const [updatedArgs, updateArgs] = useArgs();

    const onPageChange = ({ pageSize, pageNumber }: FinPaginatorEvent) => {
      updateArgs({ pageSize, pageNumber });
    };

    return {
      props: {
        ...args,
        pageChange: onPageChange,
      },
    };
  },
};
