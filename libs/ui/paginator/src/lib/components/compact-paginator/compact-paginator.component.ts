import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { FinPaginatorEvent } from '../../models/fin-paginator-event';
import { FinSize } from '@fincloud/ui/types';
import { FinSelectionType } from '../../enums/fin-selection-type';
import { FinPaginatorIntlService } from '../../services/fin-paginator-intl.service';
import { FinIconComponent } from '@fincloud/ui/icon';
import { FinButtonModule } from '@fincloud/ui/button';

@Component({
  selector: 'fin-compact-paginator',
  standalone: true,
  imports: [CommonModule, FinIconComponent, FinButtonModule],
  templateUrl: './compact-paginator.component.html',
  styleUrl: './compact-paginator.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinCompactPaginatorComponent implements OnChanges {

  /** Total items count. */
  @Input() totalItems = 0;

  /** Items per page. */
  @Input() pageSize = 1;

  /** Current page number. */
  @Input() pageNumber = 1;

  /** Event emitted when the paginator changes the page size or page index. */
  @Output() pageChange: EventEmitter<FinPaginatorEvent> =
    new EventEmitter<FinPaginatorEvent>();


    protected size = FinSize;
    protected selectionType = FinSelectionType;
    protected entriesDescription = '';
    protected totalPages = 0;

    constructor(private finPaginatorIntlService: FinPaginatorIntlService) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes['totalItems'] ||
      changes['pageSize'] ||
      changes['pageNumber'] ||
      changes['pageSizeOptions']
    ) {
      this.updatePaginator();
    }
  }


  protected previousPage() {
    this.onPageChange(this.pageSize, this.pageNumber - 1);
  }

  protected nextPage() {
    this.onPageChange(this.pageSize, this.pageNumber + 1);
  }

  private updatePaginator() {
    this.totalPages = Math.ceil(this.totalItems / this.pageSize);
    this.entriesDescription = this.finPaginatorIntlService.getCompactRangeLabel(
      this.pageNumber,
      this.pageSize,
      this.totalItems,
    );
  }


  private onPageChange(pageSize: number, pageNumber: number) {
    this.pageChange.emit({ pageSize, pageNumber });
  }
}
