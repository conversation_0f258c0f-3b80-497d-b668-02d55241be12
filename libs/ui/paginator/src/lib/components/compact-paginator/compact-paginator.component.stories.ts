import {
  componentWrapperDecorator,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { useArgs } from '@storybook/preview-api';
import { FinPaginatorEvent } from '../../models/fin-paginator-event';
import { FinCompactPaginatorComponent } from './compact-paginator.component';

const meta: Meta<FinCompactPaginatorComponent> = {
  component: FinCompactPaginatorComponent,
  title: 'Components/Compact Paginator',
  decorators: [
    componentWrapperDecorator(
      (story) => `<div class="fin-min-w-[900px]">${story}</div>`,
    ),
  ],
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinPaginatorModule } from "@fincloud/ui/paginator"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6127-45189&t=r2TvIAzplZo8vPDp-0',
    },
  },
};
export default meta;
type Story = StoryObj<FinCompactPaginatorComponent>;

export const Primary: Story = {
  argTypes: {
    pageChange: {
      control: { type: 'boolean' },
    },
  },
  args: {
    totalItems: 500,
    pageSize: 20,
    pageNumber: 5,
  },
  render: (args) => {
    const [, updateArgs] = useArgs();

    const onPageChange = ({ pageSize, pageNumber }: FinPaginatorEvent) => {
      updateArgs({ pageSize, pageNumber });
    };

    return {
      props: {
        ...args,
        pageChange: onPageChange,
      },
    };
  },
};
