<div class="fin-flex fin-items-center fin-gap-5">
  <div class="fin-flex fin-items-center">
    <button
      fin-button-stealth
      class="fin-me-[1.0rem] fin-h-[2.4rem] fin-w-[2.4rem]"
      (click)="previousPage()"
      [disabled]="pageNumber === 1"
    >
      <fin-icon name="navigate_before" [size]="size.L"></fin-icon>
    </button>
    <button
      fin-button-stealth
      class="fin-h-[2.4rem] fin-w-[2.4rem]"
      (click)="nextPage()"
      [disabled]="pageNumber === totalPages"
    >
      <fin-icon name="navigate_next" [size]="size.L"></fin-icon>
    </button>
  </div>

  <div
    class="fin-flex fin-items-center fin-text-body-2-moderate fin-text-color-text-primary"
  >
    <div>{{ entriesDescription }}</div>
  </div>
</div>
