:host {
  display: block;
}

.fin-datatable.mat-mdc-table {
  background-color: theme('colors.transparent');

  .mat-mdc-header-row {
    background-color: theme('colors.white');
    min-height: auto;
    user-select: none;

    .mat-mdc-header-cell {
      @apply fin-text-body-2-strong;
      @apply fin-px-[2.4rem];
      color: theme('colors.color-text-tertiary');
      border: none;
    }
  }

  .mat-mdc-row {
    background-color: theme('colors.white');
    min-height: auto;

    .mat-mdc-cell {
      @apply fin-text-body-2-strong;
      @apply fin-px-[2.4rem];
      border: none;
    }

    &.expanded-detail-row {
      display: grid;
      grid-template-rows: 0fr;
      transition: grid-template-rows 225ms cubic-bezier(0.4, 0, 0.2, 1);
      .expanded-detail-row-container {
        @apply fin-text-body-3-moderate;
        width: 100%;
      }
    }

    // Nested table
    &.expanded-detail-row:has(.fin-datatable) {
      .mat-mdc-cell:has(.fin-datatable),
      .expanded-detail-row-container:has(.fin-datatable) {
        padding: 0;

        .mat-mdc-header-row {
          height: 3.2rem !important;
          background-color: theme('colors.color-surface-tertiary');
          color: theme('colors.color-text-secondary');

          .mat-mdc-header-cell {
            @apply fin-text-body-3-strong;
            text-transform: uppercase;
          }
        }

        .fin-row-border {
          .mat-mdc-row:last-child {
            border-bottom: none;
          }
        }
      }
    }

    &.expanded {
      grid-template-rows: 1fr;
      .expanded-detail-row-container {
        @apply fin-py-[2rem];
      }
    }

    &:hover:not(.expanded-detail-row):not(.header-group) {
      ::ng-deep {
        .fin-table-content-hidden {
          display: none !important;
        }

        .fin-table-content-block {
          display: block !important;
        }
      }

      background-color: theme('colors.color-hover-tertiary');
    }

    &.header-group {
      background-color: theme('colors.color-surface-tertiary');
      color: theme('colors.color-text-secondary');
      border-bottom: 0.1rem solid theme('colors.color-border-default-primary');
      height: 3rem;
      @apply fin-py-[0.6rem];

      .mat-mdc-cell {
        @apply fin-text-body-3-strong;
      }
    }
  }

  .header-group + .mat-mdc-row {
    display: none;
  }

  // hasBorderRadius
  &.fin-border-radius > {
    .mat-mdc-row {
      border-radius: 0.4rem;
    }

    .mat-mdc-row:not(.expanded-detail-row.expanded) {
      .mat-mdc-cell:first-child {
        border-top-left-radius: 0.4rem;
      }

      .mat-mdc-cell:last-child {
        border-top-right-radius: 0.4rem;
      }
    }

    .mat-mdc-row:not(.expanded-row) {
      .mat-mdc-cell:first-child {
        border-bottom-left-radius: 0.4rem;
      }

      .mat-mdc-cell:last-child {
        border-bottom-right-radius: 0.4rem;
      }
    }
  }

  // hasRowBorder
  &.fin-row-border > {
    .mat-mdc-header-row,
    .mat-mdc-row:not(.expanded-detail-row):not(.header-group),
    .mat-mdc-row.expanded {
      border-bottom: 0.1rem solid theme('colors.color-border-default-primary');
    }
  }

  // hasRowPartialBorder
  &.fin-row-partial-border > {
    .mat-mdc-header-row {
      border-bottom: 0.1rem solid theme('colors.color-border-default-primary');
    }

    .mat-mdc-row:not(.expanded-detail-row),
    .mat-mdc-row.expanded {
      border: none;
      position: relative;
      padding-left: 2.4rem;
      &::before {
        content: '';
        position: absolute;
        top: -0.05rem;
        left: 2.4rem;
        width: calc(100% - 2.4rem);
      }

      &:not(:nth-child(2)) {
        &::before {
          border-top: 0.1rem solid theme('colors.color-border-default-primary');
        }
      }
    }
  }

  // hasRowSpacing
  &.fin-row-spacing > {
    .mat-mdc-header-row,
    .mat-mdc-row:not(.expanded-detail-row):not(.expanded-row):not(
        :last-of-type
      ),
    .mat-mdc-row.expanded-detail-row.expanded {
      margin-bottom: 0.6rem;
    }
  }

  // hideHeader
  &.fin-hide-header > {
    .mat-mdc-header-row {
      display: none;
    }
  }

  &.fin-column-mode {
    &-standard {
      .mat-mdc-header-row,
      .mat-mdc-header-row .mat-mdc-header-cell,
      .mat-mdc-row:not(.expanded-detail-row),
      .mat-mdc-row:not(.expanded-detail-row) .mat-mdc-cell {
        display: inline-block;
      }

      .mat-mdc-header-row .mat-mdc-header-cell {
        vertical-align: bottom;
      }
    }

    &-force {
      .mat-mdc-header-row .mat-mdc-header-cell,
      .mat-mdc-row .mat-mdc-cell {
        flex-grow: 1 !important;
      }
    }
  }
}
