import {
  componentWrapperDecorator,
  moduleMetadata,
  type Meta,
  type StoryObj,
} from '@storybook/angular';

import { CommonModule } from '@angular/common';
import { FinAvatarModule } from '@fincloud/ui/avatar-default';
import { FinBadgesModule } from '@fincloud/ui/badges';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinEmptyStateModule } from '@fincloud/ui/empty-state';
import { FinExpansionPanelModule } from '@fincloud/ui/expansion-panel';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinTableColumnMode } from '../../enums/fin-table-column-mode';
import { FinTableModule } from '../../table.module';
import { FinTableComponent } from './table.component';

const meta: Meta<FinTableComponent<unknown>> = {
  component: FinTableComponent,
  title: 'Components/Table',
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        FinAvatarModule,
        FinButtonModule,
        FinIconModule,
        FinBadgesModule,
        FinExpansionPanelModule,
        FinTableModule,
        FinEmptyStateModule,
      ],
    }),
    componentWrapperDecorator(
      (story) =>
        `<div class="story-wrapper-DO-NOT-COPY fin-w-[900px]">${story}</div>`,
    ),
  ],
  parameters: {
    backgrounds: {
      default: 'dark',
      values: [{ name: 'dark', value: '#ececec' }],
    },
    docs: {
      description: {
        component: '`import { FinTableModule } from "@fincloud/ui/table"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6656-8276&t=CHJt5tl1857uAXRI-4',
    },
  },
};
export default meta;
type Story = StoryObj<
  FinTableComponent<unknown> & {
    finRowTemplate: string;
    finRowDetailsTemplate: string;
    finHeaderTemplate: string;
    finTableNoDataTemplate: string;
  }
>;

export const Default: Story = {
  name: 'Double row, borderless',
  args: {
    rows: [
      {
        person: {
          fName: 'John',
          lName: 'Doe',
        },
        capital: 43210000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'AA',
      },
      {
        person: {
          fName: 'Jane',
          lName: 'Smith',
        },
        capital: 56780000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'BB',
      },
      {
        person: {
          fName: 'Bugs',
          lName: 'Bunny',
        },
        capital: 21500000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'CC',
      },
    ],
    columns: [
      {
        name: 'Name',
        prop: 'name',
        templateName: 'name',
        isSortable: true,
        flexGrow: 2,
      },
      {
        name: 'Capital',
        prop: 'capital',
        templateName: 'capital',
        isSortable: true,
      },
      {
        name: 'Info',
        prop: 'info',
        templateName: 'info',
        isSortable: false,
      },
      {
        name: 'Grade',
        prop: 'grade',
        templateName: 'grade',
        isSortable: false,
      },
    ],
    columnMode: FinTableColumnMode.FLEX,
    useDefaultSort: false,
    hasBorderRadius: false,
    hasRowBorder: false,
    hasRowPartialBorder: false,
    hasRowSpacing: false,
    columnsToHide: [],
    initialSort: undefined,
  },
  argTypes: {
    columnMode: {
      options: Object.values(FinTableColumnMode),
      control: { type: 'select' },
    },
    sortChange: {
      control: false,
    },
    finRowTemplate: {
      description: 'Row template.',
      defaultValue: { summary: 'attribute' },
      control: false,
      table: {
        category: 'Templates',
      },
    },
    finHeaderTemplate: {
      description: 'Header template.',
      defaultValue: { summary: 'attribute' },
      control: false,
      table: {
        category: 'Templates',
      },
    },
    finRowDetailsTemplate: {
      description: 'Row details template.',
      defaultValue: { summary: 'attribute' },
      control: false,
      table: {
        category: 'Templates',
      },
    },
    finTableNoDataTemplate: {
      description:
        'A custom template that will be shown when the table is empty.',
      defaultValue: { summary: 'attribute' },
      control: false,
      table: {
        category: 'Templates',
      },
    },
  },
  render: (args) => ({
    props: args,
    template: `
    <fin-table
      [rows]="rows"
      [columns]="columns"
      [columnMode]="columnMode"
      [headerHeight]="44"
      [rowHeight]="64"
      [useDefaultSort]="useDefaultSort"
      [hasBorderRadius]="hasBorderRadius"
      [hasRowBorder]="hasRowBorder"
      [hasRowPartialBorder]="hasRowPartialBorder"
      [hasRowSpacing]="hasRowSpacing"
      [initialSort]="initialSort"
      [autoHideColumns]="autoHideColumns"
    >
      <ng-template name="name" [finRowTemplate]="rows" let-row>
        <div class="fin-flex fin-items-center">
          <fin-avatar-default
            [firstName]="row.person.fName"
            [lastName]="row.person.lName"
            size="s"
          ></fin-avatar-default>

            <div class="fin-ms-[8px]">
            {{ row.person.fName }} {{ row.person.lName }}
            <div class="fin-text-color-text-tertiary fin-text-body-3-strong">
              Description
            </div>
          </div>
        </div>
      </ng-template>

      <ng-template name="capital" [finRowTemplate]="rows" let-row>
        {{ row.capital | currency: 'EUR' }}

        <div class="fin-text-color-text-tertiary fin-text-body-3-strong">
          Description
        </div>
      </ng-template>

      <ng-template name="info" [finRowTemplate]="rows" let-row>
        <div>
          {{ row.info.text }}
        </div>

        <div class="fin-text-color-text-tertiary fin-text-body-3-strong">
          {{ row.info.description }}
        </div>
      </ng-template>

      <ng-template name="grade" [finRowTemplate]="rows" let-row>
        {{ row.grade }}
      </ng-template>
    </fin-table>
    `,
  }),
};

export const TripleRowBorderless: Story = {
  name: 'Triple row, borderless',
  args: {
    rows: [
      {
        type: {
          index: 'Index',
          text: 'Text text',
          type: 'Finanzierung',
        },
        info: {
          text: 'Plain text',
          subtext: 'Subtext',
          description: 'Description',
        },
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
        status: 'Signed',
      },
      {
        type: {
          index: 'Index',
          text: 'Text text',
          type: 'Finanzierung',
        },
        info: {
          text: 'Plain text',
          subtext: 'Subtext',
          description: 'Description',
        },
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
        status: 'Pending',
      },
      {
        type: {
          index: 'Index',
          text: 'Text text',
          type: 'Finanzierung',
        },
        info: {
          text: 'Plain text',
          subtext: 'Subtext',
          description: 'Description',
        },
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
        status: 'Cancelled',
      },
    ],
    columns: [
      { name: 'Type', prop: 'type', templateName: 'type', isSortable: true },
      {
        name: 'Info',
        prop: 'info',
        templateName: 'info',
        isSortable: true,
      },
      {
        name: 'Amounts',
        prop: 'amounts',
        templateName: 'amounts',
        isSortable: true,
      },
      {
        name: 'Status',
        prop: 'status',
        templateName: 'status',
        isSortable: true,
      },
      {
        name: '',
        prop: 'actions',
        templateName: 'actions',
        isSortable: false,
      },
    ],
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: args,
    template: `
    <fin-table
      [rows]="rows"
      [columns]="columns"
      [columnMode]="columnMode"
      [headerHeight]="64"
      [rowHeight]="80"
    >
      <ng-template name="type" [finRowTemplate]="rows" let-row>
        <div class="fin-text-color-text-tertiary fin-font-medium">
          {{ row.type.index }}
        </div>
        <div>
          {{ row.type.text }}
        </div>
        <div class="fin-flex fin-items-center">
          <fin-icon
            src="/assets/storybook/icons/circle.svg"
            size="l"
          ></fin-icon>
          <div class="fin-text-body-3-strong fin-font-medium fin-ms-[4px]">
            {{ row.type.type }}
          </div>
        </div>
      </ng-template>

      <ng-template name="info" [finRowTemplate]="rows" let-row>
        {{ row.info.text }}
        <div class="fin-font-medium">
          {{ row.info.subtext }}
        </div>
        <div class="fin-text-color-text-tertiary fin-font-medium">
          {{ row.info.description }}
        </div>
      </ng-template>

      <ng-template name="amounts" [finRowTemplate]="rows" let-row>
        {{ row.amounts.amount1 | currency: 'EUR' }}
        <div class="fin-text-color-text-tertiary">
          {{ row.amounts.amount2 | currency: 'EUR' }}
        </div>
      </ng-template>

      <ng-template name="status" [finRowTemplate]="rows" let-row>
        <fin-badge-status
          [type]="row.status"
          [text]="row.status"
        ></fin-badge-status>
      </ng-template>

      <ng-template name="actions" [finRowTemplate]="rows" let-row>
        <button fin-button-action>
          <fin-icon name="more_vert"></fin-icon>
        </button>
      </ng-template>
    </fin-table>
    `,
  }),
};

export const DoubleRowRowBorder: Story = {
  name: 'Double row, 1px spacing',
  args: {
    rows: [
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status Badge',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
      },
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status Badge',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
      },
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status Badge',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
      },
    ],
    columns: [
      {
        name: 'Column label',
        prop: 'label1',
        templateName: 'label1',
        isSortable: true,
      },
      {
        name: 'Column label',
        prop: 'label2',
        templateName: 'label2',
        isSortable: true,
      },
      {
        name: 'Column label',
        prop: 'label3',
        templateName: 'label3',
        isSortable: true,
      },
      {
        name: 'Column label',
        prop: 'label4',
        templateName: 'label4',
        isSortable: true,
      },
      {
        name: '',
        prop: 'actions',
        templateName: 'actions',
        isSortable: false,
      },
    ],
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: args,
    template: `
    <fin-table
      [rows]="rows"
      [columns]="columns"
      [columnMode]="columnMode"
      [headerHeight]="44"
      [rowHeight]="64"
      hasRowBorder
    >
      <ng-template name="label1" [finRowTemplate]="rows" let-row>
        {{ row.info.text1 }}

        <div class="fin-text-color-text-tertiary fin-text-body-3-strong">
          {{ row.info.text2 }}
        </div>
      </ng-template>

      <ng-template name="label2" [finRowTemplate]="rows" let-row>
        <div class="fin-flex fin-items-center">
          <button fin-button-icon appearance="stealth">
            <fin-icon name="content_copy"></fin-icon>
          </button>
          <div>
            {{ row.info.text1 }}
            <div class="fin-text-color-text-tertiary fin-text-body-3-strong">
              {{ row.info.text2 }}
            </div>
          </div>
          </div>
      </ng-template>

      <ng-template name="label3" [finRowTemplate]="rows" let-row>
        <fin-badge-status
          type="Signed"
          [text]="row.status"
        ></fin-badge-status>
      </ng-template>

      <ng-template name="label4" [finRowTemplate]="rows" let-row>
        {{ row.amounts.amount1 | currency: 'EUR' }}
        <div class="fin-text-color-text-tertiary">
          {{ row.amounts.amount2 | currency: 'EUR' }}
        </div>
      </ng-template>

      <ng-template name="actions" [finRowTemplate]="rows" let-row>
        <button fin-button-action>
          <fin-icon name="more_vert"></fin-icon>
        </button>
      </ng-template>
    </fin-table>
    `,
  }),
};

export const SingleRowRowBorder: Story = {
  name: 'Single row, 1px spacing',
  args: {
    rows: [
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status Badge',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
      },
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status Badge',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
      },
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status Badge',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
      },
    ],
    columns: [
      {
        name: 'Column label',
        prop: 'label1',
        templateName: 'label1',
        isSortable: true,
      },
      {
        name: 'Column label',
        prop: 'label2',
        templateName: 'label2',
        isSortable: true,
      },
      {
        name: 'Column label',
        prop: 'label3',
        templateName: 'label3',
        isSortable: true,
      },
      {
        name: 'Column label',
        prop: 'label4',
        templateName: 'label4',
        isSortable: true,
      },
      {
        name: '',
        prop: 'actions',
        templateName: 'actions',
        isSortable: false,
      },
    ],
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: args,
    template: `
    <fin-table
      [rows]="rows"
      [columns]="columns"
      [columnMode]="columnMode"
      [headerHeight]="64"
      [rowHeight]="44"
      hasRowBorder
    >
      <ng-template name="label1" [finRowTemplate]="rows" let-row>
        {{ row.info.text1 }}
      </ng-template>

      <ng-template name="label2" [finRowTemplate]="rows" let-row>
        {{ row.info.text2 }}
      </ng-template>

      <ng-template name="label3" [finRowTemplate]="rows" let-row>
        <fin-badge-status
          type="Signed"
          [text]="row.status"
        ></fin-badge-status>
      </ng-template>

      <ng-template name="label4" [finRowTemplate]="rows" let-row>
        {{ row.amounts.amount1 | currency: 'EUR' }}
      </ng-template>

      <ng-template name="actions" [finRowTemplate]="rows" let-row>
        <button fin-button-action>
          <fin-icon name="more_vert"></fin-icon>
        </button>
      </ng-template>
    </fin-table>
    `,
  }),
};

export const PartialBorder: Story = {
  args: {
    rows: [
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status Badge',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
      },
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status Badge',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
      },
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status Badge',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
      },
    ],
    columns: [
      {
        name: 'Column label',
        prop: 'label1',
        templateName: 'label1',
        isSortable: true,
      },
      {
        name: 'Column label',
        prop: 'label2',
        templateName: 'label2',
        isSortable: true,
      },
      {
        name: 'Column label',
        prop: 'label3',
        templateName: 'label3',
        isSortable: true,
      },
      {
        name: 'Column label',
        prop: 'label4',
        templateName: 'label4',
        isSortable: true,
      },
      {
        name: '',
        prop: 'actions',
        templateName: 'actions',
        isSortable: false,
      },
    ],
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: args,
    template: `
    <fin-table
      [rows]="rows"
      [columns]="columns"
      [columnMode]="columnMode"
      [headerHeight]="64"
      [rowHeight]="44"
      hasRowPartialBorder
    >
      <ng-template name="label1" [finRowTemplate]="rows" let-row>
        {{ row.info.text1 }}
      </ng-template>

      <ng-template name="label2" [finRowTemplate]="rows" let-row>
        {{ row.info.text2 }}
      </ng-template>

      <ng-template name="label3" [finRowTemplate]="rows" let-row>
        <fin-badge-status
          type="Signed"
          [text]="row.status"
        ></fin-badge-status>
      </ng-template>

      <ng-template name="label4" [finRowTemplate]="rows" let-row>
        {{ row.amounts.amount1 | currency: 'EUR' }}
      </ng-template>

      <ng-template name="actions" [finRowTemplate]="rows" let-row>
        <button fin-button-action>
          <fin-icon name="more_vert"></fin-icon>
        </button>
      </ng-template>
    </fin-table>
    `,
  }),
};

export const SingleRowSpacing: Story = {
  name: 'Single row, default spacing',
  args: {
    rows: [
      {
        info: 'Text with badge',
        description: 'Description',
        status: 'Status Badge',
        amount: 123456789,
      },
      {
        info: 'Text with badge',
        description: 'Description',
        status: 'Status Badge',
        amount: 123456789,
      },
      {
        info: 'Text with badge',
        description: 'Description',
        status: 'Status Badge',
        amount: 123456789,
      },
    ],
    columns: [
      {
        name: 'Column label',
        prop: 'label1',
        templateName: 'label1',
        isSortable: true,
        flexGrow: 2,
      },
      {
        name: 'Column label',
        prop: 'label2',
        templateName: 'label2',
        isSortable: false,
      },
      {
        name: 'Column label',
        prop: 'label3',
        templateName: 'label3',
        isSortable: false,
      },
      {
        name: 'Column label',
        prop: 'label4',
        templateName: 'label4',
        isSortable: false,
      },
      {
        name: '',
        prop: 'actions',
        templateName: 'actions',
        isSortable: false,
      },
    ],
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: args,
    template: `
    <fin-table
      [rows]="rows"
      [columns]="columns"
      [columnMode]="columnMode"
      [headerHeight]="44"
      [rowHeight]="44"
      hasRowSpacing
      hasBorderRadius
    >
      <ng-template name="label1" [finRowTemplate]="rows" let-row>
        <div class="fin-flex fin-items-center">
          <fin-badge-invitation type="regular"></fin-badge-invitation>
          <span class="fin-ms-[0.6rem]">{{ row.info }}</span>
        </div>
      </ng-template>

      <ng-template name="label2" [finRowTemplate]="rows" let-row>
        <span class="fin-text-color-text-tertiary">
          {{ row.description }}
        </span>
      </ng-template>

      <ng-template name="label3" [finRowTemplate]="rows" let-row>
        <fin-badge-status
          type="Signed"
          [text]="row.status"
        ></fin-badge-status>
      </ng-template>

      <ng-template name="label4" [finRowTemplate]="rows" let-row>
        <span class="fin-font-medium">{{ row.amount | currency: 'EUR' }}</span>
      </ng-template>

      <ng-template name="actions" [finRowTemplate]="rows" let-row>
        <div class="fin-flex fin-justify-end">
          <button fin-button-action appearance="informative" class="fin-me-[0.6rem]">
            <fin-icon name="add"></fin-icon>
          </button>

          <button fin-button-action appearance="informative">
            <fin-icon name="close"></fin-icon>
          </button>
        </div>
      </ng-template>
    </fin-table>
    `,
  }),
};

export const DoubleRowSpacing: Story = {
  name: 'Double row, default spacing',
  args: {
    rows: [
      {
        person: {
          fName: 'John',
          lName: 'Doe',
        },
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
        description: {
          text: 'Plain text',
          subtext: 'Subtext',
        },
      },
      {
        person: {
          fName: 'Jane',
          lName: 'Smith',
        },
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
        description: {
          text: 'Plain text',
          subtext: 'Subtext',
        },
      },
      {
        person: {
          fName: 'Bugs',
          lName: 'Bunny',
        },
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
        description: {
          text: 'Plain text',
          subtext: 'Subtext',
        },
      },
    ],
    columns: [
      {
        name: 'Column label',
        prop: 'label1',
        templateName: 'label1',
        isSortable: false,
      },
      {
        name: 'Column label',
        prop: 'label2',
        templateName: 'label2',
        isSortable: false,
      },
      {
        name: 'Column label',
        prop: 'label3',
        templateName: 'label3',
        isSortable: false,
      },
      {
        name: 'Column label',
        prop: 'label4',
        templateName: 'label4',
        isSortable: false,
      },
      {
        name: '',
        prop: 'actions',
        templateName: 'actions',
        isSortable: false,
      },
    ],
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: args,
    template: `
    <fin-table
      [rows]="rows"
      [columns]="columns"
      [columnMode]="columnMode"
      [headerHeight]="64"
      [rowHeight]="64"
      hasRowSpacing
      hasBorderRadius
    >
      <ng-template name="label1" [finRowTemplate]="rows" let-row>
        <div class="fin-flex fin-items-center">
          <fin-avatar-default
            [firstName]="row.person.fName"
            [lastName]="row.person.lName"
            size="s"
          ></fin-avatar-default>

            <div class="fin-ms-[8px]">
            {{ row.person.fName }} {{ row.person.lName }}
            <div class="fin-text-color-text-tertiary fin-text-body-3-strong">
              Description
            </div>
          </div>
        </div>
      </ng-template>

      <ng-template name="label2" [finRowTemplate]="rows" let-row>
        <div class="fin-flex fin-items-center">
          <button fin-button-icon appearance="stealth">
            <fin-icon name="content_copy"></fin-icon>
          </button>
          <div>
            {{ row.info.text1 }}
            <div class="fin-text-color-text-tertiary fin-text-body-3-strong">
              {{ row.info.text2 }}
            </div>
          </div>
          </div>
      </ng-template>

      <ng-template name="label3" [finRowTemplate]="rows" let-row>
        {{ row.amounts.amount1 | currency: 'EUR' }}
        <div class="fin-text-color-text-tertiary">
          {{ row.amounts.amount2 | currency: 'EUR' }}
        </div>
      </ng-template>

      <ng-template name="label4" finRowTemplate="rows" let-row>
        {{ row.description.text }}
        <div class="fin-font-medium">
          {{ row.description.subtext }}
        </div>
      </ng-template>

      <ng-template name="actions" [finRowTemplate]="rows" let-row>
        <div class="fin-flex fin-justify-end">
          <button fin-button size="m" appearance="stealth">
            Manage
            <fin-icon name="keyboard_arrow_right"></fin-icon>
          </button>
        </div>
      </ng-template>
    </fin-table>
    `,
  }),
};

export const Expandable: Story = {
  args: {
    rows: [
      {
        person: {
          fName: 'John',
          lName: 'Doe',
        },
        description: {
          text1: 'Text',
          text2: 'Description',
        },
        status: 'Status Badge',
        info: 'Plain text',
      },
      {
        person: {
          fName: 'Jane',
          lName: 'Smith',
        },
        description: {
          text1: 'Text',
          text2: 'Description',
        },
        status: 'Status Badge',
        info: 'Plain text',
      },
      {
        person: {
          fName: 'Bugs',
          lName: 'Bunny',
        },
        description: {
          text1: 'Text',
          text2: 'Description',
        },
        status: 'Status Badge',
        info: 'Plain text',
      },
    ],
    columns: [
      {
        name: 'Column label',
        prop: 'label1',
        templateName: 'label1',
        isSortable: true,
        flexGrow: 2,
      },
      {
        name: 'Column label',
        prop: 'label2',
        templateName: 'label2',
        isSortable: true,
      },
      {
        name: 'Column label',
        prop: 'label3',
        templateName: 'label3',
        isSortable: true,
      },
      {
        name: 'Column label',
        prop: 'label4',
        templateName: 'label4',
        isSortable: true,
      },
    ],
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: {
      ...args,
    },
    template: `
    <fin-accordion togglePosition="before" type="light">
      <fin-expansion-panel>
        <ng-template #finTitle>
          OJP-456
        </ng-template>

        <ng-template #finDescription>
            {{ ********* | currency: 'EUR'}} | Volksbank Tirol AG |
          <span class="fin-text-color-text-interactive fin-font-bold fin-ms-[0.4em]">
            7 tasks
          </span>
        </ng-template>

        <ng-template #finContent>
          <div class="fin-bg-color-surface-secondary">
            <fin-table
              [rows]="rows"
              [columns]="columns"
              [columnMode]="columnMode"
              [headerHeight]="40"
              [rowHeight]="40"
              hasRowSpacing
            >
              <ng-template name="label1" [finRowTemplate]="rows" let-row>
                <div class="fin-flex">
                  <fin-avatar-default
                    [firstName]="row.person.fName"
                    [lastName]="row.person.lName"
                    size="s"
                  ></fin-avatar-default>

                  <div class="fin-ms-[8px]">
                    {{ row.person.fName }} {{ row.person.lName }}
                  </div>
                </div>
              </ng-template>

              <ng-template name="label2" [finRowTemplate]="rows" let-row>
                {{ row.description.text1 }}
                <span class="fin-text-color-text-tertiary">
                  | {{ row.description.text2 }}
                </span>
              </ng-template>

              <ng-template name="label3" [finRowTemplate]="rows" let-row>
                <fin-badge-status
                  type="Signed"
                  [text]="row.status"
                ></fin-badge-status>
              </ng-template>

              <ng-template name="label4" [finRowTemplate]="rows" let-row>
                <span class="fin-font-medium">
                {{ row.description.text2 }}
                </span>
              </ng-template>
            </fin-table>
          </div>
        </ng-template>
      </fin-expansion-panel>
    </fin-accordion>
    `,
  }),
};

export const CustomHeaderBackground: Story = {
  args: {
    rows: [
      {
        person: {
          fName: 'John',
          lName: 'Doe',
        },
        description: {
          text1: 'Text',
          text2: 'Description',
        },
        status: 'Status Badge',
        info: 'Plain text',
      },
      {
        person: {
          fName: 'Jane',
          lName: 'Smith',
        },
        description: {
          text1: 'Text',
          text2: 'Description',
        },
        status: 'Status Badge',
        info: 'Plain text',
      },
      {
        person: {
          fName: 'Bugs',
          lName: 'Bunny',
        },
        description: {
          text1: 'Text',
          text2: 'Description',
        },
        status: 'Status Badge',
        info: 'Plain text',
      },
    ],
    columns: [
      {
        name: 'Column label',
        prop: 'label1',
        templateName: 'label1',
        isSortable: true,
        flexGrow: 2,
      },
      {
        name: 'Column label',
        prop: 'label2',
        templateName: 'label2',
        isSortable: true,
      },
      {
        name: 'Column label',
        prop: 'label3',
        templateName: 'label3',
        isSortable: true,
      },
      {
        name: 'Column label',
        prop: 'label4',
        templateName: 'label4',
        isSortable: true,
      },
    ],
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: args,
    template: `
      <div class="fin-bg-color-background-neutral-minimal fin-p-[1rem]">
        <fin-table
          [rows]="rows"
          [columns]="columns"
          [columnMode]="columnMode"
          [headerHeight]="44"
          [rowHeight]="44"
          hasRowSpacing
          hasBorderRadius
          headerClasses="!fin-bg-color-background-neutral-minimal"
        >
          <ng-template name="label1" [finRowTemplate]="rows" let-row>
            <div class="fin-flex">
              <fin-avatar-default
                [firstName]="row.person.fName"
                [lastName]="row.person.lName"
                size="s"
              ></fin-avatar-default>

              <div class="fin-ms-[8px]">
                {{ row.person.fName }} {{ row.person.lName }}
              </div>
            </div>
          </ng-template>

          <ng-template name="label2" [finRowTemplate]="rows" let-row>
            {{ row.description.text1 }}
            <span class="fin-text-color-text-tertiary">
              | {{ row.description.text2 }}
            </span>
          </ng-template>

          <ng-template name="label3" [finRowTemplate]="rows" let-row>
            <fin-badge-status
              type="Signed"
              [text]="row.status"
            ></fin-badge-status>
          </ng-template>

          <ng-template name="label4" [finRowTemplate]="rows" let-row>
            <span class="fin-font-medium">
              {{ row.description.text2 }}
            </span>
          </ng-template>
        </fin-table>
      </div>
    `,
  }),
};

export const EmptyMessage: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: args,
    template: `
      <fin-table [rows]="[]" [columns]="columns" emptyMessage="Text when there is no data">
      </fin-table>
    `,
  }),
};

export const CustomNoDataTemplate: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: args,
    template: `
      <fin-table [rows]="[]" [columns]="columns" hasRowBorder>
        <ng-template #finTableNoDataTemplate>
          <fin-empty-state type="complex" title="No content">
            <ng-template #finIcon>
              <img
                src="/assets/storybook/icons/frame.svg"
                alt="image"
                class="fin-w-[8.8rem]"
              />
            </ng-template>

            <ng-template #finTextContent>
              You can add content from the editor panel on the left. Hover on group and
              click + to add subgroups to it, hover on subgroup and click + to add fields
              to it.
            </ng-template>
          </fin-empty-state>
        </ng-template>
      </fin-table>
    `,
  }),
};

export const ExpandableRow: Story = {
  args: {
    rows: [
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status Badge',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
        fullDescription:
          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Dolore libero nulla quas dignissimos, quia rerum repellendus omnis amet quo voluptatibus, culpa odit esse, laudantium incidunt fuga fugiat asperiores blanditiis repudiandae.',
      },
      {
        info: {
          text1: 'Expandable row',
          text2: 'Description',
        },
        status: 'Status Badge',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
        fullDescription:
          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Dolore libero nulla quas dignissimos, quia rerum repellendus omnis amet quo voluptatibus, culpa odit esse, laudantium incidunt fuga fugiat asperiores blanditiis repudiandae.',
      },
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status Badge',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
        fullDescription:
          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Dolore libero nulla quas dignissimos, quia rerum repellendus omnis amet quo voluptatibus, culpa odit esse, laudantium incidunt fuga fugiat asperiores blanditiis repudiandae.',
      },
    ],
    columns: [
      {
        name: 'Column label',
        prop: 'label1',
        templateName: 'label1',
        isSortable: false,
      },
      {
        name: 'Column label',
        prop: 'label2',
        templateName: 'label2',
        isSortable: false,
      },
      {
        name: 'Column label',
        prop: 'label3',
        templateName: 'label3',
        isSortable: false,
      },
      {
        name: 'Column label',
        prop: 'label4',
        templateName: 'label4',
        isSortable: false,
      },
      {
        name: '',
        prop: 'actions',
        templateName: 'actions',
        isSortable: false,
      },
    ],
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: {
      ...args,
    },
    template: `
      <fin-table
        [rows]="rows"
        [columns]="columns"
        [columnMode]="columnMode"
        [rowHeight]="44"
        hasBorderRadius
        hasRowSpacing
      >
        <ng-template name="label1" [finRowTemplate]="rows" let-row>
          {{ row.info.text1 }}
        </ng-template>

        <ng-template name="label2" [finRowTemplate]="rows" let-row>
          {{ row.info.text2 }}
        </ng-template>

        <ng-template name="label3" [finRowTemplate]="rows" let-row>
          <fin-badge-status
            [type]="'Signed'"
            [text]="row.status"
          ></fin-badge-status>
        </ng-template>

        <ng-template name="label4" [finRowTemplate]="rows" let-row>
          {{ row.amounts.amount1 | currency: 'EUR' }}
        </ng-template>

        <ng-template [finRowDetailsTemplate]="rows" let-row>
          <!-- Row details content BEGIN -->
          {{ row.fullDescription }}
          <!-- Row details content END -->
        </ng-template>
      </fin-table>
    `,
  }),
};

export const ExpandableRowNestedTable: Story = {
  args: {
    rows: [
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status Badge',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
        rows: [
          {
            info: {
              text1: 'Inner table',
              text2: 'Expanded row',
            },
            status: 'Status Badge',
            amounts: {
              amount1: 123456,
              amount2: 1234567890,
            },
          },
          {
            info: {
              text1: 'Inner table',
              text2: 'Expanded row',
            },
            status: 'Status Badge',
            amounts: {
              amount1: 123456,
              amount2: 1234567890,
            },
          },
          {
            info: {
              text1: 'Inner table',
              text2: 'Expanded row',
            },
            status: 'Status Badge',
            amounts: {
              amount1: 123456,
              amount2: 1234567890,
            },
          },
        ],
      },
      {
        info: {
          text1: 'Expandable row',
          text2: 'Description',
        },
        status: 'Status Badge',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
        rows: [
          {
            info: {
              text1: 'Inner table',
              text2: 'Expanded row',
            },
            status: 'Status Badge',
            amounts: {
              amount1: 123456,
              amount2: 1234567890,
            },
          },
          {
            info: {
              text1: 'Inner table',
              text2: 'Expanded row',
            },
            status: 'Status Badge',
            amounts: {
              amount1: 123456,
              amount2: 1234567890,
            },
          },
          {
            info: {
              text1: 'Inner table',
              text2: 'Expanded row',
            },
            status: 'Status Badge',
            amounts: {
              amount1: 123456,
              amount2: 1234567890,
            },
          },
        ],
      },
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status Badge',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
        rows: [],
      },
    ],
    columns: [
      {
        name: 'Column label',
        prop: 'label1',
        templateName: 'label1',
        isSortable: false,
      },
      {
        name: 'Column label',
        prop: 'label2',
        templateName: 'label2',
        isSortable: false,
      },
      {
        name: 'Column label',
        prop: 'label3',
        templateName: 'label3',
        isSortable: false,
      },
      {
        name: 'Column label',
        prop: 'label4',
        templateName: 'label4',
        isSortable: false,
      },
      {
        name: '',
        prop: 'actions',
        templateName: 'actions',
        isSortable: false,
      },
    ],
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: {
      ...args,
      innerTableColumns: [
        {
          name: 'Column Name',
          prop: 'label1',
          templateName: 'label1',
          isSortable: false,
        },
        {
          name: '',
          prop: 'label2',
          templateName: 'label2',
          isSortable: false,
        },
        {
          name: '',
          prop: 'label3',
          templateName: 'label3',
          isSortable: false,
        },
        {
          name: '',
          prop: 'label4',
          templateName: 'label4',
          isSortable: false,
        },
        {
          name: '',
          prop: 'actions',
          templateName: 'actions',
          isSortable: false,
        },
      ],
      canExpand: (row: any) => row?.rows?.length > 0,
    },
    template: `
      <fin-table
        [rows]="rows"
        [columns]="columns"
        [columnMode]="columnMode"
        [rowHeight]="44"
        hasBorderRadius
        hasRowSpacing
        [canExpand]="canExpand"
      >
        <ng-template name="label1" [finRowTemplate]="rows" let-row>
          {{ row.info.text1 }}
        </ng-template>

        <ng-template name="label2" [finRowTemplate]="rows" let-row>
          {{ row.info.text2 }}
        </ng-template>

        <ng-template name="label3" [finRowTemplate]="rows" let-row>
          <fin-badge-status
            [type]="'Signed'"
            [text]="row.status"
          ></fin-badge-status>
        </ng-template>

        <ng-template name="label4" [finRowTemplate]="rows" let-row>
          {{ row.amounts.amount1 | currency: 'EUR' }}
        </ng-template>

        <ng-template name="actions" [finRowTemplate]="rows" let-row>
          <button fin-button-action>
            <fin-icon name="more_vert"></fin-icon>
          </button>
        </ng-template>

        <ng-template [finRowDetailsTemplate]="rows" let-row>
          <!-- Row details content BEGIN -->
            <fin-table
                [rows]="row.rows"
                [columns]="innerTableColumns"
                [columnMode]="columnMode"
                [headerHeight]="40"
                [rowHeight]="44"
              hasRowBorder
            >
              <ng-template name="label1" [finRowTemplate]="rows" let-row>
                {{ row.info.text1 }}
              </ng-template>

              <ng-template name="label2" [finRowTemplate]="rows" let-row>
                {{ row.info.text2 }}
              </ng-template>

              <ng-template name="label3" [finRowTemplate]="rows" let-row>
                <fin-badge-status
                  [type]="'Signed'"
                  [text]="row.status"
                ></fin-badge-status>
              </ng-template>

              <ng-template name="label4" [finRowTemplate]="rows" let-row>
                {{ row.amounts.amount1 | currency: 'EUR' }}
              </ng-template>
            </fin-table>
          <!-- Row details content END -->
        </ng-template>
      </fin-table>
    `,
  }),
};

export const GroupedRows: Story = {
  args: {
    rows: [
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status 1',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
      },
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status 1',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
      },
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status 1',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
      },
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status 2',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
      },
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status 2',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
      },
    ],
    columns: [
      {
        name: 'Column label',
        prop: 'label1',
        templateName: 'label1',
        isSortable: false,
      },
      {
        name: 'Column label',
        prop: 'label2',
        templateName: 'label2',
        isSortable: false,
      },
      {
        name: 'Column label',
        prop: 'label3',
        templateName: 'label3',
        isSortable: false,
      },
      {
        name: 'Column label',
        prop: 'label4',
        templateName: 'label4',
        isSortable: false,
      },
      {
        name: '',
        prop: 'actions',
        templateName: 'actions',
        isSortable: false,
      },
    ],
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: {
      ...args,
    },
    template: `
      <fin-table
        [rows]="rows"
        [columns]="columns"
        [columnMode]="columnMode"
        [rowHeight]="44"
        hasRowBorder
        groupRowsBy="status"
      >
        <ng-template [finRowGroupTemplate]="rows" let-group>
          Grouped by {{ group.key }}
        </ng-template>

        <ng-template name="label1" [finRowTemplate]="rows" let-row>
          {{ row.info.text1 }}
        </ng-template>

        <ng-template name="label2" [finRowTemplate]="rows" let-row>
          {{ row.info.text2 }}
        </ng-template>

        <ng-template name="label3" [finRowTemplate]="rows" let-row>
          <fin-badge-status
            [type]="'Signed'"
            [text]="row.status"
          ></fin-badge-status>
        </ng-template>

        <ng-template name="label4" [finRowTemplate]="rows" let-row>
          {{ row.amounts.amount1 | currency: 'EUR' }}
        </ng-template>

        <ng-template name="actions" [finRowTemplate]="rows" let-row>
          <button fin-button-action>
            <fin-icon name="more_vert"></fin-icon>
          </button>
        </ng-template>
      </fin-table>
    `,
  }),
};

export const GroupedAndExpandedRows: Story = {
  name: 'Grouped with multiple expandable rows',
  args: {
    rows: [
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status 1',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
        description: 'Expanded info',
      },
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status 1',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
      },
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status 1',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
        description: 'Expanded info',
      },
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status 2',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
        description: 'Expanded info',
      },
      {
        info: {
          text1: 'Text text',
          text2: 'Description',
        },
        status: 'Status 2',
        amounts: {
          amount1: 123456,
          amount2: 1234567890,
        },
        description: 'Expanded info',
      },
    ],
    columns: [
      {
        name: 'Column label',
        prop: 'label1',
        templateName: 'label1',
        isSortable: false,
      },
      {
        name: 'Column label',
        prop: 'label2',
        templateName: 'label2',
        isSortable: false,
      },
      {
        name: 'Column label',
        prop: 'label3',
        templateName: 'label3',
        isSortable: false,
      },
      {
        name: 'Column label',
        prop: 'label4',
        templateName: 'label4',
        isSortable: false,
      },
      {
        name: '',
        prop: 'actions',
        templateName: 'actions',
        isSortable: false,
      },
    ],
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: {
      ...args,
      canExpand: (row: any) => !!row?.description,
    },
    template: `
      <fin-table
        [rows]="rows"
        [columns]="columns"
        [columnMode]="columnMode"
        [rowHeight]="44"
        hasRowBorder
        groupRowsBy="status"
        multiExpandDetails
        [canExpand]="canExpand"
      >
        <ng-template [finRowGroupTemplate]="rows" let-group>
          Grouped by {{ group.key }}
        </ng-template>

        <ng-template name="label1" [finRowTemplate]="rows" let-row>
          {{ row.info.text1 }}
        </ng-template>

        <ng-template name="label2" [finRowTemplate]="rows" let-row>
          {{ row.info.text2 }}
        </ng-template>

        <ng-template name="label3" [finRowTemplate]="rows" let-row>
          <fin-badge-status
            [type]="'Signed'"
            [text]="row.status"
          ></fin-badge-status>
        </ng-template>

        <ng-template name="label4" [finRowTemplate]="rows" let-row>
          {{ row.amounts.amount1 | currency: 'EUR' }}
        </ng-template>

        <ng-template name="actions" [finRowTemplate]="rows" let-row>
          <button fin-button-action>
            <fin-icon name="more_vert"></fin-icon>
          </button>
        </ng-template>

        <ng-template [finRowDetailsTemplate]="rows" let-row>
          {{ row.description }}
        </ng-template>
      </fin-table>
    `,
  }),
};

export const CustomHeader: Story = {
  name: 'Table with custom templates in the header',
  args: {
    rows: [
      {
        person: {
          fName: 'John',
          lName: 'Doe',
        },
        capital: 43210000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'AA',
      },
      {
        person: {
          fName: 'Jane',
          lName: 'Smith',
        },
        capital: 56780000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'BB',
      },
      {
        person: {
          fName: 'Bugs',
          lName: 'Bunny',
        },
        capital: 21500000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'CC',
      },
    ],
    columns: [
      {
        name: 'Name',
        prop: 'name',
        templateName: 'name',
        isSortable: true,
        flexGrow: 2,
      },
      {
        name: 'Capital',
        headerTemplate: 'capital',
        prop: 'capital',
        templateName: 'capital',
        isSortable: true,
      },
      {
        name: 'Info',
        prop: 'info',
        templateName: 'info',
        isSortable: false,
      },
      {
        name: 'Grade',
        prop: 'grade',
        templateName: 'grade',
        isSortable: false,
      },
    ],
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: args,
    template: `
    <fin-table
      [rows]="rows"
      [columns]="columns"
      [columnMode]="columnMode"
      [headerHeight]="44"
      [rowHeight]="64"
      [useDefaultSort]="useDefaultSort"
      [hasBorderRadius]="hasBorderRadius"
      [hasRowBorder]="hasRowBorder"
      [hasRowPartialBorder]="hasRowPartialBorder"
      [hasRowSpacing]="hasRowSpacing"
      [initialSort]="initialSort"
    >
      <ng-container headerTemplates>
        <ng-template name="capital" [finHeaderTemplate]="columns" let-column>
          <div class="fin-flex fin-items-start">
            {{ column.name }}
            <fin-icon title="some title" class="fin-ms-[0.2rem] fin-me-[0.4rem]" [name]="'home'" [size]="'m'"></fin-icon>
          </div>
        </ng-template>
      </ng-container>

      <ng-template name="name" [finRowTemplate]="rows" let-row>
        <div class="fin-flex fin-items-center">
          <fin-avatar-default
            [firstName]="row.person.fName"
            [lastName]="row.person.lName"
            size="s"
          ></fin-avatar-default>

            <div class="fin-ms-[8px]">
            {{ row.person.fName }} {{ row.person.lName }}
            <div class="fin-text-color-text-tertiary fin-text-body-3-strong">
              Description
            </div>
          </div>
        </div>
      </ng-template>

      <ng-template name="capital" [finRowTemplate]="rows" let-row>
        {{ row.capital | currency: 'EUR' }}

        <div class="fin-text-color-text-tertiary fin-text-body-3-strong">
          Description
        </div>
      </ng-template>

      <ng-template name="info" [finRowTemplate]="rows" let-row>
        <div>
          {{ row.info.text }}
        </div>

        <div class="fin-text-color-text-tertiary fin-text-body-3-strong">
          {{ row.info.description }}
        </div>
      </ng-template>

      <ng-template name="grade" [finRowTemplate]="rows" let-row>
        {{ row.grade }}
      </ng-template>
    </fin-table>
    `,
  }),
};

export const VerticalScroll: Story = {
  args: {
    rows: [
      {
        person: {
          fName: 'John',
          lName: 'Doe',
        },
        capital: 43210000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'AA',
      },
      {
        person: {
          fName: 'Jane',
          lName: 'Smith',
        },
        capital: 56780000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'BB',
      },
      {
        person: {
          fName: 'Bugs',
          lName: 'Bunny',
        },
        capital: 21500000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'CC',
      },
      {
        person: {
          fName: 'Lena',
          lName: 'Carter',
        },
        capital: 21500000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'CC',
      },
      {
        person: {
          fName: 'Jaxon',
          lName: 'Reeves',
        },
        capital: 21500000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'CC',
      },
      {
        person: {
          fName: 'Milo',
          lName: 'Fernandez',
        },
        capital: 21500000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'CC',
      },
    ],
    columns: [
      {
        name: 'Name',
        prop: 'name',
        templateName: 'name',
        isSortable: true,
        flexGrow: 2,
      },
      {
        name: 'Capital',
        prop: 'capital',
        templateName: 'capital',
        isSortable: true,
      },
      {
        name: 'Info',
        prop: 'info',
        templateName: 'info',
        isSortable: false,
      },
      {
        name: 'Grade',
        prop: 'grade',
        templateName: 'grade',
        isSortable: false,
      },
    ],
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: {
      ...args,
    },
    template: `
    <div class="fin-max-h-[20rem] fin-overflow-auto">
      <fin-table
        [rows]="rows"
        [columns]="columns"
        hasRowBorder
      >
        <ng-template name="name" [finRowTemplate]="rows" let-row>
          <div class="fin-flex fin-items-center">
            <fin-avatar-default
              [firstName]="row.person.fName"
              [lastName]="row.person.lName"
              size="s"
            ></fin-avatar-default>

              <div class="fin-ms-[8px]">
              {{ row.person.fName }} {{ row.person.lName }}
              <div class="fin-text-color-text-tertiary fin-text-[12px]">
                Description
              </div>
            </div>
          </div>
        </ng-template>

        <ng-template name="capital" [finRowTemplate]="rows" let-row>
          {{ row.capital | currency: 'EUR' }}

          <div class="fin-text-color-text-tertiary fin-text-[12px]">
            Description
          </div>
        </ng-template>

        <ng-template name="info" [finRowTemplate]="rows" let-row>
          <div>
            {{ row.info.text }}
          </div>

          <div class="fin-text-color-text-tertiary fin-text-[12px]">
            {{ row.info.description }}
          </div>
        </ng-template>

        <ng-template name="grade" [finRowTemplate]="rows" let-row>
          {{ row.grade }}
        </ng-template>
      </fin-table>
    </div>
    `,
  }),
};

export const HorizontalScroll: Story = {
  args: {
    rows: [
      {
        person: {
          fName: 'John',
          lName: 'Doe',
        },
        capital: 43210000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'AA',
      },
      {
        person: {
          fName: 'Jane',
          lName: 'Smith',
        },
        capital: 56780000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'BB',
      },
      {
        person: {
          fName: 'Bugs',
          lName: 'Bunny',
        },
        capital: 21500000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'CC',
      },
      {
        person: {
          fName: 'Lena',
          lName: 'Carter',
        },
        capital: 21500000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'CC',
      },
      {
        person: {
          fName: 'Jaxon',
          lName: 'Reeves',
        },
        capital: 21500000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'CC',
      },
      {
        person: {
          fName: 'Milo',
          lName: 'Fernandez',
        },
        capital: 21500000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'CC',
      },
    ],
    columns: [
      {
        name: 'Name',
        prop: 'name',
        templateName: 'name',
        isSortable: true,
        flexGrow: 2,
      },
      {
        name: 'Capital',
        prop: 'capital',
        templateName: 'capital',
        isSortable: true,
      },
      {
        name: 'Info',
        prop: 'info',
        templateName: 'info',
        isSortable: false,
      },
      {
        name: 'Grade',
        prop: 'grade',
        templateName: 'grade',
        isSortable: false,
      },
    ],
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: {
      ...args,
    },
    template: `
    <div class="fin-max-w-[50rem] fin-overflow-auto">
      <fin-table
        class="fin-min-w-[80rem]"
        [rows]="rows"
        [columns]="columns"
        hasRowBorder
      >
        <ng-template name="name" [finRowTemplate]="rows" let-row>
          <div class="fin-flex fin-items-center">
            <fin-avatar-default
              [firstName]="row.person.fName"
              [lastName]="row.person.lName"
              size="s"
            ></fin-avatar-default>

              <div class="fin-ms-[8px]">
              {{ row.person.fName }} {{ row.person.lName }}
              <div class="fin-text-color-text-tertiary fin-text-[12px]">
                Description
              </div>
            </div>
          </div>
        </ng-template>

        <ng-template name="capital" [finRowTemplate]="rows" let-row>
          {{ row.capital | currency: 'EUR' }}

          <div class="fin-text-color-text-tertiary fin-text-[12px]">
            Description
          </div>
        </ng-template>

        <ng-template name="info" [finRowTemplate]="rows" let-row>
          <div>
            {{ row.info.text }}
          </div>

          <div class="fin-text-color-text-tertiary fin-text-[12px]">
            {{ row.info.description }}
          </div>
        </ng-template>

        <ng-template name="grade" [finRowTemplate]="rows" let-row>
          {{ row.grade }}
        </ng-template>
      </fin-table>
    </div>
    `,
  }),
};


export const AutoHideColumns: Story = {
  args: {
    rows: [
      {
        person: {
          fName: 'John',
          lName: 'Doe',
        },
        capital: 43210000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'AA',
      },
      {
        person: {
          fName: 'Jane',
          lName: 'Smith',
        },
        capital: 56780000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'BB',
      },
      {
        person: {
          fName: 'Bugs',
          lName: 'Bunny',
        },
        capital: 21500000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'CC',
      },
      {
        person: {
          fName: 'Lena',
          lName: 'Carter',
        },
        capital: 21500000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'CC',
      },
      {
        person: {
          fName: 'Jaxon',
          lName: 'Reeves',
        },
        capital: 21500000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'CC',
      },
      {
        person: {
          fName: 'Milo',
          lName: 'Fernandez',
        },
        capital: 21500000,
        info: {
          text: 'Some info text',
          description: 'Description',
        },
        grade: 'CC',
      },
    ],
    columns: [
      {
        name: 'Name',
        prop: 'name',
        templateName: 'name',
        isSortable: true,
      },
      {
        name: 'Capital',
        prop: 'capital',
        templateName: 'capital',
        isSortable: true,
        hideThreshold: 400,
      },
      {
        name: 'Info',
        prop: 'info',
        templateName: 'info',
        isSortable: false,
        hideThreshold: 500,
      },
      {
        name: 'Grade',
        prop: 'grade',
        templateName: 'grade',
        isSortable: false,
        hideThreshold: 600,
      },
    ],
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: {
      ...args,
    },
    template: `
    <div class="fin-max-w-[80vw] fin-mx-auto">
      <p class="fin-mb-4 fin-text-text-body-2-size">Resize the page to see columns automatically getting hidden:</p>
      <fin-table
        [rows]="rows"
        [columns]="columns"
        hasRowBorder
        autoHideColumns
      >
        <ng-template name="name" [finRowTemplate]="rows" let-row>
          <div class="fin-flex fin-items-center">
            <fin-avatar-default
              [firstName]="row.person.fName"
              [lastName]="row.person.lName"
              size="s"
            ></fin-avatar-default>

              <div class="fin-ms-[8px]">
              {{ row.person.fName }} {{ row.person.lName }}
              <div class="fin-text-color-text-tertiary fin-text-[12px]">
                Description
              </div>
            </div>
          </div>
        </ng-template>

        <ng-template name="capital" [finRowTemplate]="rows" let-row>
          {{ row.capital | currency: 'EUR' }}

          <div class="fin-text-color-text-tertiary fin-text-[12px]">
            Description
          </div>
        </ng-template>

        <ng-template name="info" [finRowTemplate]="rows" let-row>
          <div>
            {{ row.info.text }}
          </div>

          <div class="fin-text-color-text-tertiary fin-text-[12px]">
            {{ row.info.description }}
          </div>
        </ng-template>

        <ng-template name="grade" [finRowTemplate]="rows" let-row>
          {{ row.grade }}
        </ng-template>
      </fin-table>
    </div>
    `,
  }),
};
