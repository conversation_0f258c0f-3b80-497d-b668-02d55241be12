<mat-table
  class="fin-datatable fin-column-mode-{{ columnMode }}"
  [ngClass]="{
    'fin-border-radius': hasBorderRadius,
    'fin-row-border': hasRowBorder,
    'fin-hide-header': hideHeader,
    'fin-row-partial-border': hasRowPartialBorder,
    'fin-row-spacing': hasRowSpacing,
    'fin-h-full': rows.length,
  }"
  [dataSource]="dataSource"
  multiTemplateDataRows
  (finObserveResize)="onTableResized($event)"
  [finObserveResizeDisabled]="!autoHideColumns"
>
  @for (column of columns; track column.prop) {
    <ng-container [matColumnDef]="column.prop">
      <mat-header-cell
        *matHeaderCellDef
        (click)="onSortChange(column)"
        (mousedown)="onHeaderCellMouseDown()"
        [class.fin-cursor-pointer]="column.isSortable"
        [style.flex-grow]="column.flexGrow || 1"
        [style.width]="column.width || 100 | finPxToRem"
      >
        <div
          class="fin-flex fin-items-center fin-gap-[0.4rem] fin-w-full fin-min-w-0"
        >
          @if (column?.headerTemplate) {
            <div class="fin-select-text fin-min-w-0">
              <ng-container
                [ngTemplateOutlet]="
                  headerTemplates[column?.headerTemplate ?? '']
                "
                [ngTemplateOutletContext]="{ $implicit: column }"
              ></ng-container>
            </div>
          } @else {
            <span class="fin-select-text">{{ column.name }}</span>
          }

          @if (column.isSortable) {
            <span class="fin-select-none">
              @if (sort && column.prop === sort.prop) {
                @if (sort.dir === 'asc') {
                  <fin-icon
                    src="/assets/arrows/arrow-up.svg"
                    [size]="sizes.S"
                  ></fin-icon>
                } @else {
                  <fin-icon
                    src="/assets/arrows/arrow-down.svg"
                    [size]="sizes.S"
                  ></fin-icon>
                }
              } @else {
                <fin-icon
                  src="/assets/arrows/double-arrows.svg"
                  [size]="sizes.S"
                ></fin-icon>
              }
            </span>
          }
        </div>
      </mat-header-cell>
      <mat-cell
        *matCellDef="let row"
        [style.flex-grow]="column.flexGrow || 1"
        [style.width]="column.width || 100 | finPxToRem"
      >
        @if (!row.isGroupBy) {
          <div
            class="fin-w-full"
            [ngClass]="{
              'fin-flex fin-justify-end fin-items-center':
                column.templateName === 'actions',
            }"
          >
            @if (
              rowDetailsTemplateDirective &&
              column.templateName === 'actions' &&
              (canExpand ? (row | finExecuteFunc: canExpand) : true)
            ) {
              <fin-icon
                name="keyboard_arrow_down"
                [matIconOutlined]="true"
                [size]="finSize.M"
                [class.fin-rotate-180]="expandedRows.includes(row)"
              >
              </fin-icon>
            }

            <ng-container
              [ngTemplateOutlet]="rowTemplates[column.templateName]"
              [ngTemplateOutletContext]="{ $implicit: row }"
            ></ng-container>
          </div>
        }
      </mat-cell>
    </ng-container>
  }

  <!-- Group Header Template -->
  @if (finRowGroupHeaderDirective && groupRowsBy) {
    <ng-container matColumnDef="groupHeader">
      <mat-cell [attr.colspan]="displayedColumns.length" *matCellDef="let row">
        <div>
          <ng-container
            [ngTemplateOutlet]="finRowGroupHeaderDirective.finRowGroupTemplate"
            [ngTemplateOutletContext]="{ $implicit: row }"
          ></ng-container>
        </div>
      </mat-cell>
    </ng-container>
    <mat-row
      class="header-group"
      *matRowDef="let row; columns: ['groupHeader']; when: isGroup"
    ></mat-row>
  }

  <!-- Row Detail Template -->
  @if (rowDetailsTemplateDirective) {
    <ng-container matColumnDef="expandedDetail">
      <mat-cell *matCellDef="let row" [attr.colspan]="displayedColumns.length">
        <div class="expanded-detail-row-container">
          <ng-container
            [ngTemplateOutlet]="rowDetailsTemplateDirective.rowDetailsTemplate"
            [ngTemplateOutletContext]="{ $implicit: row }"
          ></ng-container>
        </div>
      </mat-cell>
    </ng-container>
  }

  <mat-header-row
    *matHeaderRowDef="displayedColumns; sticky: true"
    [style.height]="headerHeight"
    [ngClass]="headerClasses"
  ></mat-header-row>
  <mat-row
    *matRowDef="let row; let i = index; columns: displayedColumns"
    [style.height]="rowHeight"
    [ngClass]="{
      'fin-cursor-pointer':
        rowDetailsTemplateDirective &&
        (canExpand ? (row | finExecuteFunc: canExpand) : true),
      'expanded-row': expandedRows.includes(row),
    }"
    class="{{ row | finExecuteFunc: rowClassFn }}"
    (click)="onRowClick(row)"
  ></mat-row>

  @if (rowDetailsTemplateDirective) {
    <mat-row
      *matRowDef="let row; columns: ['expandedDetail']"
      class="expanded-detail-row"
      [class.expanded]="expandedRows.includes(row)"
    ></mat-row>
  }
</mat-table>

@if (dataSource.data) {
  @if (!dataSource.data.length && emptyMessage) {
    <div
      class="fin-flex fin-justify-center fin-items-center fin-h-[4rem] fin-text-body-2-strong fin-text-color-text-tertiary"
    >
      {{ emptyMessage }}
    </div>
  }

  @if (!dataSource.data.length && finTableNoDataTemplate) {
    <ng-container [ngTemplateOutlet]="finTableNoDataTemplate"></ng-container>
  }
}
