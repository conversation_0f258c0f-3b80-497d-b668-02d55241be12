import { CommonModule } from '@angular/common';
import {
  AfterContentInit,
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ContentChild,
  ContentChildren,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  QueryList,
  SimpleChanges,
  TemplateRef,
  ViewChild,
  booleanAttribute,
} from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatSort } from '@angular/material/sort';
import {
  MatTable,
  MatTableDataSource,
  MatTableModule,
} from '@angular/material/table';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinObserversModule } from '@fincloud/ui/observers';
import { FinSize } from '@fincloud/ui/types';
import { detectChangesOnce } from '@fincloud/utils/angular-functions';
import { pxToRem } from '@fincloud/utils/functions';
import { FinExecuteFuncPipe, FinPxToRemPipe } from '@fincloud/utils/pipes';
import { FinHeaderTemplateDirective } from '../../directives/header-template.directive';
import { FinRowDetailsTemplateDirective } from '../../directives/row-details-template.directive';
import { FinRowGroupTemplateDirective } from '../../directives/row-group-template.directive';
import { FinRowTemplateDirective } from '../../directives/row-template.directive';
import { FinTableColumnMode } from '../../enums/fin-table-column-mode';
import { FinTableColumn } from '../../models/fin-table-column';
import { FinTableSort } from '../../models/fin-table-sort';

/**
 * A component for displaying data in rows and columns.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-table--docs Storybook Reference}
 */
@Component({
  selector: 'fin-table',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    FinIconModule,
    MatTableModule,
    FinExecuteFuncPipe,
    FinObserversModule,
    FinPxToRemPipe,
  ],
  templateUrl: './table.component.html',
  styleUrl: './table.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinTableComponent<T>
  implements AfterViewInit, AfterContentInit, OnChanges
{
  /** Automatically hides certain columns when the table is resized.
   * Columns that should be hidden need to have a `hideThreshold` property set in their configuration.
   * @see FinTableColumn
   * */
  @Input({ transform: booleanAttribute }) autoHideColumns = false;

  /**  Iterable table items. */
  @Input({ required: true }) rows: T[] = [];

  /** Defined columns. */
  @Input({ required: true }) columns: FinTableColumn[] = [];

  /**
   * Mode of the columns:
   *
   * `force`: Automatically aligns columns.
   *
   * `standard`: Sets custom column width (default: 100px).
   *
   * `flex`: Utilizes a flexible grid (default flexGrow: 1).
   *
   * `width` and `flexGrow` are part of `Column`
   */
  @Input() columnMode: FinTableColumnMode = FinTableColumnMode.FLEX;

  /** Specifies the height of the header in `px` units. */
  @Input({ transform: pxToRem }) headerHeight = pxToRem(50);

  /** Specifies the height of each row in `px` units. */
  @Input({ transform: pxToRem }) rowHeight = pxToRem(50);

  /** Determines whether to use default sorting or BE sorting. */
  @Input({ transform: booleanAttribute }) useDefaultSort = false;

  /** Initial sort. */
  @Input() initialSort: FinTableSort | undefined = undefined;

  /** Adds border radius to both the header and the rows. */
  @Input({ transform: booleanAttribute }) hasBorderRadius = false;

  /** <span class="fin-text-red-500 fin-font-bold">Deprecated</span><br/>Makes the header background transparent. */
  @Input() isHeaderBgTransparent = false;

  /** Custom header classes. For example to set a background or top position for the sticky header.<br/>
   *  <span class="fin-text-red-500">The classes might need !important to work properly.</span>
   */
  @Input() headerClasses = '';

  /** Adds a border separator between the rows. */
  @Input({ transform: booleanAttribute }) hasRowBorder = false;

  /** Adds a partial top border to the rows. */
  @Input({ transform: booleanAttribute }) hasRowPartialBorder = false;

  /** Adds space between rows. */
  @Input({ transform: booleanAttribute }) hasRowSpacing = false;

  /** <span class="fin-text-red-500 fin-font-bold">Deprecated</span> */
  @Input({ transform: booleanAttribute }) scrollbarH = false;

  /** <span class="fin-text-red-500 fin-font-bold">Deprecated</span> */
  @Input({ transform: booleanAttribute }) scrollbarV = false;

  /** <span class="fin-text-red-500 fin-font-bold">Deprecated</span><br/>Enables manual column resizing. */
  @Input({ transform: booleanAttribute }) resizeable = false;

  /** Message displayed when the table has no data to show. */
  @Input() emptyMessage = '';

  /** Message displayed when the table has no data to show. */
  @Input({ transform: booleanAttribute }) hideHeader = false;

  /** <span class="fin-text-red-500 fin-font-bold">Deprecated</span><br/>Boolean to set whether groups start expanded by default, it is `true` by default. */
  @Input({ transform: booleanAttribute }) expandGroups = true;

  /** This attribute allows the user to set the name of the column to group the data with. */
  @Input() groupRowsBy = '';

  /** When `finRowDetailsTemplate` is used and `multiExpandDetails` is set to `true`, it allows multiple row details to be expanded. */
  @Input({ transform: booleanAttribute }) multiExpandDetails = false;

  /** Hide specific columns by providing their template name in an array. */
  @Input() columnsToHide: string[] = [];

  /** When click on rows `canExpand` will be called to determine if the row can be expanded. */
  @Input({ required: false }) canExpand!: ((row: T) => boolean) | undefined;

  /** A function that returns custom CSS class(es) for each row. */
  @Input() rowClassFn: (row: T, index: number) => string = () => '';

  /** Event emitted when `useDefaultSort` is `false`. */
  @Output() sortChange = new EventEmitter<FinTableSort>();

  /** Event emitted when a row is clicked. The emitted value is the clicked row. */
  @Output() rowClick = new EventEmitter<T>();

  @ContentChildren(FinRowTemplateDirective)
  protected rowTemplateDirectives!: QueryList<FinRowTemplateDirective<T>>;

  @ContentChildren(FinHeaderTemplateDirective)
  protected headerTemplateDirectives!: QueryList<FinHeaderTemplateDirective<T>>;

  @ContentChild(FinRowDetailsTemplateDirective)
  protected rowDetailsTemplateDirective!: FinRowDetailsTemplateDirective<T>;

  @ContentChild(FinRowGroupTemplateDirective)
  protected finRowGroupHeaderDirective!: FinRowGroupTemplateDirective<T>;

  @ContentChild('finTableNoDataTemplate')
  protected finTableNoDataTemplate?: TemplateRef<unknown>;

  @ViewChild(MatSort) private matSort!: MatSort;
  @ViewChild(MatTable, { static: true }) private table!: MatTable<any>;

  protected rowTemplates: Record<string, TemplateRef<unknown>> = {};
  protected headerTemplates: Record<string, TemplateRef<unknown>> = {};
  protected rowDetailsTemplates!: TemplateRef<unknown>;
  protected sizes = FinSize;
  protected sort: FinTableSort | undefined = undefined;
  protected finSize = FinSize;
  protected displayedColumns: string[] = [];
  protected expandedRows: T[] = [];
  protected dataSource!: MatTableDataSource<T>;

  constructor(private cd: ChangeDetectorRef) {}

  ngAfterViewInit() {
    this.dataSource.sort = this.matSort;

    if (this.initialSort) {
      this.sort = this.initialSort;
      this.executeDefaultSort(this.sort);
    }
  }

  ngAfterContentInit() {
    this.dataSource = new MatTableDataSource(this.rows);

    this.rowTemplateDirectives.forEach((directive) => {
      this.rowTemplates[directive.name] = directive.rowTemplate;
    });

    this.headerTemplateDirectives.forEach((directive) => {
      this.headerTemplates[directive.name] = directive.headerTemplate;
    });

    this.filterDisplayedColumns();

    if (this.groupRowsBy) {
      this.groupTableData();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['rows'] && this.dataSource?.data) {
      if (this.groupRowsBy) {
        this.groupTableData();
      } else {
        this.dataSource.data = this.rows;
      }
    }

    if (changes['columnsToHide'] && !this.autoHideColumns) {
      this.filterDisplayedColumns();
    }
  }

  private filterDisplayedColumns() {
    this.displayedColumns = this.columns
      .filter((column) => !this.columnsToHide.includes(column.templateName))
      .map((column) => column.prop);
  }

  protected onTableResized(entry: ResizeObserverEntry): void {
    const visibleColumns = this.displayedColumns.length;

    this.displayedColumns = this.columns
      .filter((column) => (column.hideThreshold || 0) < entry.contentRect.width)
      .map((column) => column.prop);

    if (this.displayedColumns.length !== visibleColumns) {
      detectChangesOnce(this.cd);
    }
  }

  protected onSortChange(column: FinTableColumn) {
    const { prop, isSortable } = column;

    if (isSortable) {
      let currentSort: FinTableSort = {
        prop,
        dir: 'asc',
      };

      if (this.sort) {
        const { dir } = this.sort;
        currentSort = {
          prop,
          dir: dir === 'asc' ? 'desc' : 'asc',
        };
      }

      this.sort = currentSort;

      if (this.useDefaultSort) {
        this.executeDefaultSort(this.sort);
      } else {
        this.sortChange.emit(this.sort);
      }
    }
  }

  protected onHeaderCellMouseDown(): void {
    const selection = window.getSelection() ?? document.getSelection();
    selection?.empty();
  }

  protected isGroup<T extends { isGroupBy: boolean }>(index: number, row: T) {
    return row.isGroupBy;
  }

  protected onRowClick(clickedRow: T) {
    if (this.canExpand && !this.canExpand(clickedRow)) {
      return;
    }

    if (this.rowDetailsTemplateDirective) {
      if (this.expandedRows.includes(clickedRow)) {
        this.expandedRows = this.expandedRows.filter(
          (row) => row !== clickedRow,
        );
      } else {
        if (!this.multiExpandDetails) {
          this.expandedRows = [];
        }
        this.expandedRows.push(clickedRow);
      }
    }

    this.rowClick.emit(clickedRow);
  }

  private groupTableData() {
    const groupedRows = this.groupBy(this.rows, this.groupRowsBy);
    this.dataSource.data = [];

    Object.keys(groupedRows).forEach((key) => {
      this.dataSource.data.push({ key, isGroupBy: true } as T);
      const values = groupedRows[key];
      values.forEach((row: T) => {
        this.dataSource.data.push(row);
      });
    });

    this.dataSource.data = [...this.dataSource.data];
  }

  private groupBy(rows: T[], key: string) {
    return rows.reduce((previous: Record<string, T[]>, current: T) => {
      const currentKey = current[key as keyof T] as string;
      previous[currentKey] = previous[currentKey] || [];
      previous[currentKey].push(current);
      return previous;
    }, {});
  }

  private executeDefaultSort(sort: FinTableSort) {
    const { prop, dir } = sort;
    const updatedSort = {
      active: prop,
      direction: dir,
    } as MatSort;

    this.dataSource.data = this.dataSource.sortData(
      this.dataSource.data,
      updatedSort,
    );
  }
}
