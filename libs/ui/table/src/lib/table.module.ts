import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FinTableComponent } from './components/table/table.component';
import { FinHeaderTemplateDirective } from './directives/header-template.directive';
import { FinRowDetailsTemplateDirective } from './directives/row-details-template.directive';
import { FinRowGroupTemplateDirective } from './directives/row-group-template.directive';
import { FinRowTemplateDirective } from './directives/row-template.directive';

@NgModule({
  imports: [
    CommonModule,
    FinTableComponent,
    FinRowTemplateDirective,
    FinHeaderTemplateDirective,
    FinRowDetailsTemplateDirective,
    FinRowGroupTemplateDirective,
  ],
  exports: [
    FinTableComponent,
    FinRowTemplateDirective,
    FinHeaderTemplateDirective,
    FinRowDetailsTemplateDirective,
    FinRowGroupTemplateDirective,
  ],
})
export class FinTableModule {}
