import { Directive, Input, OnInit, TemplateRef } from '@angular/core';
import { CtxRowTemplateDirective } from '../models/context-row-template-directive';

@Directive({
  selector: 'ng-template[finRowTemplate]',
  standalone: true,
})
export class FinRowTemplateDirective<T> implements OnInit {
  /**
   * Row definition for `fin-table`. Needs to be used with `<ng-template></<ng-template>`.
   */
  @Input('finRowTemplate') data: T[] = [];

  @Input({ required: true }) name = '';

  rowTemplate!: TemplateRef<unknown>;

  constructor(private _rowTemplate: TemplateRef<unknown>) {}

  ngOnInit(): void {
    this.rowTemplate = this._rowTemplate;
  }

  static ngTemplateContextGuard<T>(
    directive: FinRowTemplateDirective<T>,
    context: unknown,
  ): context is CtxRowTemplateDirective<T> {
    return true;
  }
}
