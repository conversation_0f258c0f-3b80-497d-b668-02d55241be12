import { Directive, Input, OnInit, TemplateRef } from '@angular/core';
import { CtxRowGroupTemplateDirective } from '../models/row-group-template-directive';

@Directive({
  selector: 'ng-template[finRowGroupTemplate]',
  standalone: true,
})
export class FinRowGroupTemplateDirective<T> implements OnInit {
  /**
   * Row details definition for `fin-table`. Needs to be used with `<ng-template></<ng-template>`.
   */
  @Input('finRowGroupTemplate') data: T[] = [];

  finRowGroupTemplate!: TemplateRef<unknown>;

  constructor(private _finRowGroupTemplate: TemplateRef<unknown>) {}

  ngOnInit(): void {
    this.finRowGroupTemplate = this._finRowGroupTemplate;
  }

  static ngTemplateContextGuard<T>(
    directive: FinRowGroupTemplateDirective<T>,
    context: unknown,
  ): context is CtxRowGroupTemplateDirective<T> {
    return true;
  }
}
