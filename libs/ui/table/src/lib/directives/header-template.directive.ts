import { Directive, Input, OnInit, TemplateRef } from '@angular/core';
import { CtxHeaderTemplateDirective } from '../models/context-header-template-directive';

@Directive({
  selector: 'ng-template[finHeaderTemplate]',
  standalone: true,
})
export class FinHeaderTemplateDirective<T> implements OnInit {
  /**
   * Header definition for `fin-table`. Needs to be used with `<ng-template></<ng-template>`.
   */
  @Input('finHeaderTemplate') data: T[] = [];

  @Input({ required: true }) name = '';

  headerTemplate!: TemplateRef<unknown>;

  constructor(private _headerTemplate: TemplateRef<unknown>) {}

  ngOnInit(): void {
    this.headerTemplate = this._headerTemplate;
  }

  static ngTemplateContextGuard<T>(
    directive: FinHeaderTemplateDirective<T>,
    context: unknown,
  ): context is CtxHeaderTemplateDirective<T> {
    return true;
  }
}
