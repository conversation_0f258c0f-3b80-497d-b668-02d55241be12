import { Directive, Input, OnInit, TemplateRef } from '@angular/core';
import { CtxRowTemplateDirective } from '../models/context-row-template-directive';

@Directive({
  selector: 'ng-template[finRowDetailsTemplate]',
  standalone: true,
})
export class FinRowDetailsTemplateDirective<T> implements OnInit {
  /**
   * Row details definition for `fin-table`. Needs to be used with `<ng-template></<ng-template>`.
   */
  @Input('finRowDetailsTemplate') data: T[] = [];

  rowDetailsTemplate!: TemplateRef<unknown>;

  constructor(private _rowDetailsTemplate: TemplateRef<unknown>) {}

  ngOnInit(): void {
    this.rowDetailsTemplate = this._rowDetailsTemplate;
  }

  static ngTemplateContextGuard<T>(
    directive: FinRowDetailsTemplateDirective<T>,
    context: unknown,
  ): context is CtxRowTemplateDirective<T> {
    return true;
  }
}
