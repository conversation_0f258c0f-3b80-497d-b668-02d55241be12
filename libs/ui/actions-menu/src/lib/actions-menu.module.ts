import { CdkMenuModule } from '@angular/cdk/menu';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FinActionsMenuComponent } from './components/action-menu/actions-menu.component';
import { FinActionMenuTriggerDirective } from './directives/fin-action-menu-trigger.directive';
@NgModule({
  imports: [
    CommonModule,
    FinActionsMenuComponent,
    FinActionMenuTriggerDirective,
    CdkMenuModule,
  ],
  exports: [FinActionsMenuComponent, FinActionMenuTriggerDirective],
})
export class FinActionsMenuModule {}
