import { CommonModule } from '@angular/common';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinMenuItemModule } from '@fincloud/ui/menu-item';
import { FinSlideToggleModule } from '@fincloud/ui/slide-toggle';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { FinActionsMenuModule } from '../../actions-menu.module';
import { FinActionsMenuComponent } from './actions-menu.component';

const meta: Meta<FinActionsMenuComponent> = {
  component: FinActionsMenuComponent,
  title: 'Components/Actions Menu',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinActionsMenuModule } from "@fincloud/ui/actions-menu"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6547-19162&m=dev',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        FinActionsMenuModule,
        FinIconModule,
        FinSlideToggleModule,
        FinButtonModule,
        FinTruncateTextModule,
        FinMenuItemModule,
      ],
    }),
  ],
};
export default meta;

type Story = StoryObj<FinActionsMenuComponent>;

const actionMenuHtml = `
      <fin-actions-menu #finMenu="finActionMenu" class="fin-w-[24rem]" [xPosition]="xPosition" [yPosition]="yPosition">
          <button 
            fin-menu-item 
            iconName="image"
            size="m"
            (click)="selected()">
              <ng-container finMenuItemTitle>Menu item 1</ng-container>
              <ng-container finMenuItemDescription><span>Description</span></ng-container>
              <ng-container finMenuItemDescription><span>Additional text</span></ng-container>
          </button>
          <button 
            fin-menu-item 
            disabled
            iconName="image"
            size="m">
              <ng-container finMenuItemTitle>
                Menu item 3
              </ng-container>
          </button>
          <button 
            fin-menu-item 
            attention="true"
            iconName="image"
            size="m">
              <ng-container finMenuItemTitle>
                Menu item 4
              </ng-container>
          </button>
          <button 
            fin-menu-item 
            iconName="image"
            size="m">
              <ng-container finMenuItemTitle>
                <div finTruncateText class="fin-max-w-[13.6rem]">Menu item text with more symbols</div>
              </ng-container>
          </button>
          <button 
            fin-menu-item 
            iconName="image"
            size="m">
              <ng-container finMenuItemTitle>
                Menu item 6
              </ng-container>
          </button>      
      </fin-actions-menu>
      `;

export const Primary: Story = {
  argTypes: {
    xPosition: {
      control: { type: 'select' },
      options: ['before', 'after'],
    },
    yPosition: {
      control: { type: 'select' },
      options: ['below', 'above'],
    },
    class: {
      control: { type: 'text' },
    },
  },
  args: {
    xPosition: 'before',
    yPosition: 'below',
    class: '',
  },
  render: (args) => ({
    props: { ...args },
    template: `
      <button fin-button-action [finActionMenuTrigger]="finMenu.panel">
        <fin-icon name="more_vert" [size]="'l'"></fin-icon>
      </button>

      ${actionMenuHtml}
    `,
  }),
};

export const Button: Story = {
  argTypes: {
    ...Primary.argTypes,
  },
  args: {
    ...Primary.args,
    xPosition: 'after',
  },
  render: (args) => ({
    props: args,
    template: `
      <button
        fin-button
        [size]="'m'" [shape]="'round'" [appearance]="'secondary'"
        [finActionMenuTrigger]="finMenu.panel"
        #trigger="finActionMenuTrigger"
      >
        Button
        <fin-icon name="keyboard_arrow_down" *ngIf="(trigger.menuOpen | async) === false" ></fin-icon>
        <fin-icon name="keyboard_arrow_up" *ngIf="(trigger.menuOpen | async) === true" ></fin-icon>
      </button>
      ${actionMenuHtml}
    `,
  }),
};

export const FullContent: Story = {
  argTypes: {
    ...Primary.argTypes,
  },
  args: {
    xPosition: 'after',
    yPosition: 'below',
  },
  render: (args) => ({
    props: args,
    template: `
      <button
        fin-button
        [size]="'m'" [shape]="'round'" [appearance]="'secondary'"
        [finActionMenuTrigger]="finMenu.panel"
        #trigger="finActionMenuTrigger"
       
      >
        Button
        <fin-icon name="keyboard_arrow_down" *ngIf="(trigger.menuOpen | async) === false" ></fin-icon>
        <fin-icon name="keyboard_arrow_up" *ngIf="(trigger.menuOpen | async) === true" ></fin-icon>
      </button>

      <fin-actions-menu #finMenu="finActionMenu" [xPosition]="xPosition" [yPosition]="yPosition" class="fin-w-[34rem]">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque nulla justo, ullamcorper eu blandit sit amet, dictum nec ante. Aliquam pretium arcu sed molestie venenatis. 
      </fin-actions-menu>
    `,
  }),
};
