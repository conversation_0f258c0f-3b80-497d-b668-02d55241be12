import { Directive, Inject, Input } from '@angular/core';
import { MatMenuPanel, MatMenuTrigger } from '@angular/material/menu';
import { map, merge, mergeAll, shareReplay, startWith } from 'rxjs';

@Directive({
  selector: '[finActionMenuTrigger]',
  standalone: true,
  hostDirectives: [
    {
      directive: MatMenuTrigger,
      inputs: ['matMenuTriggerFor: finActionMenuTrigger'],
    },
  ],
  providers: [
    {
      provide: 'MAT_MENU_TRIGGER',
      useExisting: MatMenuTrigger,
    },
  ],
  exportAs: 'finActionMenuTrigger',
})
export class FinActionMenuTriggerDirective {
  @Input() finActionMenuTrigger!: MatMenuPanel | null;
  menuOpen = merge([
    this.matMenuTrigger.menuOpened.pipe(map(() => true)),
    this.matMenuTrigger.menuClosed.pipe(map(() => false)),
  ]).pipe(
    mergeAll(),
    startWith(false),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  constructor(
    @Inject('MAT_MENU_TRIGGER') private matMenuTrigger: MatMenuTrigger,
  ) {}
}
