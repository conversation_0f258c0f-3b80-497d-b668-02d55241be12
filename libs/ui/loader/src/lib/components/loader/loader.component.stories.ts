import { CommonModule } from '@angular/common';
import { moduleMetadata, type Meta, type StoryObj } from '@storybook/angular';
import { FinLoaderModule } from './../../loader.module';
import { FinLoaderComponent } from './loader.component';

const meta: Meta<FinLoaderComponent> = {
  component: FinLoaderComponent,
  title: 'Components/Loader',
  parameters: {
    docs: {
      description: {
        component: '`import { FinLoaderModule } from "@fincloud/ui/loader"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=12920-4094&m=dev',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [CommonModule, FinLoaderModule],
    }),
  ],
};
export default meta;
type Story = StoryObj<FinLoaderComponent>;

export const Primary: Story = {
  args: {
    hide: false,
  },
  argTypes: {
    hide: {
      control: 'boolean',
    },
  },
};
