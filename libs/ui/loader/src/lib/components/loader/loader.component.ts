import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

/**
 * An animation or indicator that shows loading progress.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-loader--docs Storybook Reference}
 */
@Component({
  selector: 'fin-loader',
  standalone: true,
  imports: [],
  templateUrl: './loader.component.html',
  styleUrl: './loader.component.scss',
  host: {
    class: 'fin-loader',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinLoaderComponent {
  /** Hide the loader. */
  @Input() hide = false;
}
