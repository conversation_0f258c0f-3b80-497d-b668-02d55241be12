# ui-ai-suggestion

This library provides a reusable AI suggestion component for Angular applications.

## Features

- Reactive AI suggestion animations
- Automatic timing with 2-second auto-hide
- Form control integration
- Proper cleanup and memory management
- Event emission on animation completion

## Usage

```typescript
import { FinAiSuggestionComponent } from '@fincloud/ui/ai-suggestion';

@Component({
  template: `
    <fin-ai-suggestion 
      [aiEnabled]="true" 
      [control]="formControl"
      (aiSuggestionReady)="onSuggestionReady()">
    </fin-ai-suggestion>
  `
})
export class MyComponent {
  formControl = new FormControl('');
  
  onSuggestionReady() {
    console.log('AI suggestion animation completed');
  }
}
```

## Running unit tests

Run `nx test ui-ai-suggestion` to execute the unit tests.
