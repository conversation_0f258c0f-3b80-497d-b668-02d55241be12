:host {
  @apply fin-block;
  @apply fin-blur-sm;
  @apply fin-h-auto;
  @apply fin-whitespace-nowrap;
  @apply fin-px-2;
  @extend .fin-animate-expand-width;
  @extend .fin-text-ai-gradient;
}
.fin-animate-expand-width {
  width: 0%;
  animation: expandWidth 1s ease-in forwards;
}
.fin-text-ai-gradient {
  background: linear-gradient(
    259deg,
    #060fd0 -0.13%,
    #03086a 16.48%,
    #8e1ccc 36.53%,
    #556bff 53.71%,
    #198aff 69.76%,
    #556bff 84.65%,
    #060fd0 97.83%,
    #8e1ccc 113.29%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}
@keyframes expandWidth {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
