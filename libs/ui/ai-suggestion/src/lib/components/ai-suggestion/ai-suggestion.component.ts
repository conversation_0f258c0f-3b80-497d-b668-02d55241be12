import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  ElementRef,
  EventEmitter,
  Injector,
  Input,
  OnChanges,
  OnInit,
  Output,
  Renderer2,
  SimpleChanges,
  booleanAttribute,
  forwardRef,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NG_VALUE_ACCESSOR } from '@angular/forms';
import { FinControlValueAccessor } from '@fincloud/utils/control-value-accessor';
import {
  BehaviorSubject,
  combineLatest,
  filter,
  startWith,
  switchMap,
  tap,
  timer,
} from 'rxjs';

/**
 * Reusable AI suggestion component that provides animated overlay functionality
 * for form controls. Shows AI suggestions on value changes with automatic timing.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-ai-suggestion--docs Storybook Reference}
 */
@Component({
  selector: 'fin-ai-suggestion',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './ai-suggestion.component.html',
  styleUrl: './ai-suggestion.component.scss',
  host: {
    class: 'fin-ai-suggestion',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FinAiSuggestionComponent),
      multi: true,
    },
  ],
})
export class FinAiSuggestionComponent
  extends FinControlValueAccessor
  implements OnInit, OnChanges
{
  /**
   * Enables AI suggestion animations. When true, suggestions appear on input changes and auto-hide after 2 seconds.
   * Animation only triggers when BOTH `aiEnabled` is true AND input value changes.
   * @default false
   */
  @Input({ transform: booleanAttribute }) aiEnabled = false;

  /**
   * Emitted when the AI suggestion animation completes.
   * Only emits when the animation completes naturally, not when aiEnabled is disabled.
   */
  @Output() aiSuggestionReady = new EventEmitter<void>();

  /** Internal BehaviorSubject to manage aiEnabled state reactively */
  private aiEnabledSubject$$ = new BehaviorSubject<boolean>(false);

  /** Internal BehaviorSubject to manage AI suggestion visibility */
  private aiSuggestionVisible$$ = new BehaviorSubject<boolean>(false);

  /** Tracks `aiEnabled` input state for reactive state management. Combined with valueChanges to trigger animations. */
  protected aiEnabled$ = this.aiEnabledSubject$$.asObservable();

  /**
   * Controls AI suggestion div visibility with timing logic. Shows on value changes when `aiEnabled=true`,
   * auto-hides after 2 seconds. Template subscribes to this for actual show/hide behavior.
   */
  protected aiSuggestionVisible$ = this.aiSuggestionVisible$$.asObservable();

  constructor(
    injector: Injector,
    private destroyRef: DestroyRef,
    private elementRef: ElementRef,
    private renderer: Renderer2,
  ) {
    super(injector);
  }

  ngOnInit(): void {
    // Initialize aiEnabled state
    this.aiEnabledSubject$$.next(this.aiEnabled);

    // Set up AI suggestion animation logic
    this.setupAiSuggestionAnimation();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Handle aiEnabled changes
    if (changes['aiEnabled']) {
      this.aiEnabledSubject$$.next(changes['aiEnabled'].currentValue);
    }
  }

  /**
   * Sets up the AI suggestion animation logic that triggers when both
   * aiEnabled is true AND the form control value changes
   */
  private setupAiSuggestionAnimation(): void {
    // Combine aiEnabled state with form control value changes
    combineLatest([
      this.aiEnabledSubject$$,
      this.control.valueChanges.pipe(startWith(this.control.value)),
    ])
      .pipe(
        // Only proceed when aiEnabled is true and we have a value change
        filter(([aiEnabled]: [boolean, string | null]) => aiEnabled === true),
        // Switch to a timer that will hide the suggestion after 2 seconds
        switchMap(() => {
          // Show the AI suggestion immediately
          this.aiSuggestionVisible$$.next(true);
          this.renderer.addClass(
            this.elementRef.nativeElement,
            'fin-ai-suggestion-active',
          );
          // Return a timer that will hide it after 2 seconds
          return timer(2000);
        }),
        // Hide the AI suggestion when timer completes and emit ready event
        tap(() => {
          this.aiSuggestionVisible$$.next(false);
          this.renderer.removeClass(
            this.elementRef.nativeElement,
            'fin-ai-suggestion-active',
          );
          this.aiSuggestionReady.emit();
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
