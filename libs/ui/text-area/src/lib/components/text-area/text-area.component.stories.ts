import { CommonModule } from '@angular/common';
import { FormControl, Validators } from '@angular/forms';
import { FinActionsMenuModule } from '@fincloud/ui/actions-menu';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinFieldMessageModule } from '@fincloud/ui/field-message';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinMenuItemModule } from '@fincloud/ui/menu-item';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinSize } from '@fincloud/ui/types';
import {
  componentWrapperDecorator,
  moduleMetadata,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { FinTextAreaModule } from '../../text-area.module';
import { FinTextAreaComponent } from './text-area.component';

const meta: Meta<FinTextAreaComponent> = {
  component: FinTextAreaComponent,
  title: 'Fields/Text Area',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinTextAreaModule } from "@fincloud/ui/text-area"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=5900-6171&m=dev',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        FinTextAreaModule,
        FinFieldMessageModule,
        FinIconModule,
        FinActionsMenuModule,
        FinTruncateTextModule,
        FinButtonModule,
        FinMenuItemModule,
      ],
    }),
    componentWrapperDecorator(
      (story) => `<div class="fin-w-[300px]">${story}</div>`,
    ),
  ],
};
export default meta;
type Story = StoryObj<
  FinTextAreaComponent & {
    finInputLabel: string;
    finInputSuffix: string;
    finInputHint: string;
  }
>;

const templateWithActionMenu = `
  <fin-text-area
  [formControl]="formControl"
  placeholder="Placeholder"
  label="Label"
  [size]="size"
  >
    <ng-container finInputSuffix>
      <button type="button" [finActionMenuTrigger]="finMenu.panel">
        <fin-icon name="more_vert" [size]="'l'"></fin-icon>
      </button>

      <fin-actions-menu
        #finMenu="finActionMenu"
      >
        <button 
          fin-menu-item
          size="m" 
          iconName="image">
          <ng-container finMenuItemTitle>Menu item 1</ng-container>
        </button>   
        <button 
          fin-menu-item
          size="m" 
          iconName="image">
          <ng-container finMenuItemTitle>Menu item 2</ng-container>
        </button>      
      </fin-actions-menu>
    </ng-container>
  </fin-text-area>
`;

const disabledField = new FormControl({ value: '', disabled: true });
const invalidField = new FormControl('', Validators.required);
invalidField.markAsTouched();
const validField = new FormControl('');

export const Default: Story = {
  args: {
    label: 'Label',
    placeholder: 'Placeholder',
    rows: 4,
  },
  argTypes: {
    label: {
      control: { type: 'text' },
    },
    placeholder: {
      control: { type: 'text' },
    },
    size: {
      options: [FinSize.M, FinSize.L],
      control: { type: 'select' },
    },
    maxLength: {
      control: { type: 'number' },
    },
    readonly: {
      control: 'boolean',
    },
    finInputLabel: {
      description: 'Place the label with addition icons next to it',
      control: false,
    },
    finInputHint: {
      description: 'Place for a hint text',
      control: false,
    },
    finInputSuffix: {
      description: 'Place icons after the text area',
      control: false,
    },
    enterPressedDown: {
      control: false,
    },
  },
  render: (args) => ({
    props: { ...args, formControl: validField },
  }),
};

export const CustomLabel: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: validField,
        clickFn: function () {
          alert('Btn clicked!');
        },
      },
      template: `
        <fin-text-area
          [formControl]="formControl"
          placeholder="Placeholder"
        >
          <ng-container finInputLabel>
            <label class="fin-w-[24rem]" finTruncateText>
              Longer label with multiple actions 
            </label>

            <div class="fin-flex fin-gap-[0.4rem]">
              <button fin-button-action (click)="clickFn()">
                <fin-icon src="/assets/storybook/input/chat-bubble.svg"></fin-icon>
              </button>

              <button fin-button-action (click)="clickFn()">
                <fin-icon src="/assets/storybook/input/info.svg"></fin-icon>
              </button>

              <button fin-button-action (click)="clickFn()">
                <fin-icon src="/assets/storybook/input/visibility-off.svg"></fin-icon>
              </button>
            </div>
          </ng-container>
        </fin-text-area>
      `,
    };
  },
};

export const WithActionMenu: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: validField,
      },
      template: templateWithActionMenu,
    };
  },
};

export const ValidationSuccess: Story = {
  argTypes: {},
  args: {
    ...Default.args,
  },
  render: (args) => {
    const formControl = new FormControl('');
    formControl.markAsTouched();

    return {
      props: {
        ...args,
        formControl,
      },
      template: `
        <fin-text-area
          [label]="label"
          [formControl]="formControl"
          
        >
          <fin-field-messages>
            <ng-template finFieldMessage type="success">
              Some success message.
            </ng-template>
          </fin-field-messages>          
        </fin-text-area>
      `,
    };
  },
};

export const ValidationError: Story = {
  argTypes: {},
  args: {
    ...Default.args,
  },
  render: (args) => {
    const formControl = new FormControl(null, [Validators.required]);
    formControl.markAsTouched();

    return {
      props: {
        ...args,
        formControl,
      },
      template: `
        <fin-text-area
          [label]="label"
          [formControl]="formControl"
          
        >
          <fin-field-messages>
            <ng-template finFieldMessage type="error" errorKey="required">
              Some error message.
            </ng-template>
          </fin-field-messages>          
        </fin-text-area>
      `,
    };
  },
};

export const ValidationWarning: Story = {
  argTypes: {},
  args: {
    ...Default.args,
  },
  render: (args) => {
    const formControl = new FormControl(null, [Validators.requiredTrue]);
    formControl.markAsTouched();
    return {
      props: {
        ...args,
        formControl,
      },
      template: `
        <fin-text-area
          [label]="label"
          [formControl]="formControl"
        >
          <fin-field-messages>
            <ng-template finFieldMessage type="warning" errorKey="required">
              Some warning message.
            </ng-template>
          </fin-field-messages>          
        </fin-text-area>
      `,
    };
  },
};

export const Disabled: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: disabledField,
      },
      template: templateWithActionMenu,
    };
  },
};

export const Hint: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: validField,
      },
      template: `
        <fin-text-area
          [formControl]="formControl"
          placeholder="Placeholder"
        >
          <ng-container finInputLabel>Label</ng-container>

          <ng-container finInputHint>
            <span class="fin-font-semibold">Shift + Enter</span> to insert a new line
          </ng-container>
        </fin-text-area>
      `,
    };
  },
};

export const ReadonlyField: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl(
          'This is some sample text in readonly mode',
        ),
      },
      template: `
        <fin-text-area
          [label]="label"
          [formControl]="formControl"
          [placeholder]="placeholder"
          [rows]="rows"
          [maxLength]="maxLength"
          [readonly]="true"
        ></fin-text-area>
      `,
    };
  },
};

export const AutoResize: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl(''),
      },
      template: `
        <fin-text-area
          [formControl]="formControl"
          placeholder="Placeholder"
          label="Label"
          [autoResize]="true"
          autoResizeMaxRows="20"
          autoResizeMinRows="5"
        >
        </fin-text-area>
        `,
    };
  },
};
