import { SharedResizeObserver } from '@angular/cdk/observers/private';
import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  ElementRef,
  EventEmitter,
  Injector,
  Input,
  NgZone,
  OnInit,
  Optional,
  Output,
  Renderer2,
  ViewChild,
  booleanAttribute,
  forwardRef,
  numberAttribute,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { FinAiSuggestionComponent } from '@fincloud/ui/ai-suggestion';
import {
  FinFieldMessageBodyDirective,
  FinFieldMessageService,
} from '@fincloud/ui/field-message';
import { FinLoaderModule } from '@fincloud/ui/loader';
import { FinScrollbarModule } from '@fincloud/ui/scrollbar';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinErrorSpace, FinSize } from '@fincloud/ui/types';
import {
  FIN_MAT_FORM_FIELD_DEFAULT_OPTIONS,
  FinAngularMaterialModule,
} from '@fincloud/utils/angular-material';
import { FinControlValueAccessor } from '@fincloud/utils/control-value-accessor';
import { pxToRem } from '@fincloud/utils/functions';
import { FinFieldService } from '@fincloud/utils/services';
import { AutosizeModule, WindowRef } from 'ngx-autosize';
import { filter, shareReplay, startWith } from 'rxjs';

/**
 * A multi-line input field for entering text.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-text-area--docs Storybook Reference}
 */
@Component({
  selector: 'fin-text-area',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FinAngularMaterialModule,
    FinTruncateTextModule,
    FinLoaderModule,
    FinScrollbarModule,
    AutosizeModule,
    FinAiSuggestionComponent,
  ],
  templateUrl: './text-area.component.html',
  styleUrl: './text-area.component.scss',
  host: {
    class: 'fin-text-area',
  },

  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FinTextAreaComponent),
      multi: true,
    },
    FIN_MAT_FORM_FIELD_DEFAULT_OPTIONS,
    FinFieldService,
    FinFieldMessageService,
    WindowRef,
  ],
})
export class FinTextAreaComponent
  extends FinControlValueAccessor
  implements OnInit, AfterViewInit
{
  /** Label for the text area. */
  @Input() label = '';

  /** The placeholder for this control. */
  @Input() placeholder = '';

  /** FinSize of the text area. It should be from type FinSize. Default is Medium (FinSize.Medium). */
  @Input() size: FinSize.M | FinSize.L = FinSize.M;

  /** Specifies the number of visible rows in the textarea. */
  @Input() rows = 5;

  /** Specifies the number of maximum chars in the textarea. */
  @Input({ transform: numberAttribute }) maxLength = 0;

  /** Whether the form field should reserve space for error. */
  @Input() dynamicErrorSpace!: FinErrorSpace;

  /** Switch readonly mode. */
  @Input({ transform: booleanAttribute }) readonly = false;

  /** To be used only with external messages */
  @Input() externalFieldMessages: FinFieldMessageBodyDirective | null = null;

  /** Enable automatic resize to the textarea to fit its content */
  @Input({ transform: booleanAttribute }) autoResize = true;

  /**
   * Enables AI suggestion animations. When `true`, suggestions appear on input changes and auto-hide after certain time.
   * Animation only triggers when BOTH `aiEnabled` is `true` AND input value changes.
   */
  @Input({ transform: booleanAttribute }) aiEnabled = false;

  /** Emits event with the text area value when enter key is pressed down. */
  @Output() enterPressedDown = new EventEmitter<string>();

  /**
   * Emitted when the AI suggestion animation completes.
   * Only emits when the animation completes naturally, not when `aiEnabled` is disabled.
   */
  @Output() aiSuggestionReady = new EventEmitter<void>();

  /** Returns the active validation message. */
  protected getMessage$ = this.finFieldMessageService?.getMessage$.pipe(
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  @ViewChild('suffix') protected suffixRef!: ElementRef;
  @ViewChild('textarea') protected textareaRef!: ElementRef;

  constructor(
    private renderer: Renderer2,
    private sharedResizeObserver: SharedResizeObserver,
    private ngZone: NgZone,

    private finFieldService: FinFieldService,
    @Optional() private finFieldMessageService: FinFieldMessageService,

    private destroyRef: DestroyRef,
    injector: Injector,
  ) {
    super(injector);
  }

  ngOnInit(): void {
    this.trimByMaxLength();
    this.control.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((value) => {
        this.finFieldService.value$.next(value);
      });
  }

  ngAfterViewInit(): void {
    this.control.statusChanges
      .pipe(startWith(this.control.errors), takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.finFieldService.controlErrors$.next(this.control.errors);
      });

    this.ngZone.runOutsideAngular(() => {
      this.setTextareaPadding();
    });
  }

  private trimByMaxLength() {
    this.finFieldService.maxLength = this.maxLength;
    if (
      this.maxLength &&
      !this.finFieldService.isInMaxLength(this.control.value)
    ) {
      this.control.setValue(
        this.finFieldService.trimByMaxLength(this.control.value),
        { emitEvent: false },
      );
    }
  }

  protected keydown(event: Event): void {
    this.enterPressedDown.emit(this.control.value);
    const target = event.target as HTMLTextAreaElement;
    target.blur();
    event.preventDefault();
    event.stopPropagation();
  }

  private setTextareaPadding() {
    this.sharedResizeObserver
      .observe(this.suffixRef.nativeElement)
      .pipe(
        filter(() => !!this.suffixRef?.nativeElement),
        takeUntilDestroyed(this.destroyRef),
      )

      .subscribe(() => {
        const suffixWidth = this.suffixRef.nativeElement.offsetWidth;
        const gap = 8;
        const padding = pxToRem(suffixWidth + gap);

        this.renderer.setStyle(
          this.textareaRef.nativeElement,
          'padding-right',
          padding,
        );
      });
  }

  protected onAiSuggestionReady() {
    this.aiSuggestionReady.emit();
  }
}
