import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';

/**
 * A set of controls or actions, typically displayed at the top of an interface.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-toolbar--docs Storybook Reference}
 */
@Component({
  selector: 'fin-toolbar',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './toolbar.component.html',
  styleUrl: './toolbar.component.scss',
  host: {
    class: 'fin-toolbar',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinToolbarComponent {}
