:host {
  @apply fin-flex;
  @apply fin-justify-between;
  @apply fin-items-center;
  @apply fin-w-full;
  @apply fin-relative;

  &:before {
    content: ' ';
    display: block;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 0;
    height: 1px;
    background-color: theme('colors.color-border-default-primary');
  }
  ::ng-deep fin-tabs {
    .mat-mdc-tab-label-container {
      border-bottom: 0;
      background: none !important;
    }
    .mdc-tab-indicator__content {
      bottom: 0 !important;
    }
  }
}
