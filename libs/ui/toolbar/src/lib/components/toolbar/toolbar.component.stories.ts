import { CommonModule } from '@angular/common';
import { FormControl } from '@angular/forms';
import { FinSlideToggleModule } from '@fincloud/ui/slide-toggle';
import { FinTabsModule } from '@fincloud/ui/tabs';
import { moduleMetadata, type Meta, type StoryObj } from '@storybook/angular';
import { FinToolbarModule } from '../../toolbar.module';
import { FinToolbarComponent } from './toolbar.component';

const meta: Meta = {
  component: FinToolbarComponent,
  title: 'Components/Toolbar',
  parameters: {
    docs: {
      description: {
        component: '`import { FinToolbarModule } from "@fincloud/ui/toolbar"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=25971-77179&t=VZUvum0VGVbc7R3u-4',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        FinToolbarModule,
        FinTabsModule,
        FinSlideToggleModule,
      ],
    }),
  ],
};
export default meta;
type Story = StoryObj<FinToolbarComponent & { finToolbarSuffix: string }>;

export const Primary: Story = {
  argTypes: {
    finToolbarSuffix: {
      description: 'Toolbar suffix',
      defaultValue: { summary: 'attribute' },
    },
  },
  render: () => ({
    props: { formControl: new FormControl(false) },
    template: `
      <div class="fin-w-[80rem]">
        <fin-toolbar>
          <fin-tabs #finTabs type="secondary">
            <fin-tab>
              <ng-template finTabLabel>Tab 1</ng-template>
            </fin-tab>

            <fin-tab>
              <ng-template finTabLabel>Tab 2</ng-template>
            </fin-tab>
          </fin-tabs>

          <ng-container finToolbarSuffix>
            <fin-slide-toggle
              [formControl]="formControl"
              label="Edit mode"
            ></fin-slide-toggle>
          </ng-container>
        </fin-toolbar>
      </div>
    `,
  }),
};
