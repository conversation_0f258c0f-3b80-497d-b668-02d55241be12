import { Directive, Input } from '@angular/core';
import { FinSize } from '@fincloud/ui/types';

@Directive({
  // eslint-disable-next-line @angular-eslint/directive-selector
  selector: 'fin-footer',
  standalone: true,
  host: {
    '[class]': 'footerClasses',
  },
})
export class FinFooterDirective {
  /** Size of the footer. */
  @Input() size: FinSize.S | FinSize.M = FinSize.S;

  protected get footerClasses(): string {
    return `fin-footer fin-footer-${this.size} fin-hf-common fin-hf-common-${this.size}`;
  }
}
