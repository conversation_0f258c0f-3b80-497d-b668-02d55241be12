import { Directive, Input } from '@angular/core';
import { FinSize } from '@fincloud/ui/types';

@Directive({
  // eslint-disable-next-line @angular-eslint/directive-selector
  selector: 'fin-header',
  standalone: true,
  host: {
    '[class]': 'headerClasses',
  },
})
export class FinHeaderDirective {
  /** Size of the header. */
  @Input() size: FinSize.S | FinSize.M = FinSize.S;

  protected get headerClasses(): string {
    return `fin-header fin-header-${this.size} fin-hf-common fin-hf-common-${this.size}`;
  }
}
