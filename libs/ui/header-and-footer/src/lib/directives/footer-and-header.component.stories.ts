import { CommonModule } from '@angular/common';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';
import {
  componentWrapperDecorator,
  Meta,
  moduleMetadata,
  StoryObj,
} from '@storybook/angular';
import { FinHeaderAndFooterModule } from '../header-and-footer.module';

const meta: Meta = {
  title: 'Components/Header & Footer',
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        FinHeaderAndFooterModule,
        FinButtonModule,
        FinIconModule,
      ],
    }),
    componentWrapperDecorator(
      (story) => `<div class="fin-w-[900px]">${story}</div>`,
    ),
  ],
  parameters: {
    backgrounds: {
      default: 'dark',
      values: [{ name: 'dark', value: '#ececec' }],
    },
    docs: {
      description: {
        component:
          '`import { FinHeaderAndFooterModule } from "@fincloud/ui/header-and-footer"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=31207-49705&t=U2goQrJw18GFJtJt-4',
    },
  },
};
export default meta;

type Story = StoryObj;

export const SSize: Story = {
  args: {
    size: FinSize.S,
  },
  argTypes: {
    size: {
      description: 'Size of the component.',
      options: [FinSize.S, FinSize.M],
      control: { type: 'select' },
      table: {
        category: 'Header & Footer inputs',
        defaultValue: { summary: 'FinSize.S' },
      },
    },
  },
  render: (args) => ({
    props: args,
    template: `
      <fin-header [size]="size">
        Header

        <button fin-button-action size="l">
          <fin-icon name="close"></fin-icon>
        </button>
      </fin-header>

      <div class="fin-py-[2rem]">Content...</div>

      <fin-footer [size]="size">
        <button fin-button appearance="secondary">
          Close
        </button>

        <button fin-button appearance="primary">Action</button>
      </fin-footer>
      `,
  }),
};

export const MSize: Story = {
  args: {
    ...SSize.args,
    size: FinSize.M,
  },
  argTypes: {
    ...SSize.argTypes,
  },
  render: (args) => ({
    props: args,
    template: `
      <fin-header [size]="size">
        Header

        <button fin-button-action size="l">
          <fin-icon name="close"></fin-icon>
        </button>
      </fin-header>

      <div class="fin-py-[2rem]">Content...</div>

      <fin-footer [size]="size">
        <button fin-button appearance="secondary">
          Close
        </button>

        <div class="fin-flex fin-justify-between fin-gap-[1rem]">
          <button fin-button appearance="primary">Action</button>        
          <button fin-button appearance="primary">Action</button>
        </div>
      </fin-footer>
      `,
  }),
};
