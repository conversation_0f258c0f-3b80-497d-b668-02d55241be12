#!/usr/bin/env node

const shell = require('shelljs');

// Check if the 'code' command is available
if (!shell.which('code')) {
  shell.echo(
    'Warning: VS Code command line tool is not installed. Skipping extension installation.',
  );
  shell.exit(0); // Exit without error
}
// Navigate to snippets directory
shell.cd('snippets');

// Rename package.json.sample to package.json
if (shell.mv('package.json.sample', 'package.json').code !== 0) {
  shell.echo('Error: Failed to rename package.json.sample to package.json');
  shell.exit(1);
}

// Package the extension
if (shell.exec('npx vsce package').code !== 0) {
  shell.echo('Error: vsce package failed');
  shell.exit(1);
}

// Find the generated .vsix file
const vsixFiles = shell.ls('*.vsix');
if (vsixFiles.length === 0) {
  shell.echo('Error: No .vsix file found');
  shell.exit(1);
}

const vsixFile = vsixFiles[0];

// Install the extension
if (shell.exec(`code --install-extension ${vsixFile}`).code !== 0) {
  shell.echo('Error: Failed to install extension');
  shell.exit(1);
}

shell.echo('Extension installed successfully');
