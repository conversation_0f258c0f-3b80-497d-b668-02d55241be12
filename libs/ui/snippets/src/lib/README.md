# Fincloud UI Snippets

A collection of code snippets for [@fincloud/ui](https://lib-ui.neoshare.dev) components, designed to enhance your development workflow in Visual Studio Code.

## Features

This extension includes snippets for the following components:

- **Action Menu Component** (`fin-action-menu`): Snippet for using the action menu component.
- **Badge Icon Component** (`fin-badge-icon`): Snippet for using the badge icon component.
- **Breadcrumb Component** (`fin-breadcrumb`): Snippet for using the breadcrumb component.
- **Button Components**:
  - <PERSON> Button (`fin-button`)
  - <PERSON> <PERSON>ton with Options (`fin-button-full`)
  - <PERSON><PERSON> with Loader (`fin-button-loader`)
  - <PERSON><PERSON> (`fin-button-icon`)
  - <PERSON> (`fin-button-link`)
  - <PERSON>ab <PERSON> (`fin-button-fab`)
- **Chart Components**:
  - Area Chart (`fin-area-chart`)
  - Bar Chart (`fin-bar-chart`)
  - Doughnut Chart (`fin-doughnut-chart`)
- **And many more...**

_For a complete list of snippets, see the **Snippets** section below._

## Usage

1. **Install the Extension**:

   - Download the `.vsix` file or install it directly from the VS Code Marketplace (if published there).
   - In VS Code, press `Ctrl+Shift+X` (or `⇧⌘X` on Mac) to open the Extensions view.
   - Click on the three dots `...` in the top-right corner and select **Install from VSIX...**.
   - Select the downloaded `.vsix` file to install.

2. **Use the Snippets**:
   - Open an HTML file in VS Code.
   - Start typing the snippet prefix (e.g., `fin-`).
   - Select the desired snippet from the IntelliSense suggestions or press `Tab` to insert.

## Snippets

Below is a list of available snippets along with their prefixes and descriptions:

| Prefix                     | Description                                                |
| -------------------------- | ---------------------------------------------------------- |
| `fin-action-menu`          | Snippet for using the action menu component                |
| `fin-badge-icon`           | Snippet for using the badge icon component                 |
| `fin-badge-indicator`      | Snippet for using the badge indicator component            |
| `fin-badge-status`         | Snippet for using the badge status component               |
| `fin-breadcrumb`           | Snippet for using the breadcrumb component                 |
| `fin-button`               | Snippet for using the button component without options     |
| `fin-button-full`          | Snippet for using the button component with all options    |
| `fin-button-loader`        | Snippet for using the button component with loader         |
| `fin-button-icon`          | Snippet for using the button icon component                |
| `fin-button-link`          | Snippet for using the button link component                |
| `fin-button-fab`           | Snippet for using the button fab component                 |
| `fin-card-label`           | Snippet for using the card label component                 |
| `fin-area-chart`           | Snippet for using the area chart component                 |
| `fin-bar-chart`            | Snippet for using the bar chart component                  |
| `fin-doughnut-chart`       | Snippet for using the doughnut chart component             |
| `fin-checkbox`             | Snippet for using the checkbox component                   |
| `fin-container`            | Snippet for using the container directive                  |
| `fin-dropdown`             | Snippet for using the dropdown component                   |
| `fin-dropdown-error`       | Snippet for using the dropdown component with field error  |
| `fin-accordion`            | Snippet for using the accordion component                  |
| `fin-filter-tabs`          | Snippet for using the filter tabs component                |
| `fin-icon`                 | Snippet for using the icon component                       |
| `fin-input`                | Snippet for using the input component                      |
| `fin-input-error`          | Snippet for using the input component with field error     |
| `fin-field-error`          | Snippet for using the field error component                |
| `fin-loader`               | Snippet for using the loader component                     |
| `fin-paginator`            | Snippet for using the paginator component                  |
| `fin-percentage-loader`    | Snippet for using the percentage loader component          |
| `fin-progress-bar`         | Snippet for using the progress bar component               |
| `fin-radio-button`         | Snippet for using the radio button component               |
| `fin-scrollbar`            | Snippet for using the scrollbar component                  |
| `fin-horizontal-separator` | Snippet for using the horizontal separator directive       |
| `fin-vertical-separator`   | Snippet for using the vertical separator directive         |
| `fin-side-panel`           | Snippet for using the side-panel container component       |
| `fin-slide-toggle`         | Snippet for using the slide toggle component               |
| `fin-split-button`         | Snippet for using the split button component               |
| `fin-switch-toggle`        | Snippet for using the switch toggle component              |
| `fin-table`                | Snippet for using the table component                      |
| `fin-table-full`           | Snippet for using the table component with all options     |
| `fin-tabs`                 | Snippet for using the tabs component with dynamic tabs     |
| `fin-text-area`            | Snippet for using the text area component with action menu |
| `fin-text-area-full`       | Snippet for using the text area component with all options |
| `fin-warning-message`      | Snippet for using the warning message component            |
