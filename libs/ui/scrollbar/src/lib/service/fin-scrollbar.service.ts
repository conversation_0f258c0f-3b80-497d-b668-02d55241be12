import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';

@Injectable(
  { providedIn: 'root' }, //
)
export class FinScrollbarService {
  private disableSubject$$ = new Subject<boolean>();
  /**
   * An observable that emits the current state of the scrollbar (enabled or disabled).
   */
  disabled$: Observable<boolean> = this.disableSubject$$.asObservable();

  /** Disables the scrollbar. */
  disable(): void {
    this.disableSubject$$.next(true);
  }

  /** Enable the scrollbar. */
  enable(): void {
    this.disableSubject$$.next(false);
  }
}
