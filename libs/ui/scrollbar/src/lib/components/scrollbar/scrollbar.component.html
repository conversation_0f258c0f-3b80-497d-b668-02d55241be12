<ng-scrollbar
  class="fin-h-full fin-relative"
  cdkScrollable
  (reachedTop)="reachTop()"
  (reachedBottom)="reachBottom()"
  [reachedTopOffset]="reachedTopOffset"
  [reachedBottomOffset]="reachedBottomOffset"
  [disableReached]="!enableInfinityScroll"
  [visibility]="alwaysVisible ? 'native' : 'hover'"
  (afterInit)="onAfterInit()"
  (afterUpdate)="onAfterUpdate()"
  (finObserveResize)="onResize()"
  [finObserveResizeDebounce]="300"
  [disableInteraction]="(getIsDisabled$ | async) || disabled"
>
  <ng-content></ng-content>
</ng-scrollbar>
