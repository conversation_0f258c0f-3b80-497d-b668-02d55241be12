import { Directive, ElementRef, OnInit } from '@angular/core';
import { FinScrollbarService } from '../service/fin-scrollbar.service';

@Directive({
  selector: '[finPreventScroll]',
  standalone: true,
})
export class FinPreventScrollDirective implements OnInit {
  constructor(
    private elementRef: ElementRef,
    private finScrollbarService: FinScrollbarService,
  ) {}

  ngOnInit(): void {
    this.finScrollbarService.disabled$.subscribe((disabled) => {
      if (disabled) {
        this.elementRef.nativeElement.addEventListener(
          'wheel',
          this.preventScroll,
          {
            passive: false,
          },
        );
      } else {
        this.elementRef.nativeElement.removeEventListener(
          'wheel',
          this.preventScroll,
        );
      }
    });
  }
  /**
   * Prevents the default scroll behavior on the element.
   * @param event The scroll event to prevent.
   */
  private preventScroll(event: Event) {
    event.preventDefault();
    event.stopImmediatePropagation();
    return false;
  }
}
