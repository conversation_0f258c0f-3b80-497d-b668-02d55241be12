import type { <PERSON>a, StoryObj } from '@storybook/angular';
import { FinParticipantType } from '../../enums/fin-participant-type';
import { FinAvatarParticipantsComponent } from './avatar-participants.component';

const meta: Meta<FinAvatarParticipantsComponent> = {
  component: FinAvatarParticipantsComponent,
  title: 'Components/Avatars/Avatar Participants',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinAvatarParticipantsModule } from "@fincloud/ui/avatar-participants"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-WIP?type=design&node-id=6528-194&mode=dev',
    },
  },
};
export default meta;
type Story = StoryObj<FinAvatarParticipantsComponent>;

export const Participant: Story = {
  argTypes: {
    participantType: {
      options: Object.values(FinParticipantType),
      control: { type: 'select' },
    },
    showType: {
      options: [true, false],
      control: { type: 'radio' },
    },
    participantsTypes: {
      table: {
        disable: true,
      },
    },
  },
  args: {
    participantType: FinParticipantType.PARTICIPANT,
    showType: true,
  },
};

export const Leader: Story = {
  argTypes: {
    ...Participant.argTypes,
  },
  args: {
    ...Participant.args,
    participantType: FinParticipantType.LEADER,
  },
  parameters: {
    ...Participant.parameters,
  },
};
