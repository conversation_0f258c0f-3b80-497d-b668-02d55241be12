import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FinParticipantType } from '../../enums/fin-participant-type';

/**
 * Shows a participant avatars.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-avatar-participants--docs Storybook Reference}
 */
@Component({
  selector: 'fin-avatar-participants',
  standalone: true,
  imports: [],
  templateUrl: './avatar-participants.component.html',
  styleUrl: './avatar-participants.component.scss',
  host: {
    class: 'fin-avatar-participants',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinAvatarParticipantsComponent {
  @Input() participantType:
    | FinParticipantType.LEADER
    | FinParticipantType.PARTICIPANT = FinParticipantType.PARTICIPANT;
  @Input() showType = false;

  participantsTypes = FinParticipantType;
}
