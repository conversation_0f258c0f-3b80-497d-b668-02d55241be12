{"name": "ui", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/ui/src", "prefix": "fin", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"tsConfig": "libs/ui/tsconfig.lib.json", "project": "libs/ui/ng-package.json", "tailwindConfig": "libs/ui/tailwind.config.js"}, "configurations": {"production": {"tsConfig": "libs/ui/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/ui/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "build-lib": {"executor": "nx:run-commands", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "configurations": {"production": {"commands": ["nx run ui:build:production", "tailwindcss -c {projectRoot}/tailwind.config.js -i ./{projectRoot}/styles/src/lib/base.css -o ./dist/{projectRoot}/styles/base.css -m"]}, "development": {"commands": ["nx run ui:build:development", "tailwindcss -c {projectRoot}/tailwind.config.js -i ./{projectRoot}/styles/src/lib/base.css -o ./dist/{projectRoot}/styles/base.css"]}}, "defaultConfiguration": "production"}, "prepare-publish": {"executor": "nx:run-commands", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "configurations": {"production": {"commands": ["rm -rf dist/{projectRoot}", "nx run ui:build-lib:production"], "parallel": false}, "development": {"commands": ["rm -rf dist/{projectRoot}", "nx run ui:build-lib:development"], "parallel": false}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/ui/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}, "storybook": {"executor": "@storybook/angular:start-storybook", "options": {"port": 4400, "configDir": "libs/ui/.storybook", "browserTarget": "ui:build-storybook", "compodoc": true, "compodocArgs": ["-e", "json", "-d", "libs/ui", "--disablePrivate", "--disableProtected", "--disableInternal", "--disable<PERSON>ifeCycle<PERSON><PERSON><PERSON>", "--disableSourceCode"], "assets": [{"input": "./libs/ui/assets", "glob": "**", "output": "assets"}]}, "configurations": {"ci": {"quiet": true}}}, "build-storybook": {"executor": "@storybook/angular:build-storybook", "outputs": ["{options.outputDir}"], "options": {"outputDir": "dist/storybook/ui", "configDir": "libs/ui/.storybook", "browserTarget": "ui:build-storybook", "compodoc": true, "compodocArgs": ["-e", "json", "-d", "libs/ui", "--disable<PERSON>ifeCycle<PERSON><PERSON><PERSON>", "--disablePrivate", "--disableProtected", "--disableInternal", "--disableSourceCode"], "styles": ["libs/utils/styles/src/lib/index.scss", "libs/ui/styles/src/lib/index.scss"], "assets": [{"input": "./libs/ui/assets", "glob": "**", "output": "assets"}]}, "configurations": {"ci": {"quiet": true}}}, "test-storybook": {"executor": "nx:run-commands", "options": {"command": "test-storybook -c libs/ui/.storybook --url=http://localhost:4400"}}, "static-storybook": {"executor": "@nx/web:file-server", "options": {"buildTarget": "ui:build-storybook", "staticFilePath": "dist/storybook/ui", "spa": true, "gzip": true}, "configurations": {"ci": {"buildTarget": "ui:build-storybook:ci"}}}}}