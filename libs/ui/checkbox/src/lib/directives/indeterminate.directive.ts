import { Directive, Input, On<PERSON><PERSON>roy, OnInit, inject } from '@angular/core';
import { FormControl } from '@angular/forms';
import { IgnoresNull } from '@fincloud/utils/decorators';
import { emitPerJsFrame } from '@fincloud/utils/rxjs-operators';
import { Subscription, merge } from 'rxjs';
import { FinCheckboxComponent } from '../components/checkbox.component';

/**
 * Directive to be used in conjunction with fin-checkbox
 * @param {childControls} Array of FormControls
 * */
@Directive({
  selector: 'fin-checkbox[finIndeterminate]',
  standalone: true,
})
export class FinIndeterminateDirective implements OnInit, OnDestroy {
  private subscriptions = new Subscription();
  private controlsSubscription: Subscription | undefined;
  private _childControls: FormControl[] = [];

  get childControls() {
    return this._childControls;
  }

  @IgnoresNull()
  @Input()
  set childControls(value: FormControl[]) {
    this.listenForIndeterminateState(value);
    this._childControls = value;
  }

  hostComponent = inject(FinCheckboxComponent);

  ngOnInit(): void {
    this.listenForValueChange();
  }

  private listenForIndeterminateState(controls: FormControl[]): void {
    const streams = controls.map((control) => control.valueChanges);

    if (this.controlsSubscription) {
      this.controlsSubscription.unsubscribe();
    }

    this.controlsSubscription = merge(...streams)
      .pipe(emitPerJsFrame())
      .subscribe(() => {
        if (
          this.childControls.some((children) => !!children.value) &&
          this.childControls.some((children) => !children.value)
        ) {
          // We have indeterminate state
          return this.hostComponent.setIndeterminateState(true);
        }

        this.hostComponent.setIndeterminateState(false);

        if (this.childControls.every((children) => !!children.value)) {
          return this.hostComponent.control.setValue(true);
        }

        return this.hostComponent.control.setValue(false);
      });

    this.subscriptions.add(this.controlsSubscription);
  }

  private listenForValueChange(): void {
    this.subscriptions.add(
      this.hostComponent.control.valueChanges.subscribe((value) => {
        this.childControls?.forEach((children) => {
          if (children.value !== value) {
            children.setValue(value);
          }
        });
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
}
