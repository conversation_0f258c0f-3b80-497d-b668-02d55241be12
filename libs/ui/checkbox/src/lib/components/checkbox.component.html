<mat-checkbox
  [formControl]="control"
  class="fin-checkbox fin-max-w-full"
  [ngClass]="['fin-checkbox-' + size]"
  [indeterminate]="indeterminate$ | async"
  (change)="onChange($event.checked)"
  [labelPosition]="labelPosition"
>
  @if (label) {
    <mat-label
      class="fin-w-full fin-inline-block fin-align-middle"
      finTruncateText
    >
      {{ label }}
    </mat-label>
  } @else {
    <ng-content select="[finCheckboxLabel]"></ng-content>
  }
</mat-checkbox>
