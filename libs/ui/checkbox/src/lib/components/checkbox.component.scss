:host {
  --mdc-checkbox-disabled-selected-icon-color: theme(
    'colors.color-background-secondary-subtle'
  );
  --mdc-checkbox-disabled-unselected-icon-color: theme(
    'colors.color-border-default-inactive'
  );
  --mdc-checkbox-selected-focus-icon-color: theme(
    'colors.color-background-secondary-strong'
  );
  --mdc-checkbox-selected-hover-icon-color: theme(
    'colors.color-background-dark-strong'
  );
  --mdc-checkbox-selected-icon-color: theme(
    'colors.color-background-secondary-strong'
  );
  --mdc-checkbox-selected-pressed-icon-color: theme(
    'colors.color-background-secondary-strong'
  );
  --mdc-checkbox-unselected-focus-icon-color: theme(
    'colors.color-border-default-primary'
  );
  --mdc-checkbox-unselected-hover-icon-color: theme(
    'colors.color-background-dark-strong'
  );

  --mdc-checkbox-unselected-icon-color: theme(
    'colors.color-border-default-primary'
  );

  --mdc-checkbox-unselected-pressed-icon-color: theme(
    'colors.color-background-secondary-strong'
  );
  --mdc-checkbox-selected-focus-state-layer-color: none;
  --mdc-checkbox-selected-hover-state-layer-color: none;
  --mdc-checkbox-selected-pressed-state-layer-color: none;
  --mdc-checkbox-unselected-focus-state-layer-color: none;
  --mdc-checkbox-unselected-hover-state-layer-color: none;
  --mdc-checkbox-unselected-pressed-state-layer-color: none;
  --mat-checkbox-disabled-label-color: theme(
    'colors.color-border-default-inactive'
  );

  ::ng-deep {
    .fin-checkbox.mat-mdc-checkbox {
      --mdc-form-field-label-text-color: theme(
        'colors.color-background-dark-strong'
      );

      .mdc-form-field--align-end .mdc-label {
        padding-inline-end: 1.2rem;
      }
      :not(.mdc-form-field--align-end) .mdc-label {
        padding-inline-start: 1.2rem;
      }
      .mat-mdc-checkbox-touch-target {
        width: var(--mdc-checkbox-state-layer-size);
        height: var(--mdc-checkbox-state-layer-size);
      }

      .mdc-form-field.mat-internal-form-field {
        @apply fin-w-full;
      }

      .mdc-checkbox {
        &__background {
          border: 0.1rem solid;
          border-radius: 0.4rem;
          top: 0;
          left: 0;
          width: var(--mdc-checkbox-state-layer-size);
          height: var(--mdc-checkbox-state-layer-size);
        }
        &__checkmark {
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }

      .mdc-label {
        @apply fin-w-full;
      }

      // FinSize M
      &.fin-checkbox-m {
        --mdc-checkbox-state-layer-size: theme('fontSize.text-heading-2-size');
        .mdc-label {
          @apply fin-text-body-2-moderate;
        }

        .mdc-checkbox {
          &__checkmark {
            width: 1.2rem;
            height: 1.2rem;
          }
          &__mixedmark {
            width: 1.2rem;
          }
        }
      }

      // FinSize S
      &.fin-checkbox-s {
        --mdc-checkbox-state-layer-size: theme('fontSize.text-body-1-size');

        .mdc-label {
          padding-left: 1rem;
          @apply fin-text-body-3-moderate;
        }
        .mdc-form-field {
          height: 1.6rem;
        }
        .mdc-checkbox {
          height: 1.6rem;
          &__checkmark {
            width: 0.8rem;
            height: 0.8rem;
          }
          &__mixedmark {
            width: 0.8rem;
          }
        }
      }
    }

    .mdc-checkbox
      .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate)
      ~ .mdc-checkbox__background {
      @apply fin-bg-color-background-neutral-minimal #{!important};
      @apply fin-border-color-border-default-primary #{!important};
    }

    .mdc-checkbox:hover
      .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate)
      ~ .mdc-checkbox__background {
      @apply fin-bg-color-hover-neutral #{!important};
      @apply fin-border-color-border-default-hover #{!important};
    }

    .mat-mdc-checkbox-checked .mdc-checkbox:hover .mdc-checkbox__background {
      @apply fin-border-color-background-dark-strong #{!important};
      @apply fin-bg-color-background-dark-strong #{!important};
    }

    .mdc-checkbox--disabled:not(.mat-mdc-checkbox-checked)
      .mdc-checkbox__background {
      @apply fin-bg-color-background-disabled #{!important};
    }
  }
}

::ng-deep fin-checkbox {
  .ng-touched.ng-invalid,
  .ng-dirty.ng-invalid {
    --mdc-checkbox-unselected-focus-icon-color: theme(
      'colors.color-border-default-error'
    );
    --mdc-checkbox-unselected-icon-color: theme(
      'colors.color-border-default-error'
    );
    --mdc-checkbox-unselected-hover-icon-color: theme(
      'colors.color-border-default-error'
    );

    .mdc-checkbox
      .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate)
      ~ .mdc-checkbox__background {
      @apply fin-border-color-border-default-error #{!important};
    }
    .mdc-checkbox:hover
      .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate)
      ~ .mdc-checkbox__background {
      @apply fin-bg-color-hover-neutral #{!important};
    }
  }
}
