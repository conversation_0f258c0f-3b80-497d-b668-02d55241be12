import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  forwardRef,
} from '@angular/core';
import { NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinSize, LabelPosition } from '@fincloud/ui/types';
import { FinAngularMaterialModule } from '@fincloud/utils/angular-material';
import { FinControlValueAccessor } from '@fincloud/utils/control-value-accessor';
import { BehaviorSubject, Observable } from 'rxjs';

/**
 * A component that allows users to select one or more options from a set.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-checkbox--docs Storybook Reference}
 */
@Component({
  selector: 'fin-checkbox',
  standalone: true,
  imports: [
    CommonModule,
    MatCheckboxModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    FinAngularMaterialModule,
    FinTruncateTextModule,
  ],
  templateUrl: './checkbox.component.html',
  styleUrl: './checkbox.component.scss',
  host: {
    class: 'fin-checkbox',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FinCheckboxComponent),
      multi: true,
    },
  ],
})
export class FinCheckboxComponent extends FinControlValueAccessor {
  private indeterminate$$ = new BehaviorSubject<boolean>(false);
  /** Label of the checkbox. */
  @Input() label = '';

  /** Size of the checkbox. */
  @Input() size: FinSize.S | FinSize.M = FinSize.M;

  /** Whether the label should appear after or before the checkbox. */
  @Input() labelPosition: LabelPosition.AFTER | LabelPosition.BEFORE =
    LabelPosition.AFTER;

  /** Event emitted when the checkbox's checked value changes. `EventEmitter<boolean>()` */
  @Output() changed = new EventEmitter<boolean>();

  protected indeterminate$: Observable<boolean> =
    this.indeterminate$$.asObservable();

  /** Whether the checkbox is indeterminate. This is also known as "mixed" mode and can be used to represent a checkbox with three states */
  setIndeterminateState(value: boolean): void {
    this.indeterminate$$.next(value);
  }

  protected onChange(checked: boolean) {
    this.changed.emit(checked);
  }
}
