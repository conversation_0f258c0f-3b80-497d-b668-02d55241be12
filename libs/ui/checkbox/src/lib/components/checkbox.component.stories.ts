import { FormControl, Validators } from '@angular/forms';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinSize, LabelPosition } from '@fincloud/ui/types';
import {
  moduleMetadata,
  type Args,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { of } from 'rxjs';
import { FinCheckboxComponent } from './checkbox.component';

const meta: Meta<FinCheckboxComponent> = {
  component: FinCheckboxComponent,
  title: 'Fields/Checkbox',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinCheckboxModule } from "@fincloud/ui/checkbox"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6531-6023&m=dev',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [FinTruncateTextModule],
    }),
  ],
};
export default meta;

type Story = StoryObj<
  FinCheckboxComponent & {
    finCheckboxLabel: string;
  }
>;

const createFormControlProps = (
  args: Args,
  value = true,
  disabled = false,
  indeterminate = false,
) => {
  const formControl = new FormControl({
    value,
    disabled,
  });
  return {
    ...args,
    indeterminate$: of(indeterminate),
    formControl,
  };
};

export const Checkbox: Story = {
  args: {
    size: FinSize.M,
    label: 'Option label',
  },
  argTypes: {
    size: {
      control: { type: 'select' },
      options: [FinSize.S, FinSize.M],
    },
    labelPosition: {
      control: { type: 'select' },
      options: Object.values(LabelPosition),
    },
    setIndeterminateState: {
      control: false,
    },
    finCheckboxLabel: {
      description:
        'Place the content to be rendered in the place of the label.',
      table: {
        category: 'Templates',
      },
    },
  },
  render: (args) => ({
    props: createFormControlProps(args),
  }),
};

export const Disabled: Story = {
  argTypes: {
    ...Checkbox.argTypes,
  },
  args: {
    ...Checkbox.args,
  },
  render: (args) => ({
    props: createFormControlProps(args, true, true),
  }),
  parameters: {
    ...Checkbox.parameters,
  },
};

export const WithoutLabel: Story = {
  argTypes: {
    ...Checkbox.argTypes,
  },
  args: {
    ...Checkbox.args,
    label: '',
  },
  render: (args) => ({
    props: createFormControlProps(args),
  }),
  parameters: {
    ...Checkbox.parameters,
  },
};

export const WithoutLabelUnchecked: Story = {
  argTypes: {
    ...Checkbox.argTypes,
  },
  args: {
    ...Checkbox.args,
    label: '',
  },
  render: (args) => ({
    props: createFormControlProps(args, false),
  }),
  parameters: {
    ...Checkbox.parameters,
  },
};

export const WithoutLabelUncheckedDisabled: Story = {
  argTypes: {
    ...Checkbox.argTypes,
  },
  args: {
    ...Checkbox.args,
    label: '',
  },
  render: (args) => ({
    props: createFormControlProps(args, false, true),
  }),
  parameters: {
    ...Checkbox.parameters,
  },
};

export const Indeterminate: Story = {
  argTypes: {
    ...Checkbox.argTypes,
  },
  args: {
    ...Checkbox.args,
  },
  render: (args) => ({
    props: createFormControlProps(args, true, false, true),
  }),
  parameters: {
    ...Checkbox.parameters,
  },
};

export const IndeterminateDisabled: Story = {
  argTypes: {
    ...Checkbox.argTypes,
  },
  args: {
    ...Checkbox.args,
  },
  render: (args) => ({
    props: createFormControlProps(args, true, true, true),
  }),
  parameters: {
    ...Checkbox.parameters,
  },
};

export const CustomTruncatedLabel: Story = {
  argTypes: {
    ...Checkbox.argTypes,
  },
  args: {
    ...Checkbox.args,
  },
  render: () => {
    return {
      template: `
          <fin-checkbox
            [formControl]="formControl"
            [size]="'m'"
            (changed)="changed($event)"
          >
            <ng-container finCheckboxLabel>
              <label
                class="fin-w-[16rem] fin-inline-block fin-align-middle"
                finTruncateText
              >
                Long label to be truncated
              </label>
            </ng-container>
          </fin-checkbox>`,
    };
  },
};

export const SmallSize: Story = {
  argTypes: {
    ...Checkbox.argTypes,
  },
  args: {
    ...Checkbox.args,
    size: FinSize.S,
  },
  render: (args) => ({
    props: createFormControlProps(args),
  }),
  parameters: {
    ...Checkbox.parameters,
  },
};

export const Invalid: Story = {
  argTypes: {
    ...Checkbox.argTypes,
  },
  args: {
    ...Checkbox.args,
  },
  render: (args) => {
    const formControl = new FormControl('', Validators.required);
    formControl.markAsTouched();

    return {
      props: {
        ...createFormControlProps(args, false, false),
        formControl,
      },
    };
  },
  parameters: {
    ...Checkbox.parameters,
  },
};

export const InvalidSmall: Story = {
  argTypes: {
    ...Checkbox.argTypes,
  },
  args: {
    ...Checkbox.args,
    size: FinSize.S,
  },
  render: (args) => {
    const formControl = new FormControl('', Validators.required);
    formControl.markAsTouched();

    return {
      props: {
        ...createFormControlProps(args, false, false),
        formControl,
      },
    };
  },
  parameters: {
    ...Checkbox.parameters,
  },
};
