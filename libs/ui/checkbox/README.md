# Checkbox Component

A comprehensive checkbox component for selecting one or more options from a set. Supports form integration, indeterminate state, custom labels, and accessibility features.

## Installation

```bash
npm install @fincloud/ui
```

```typescript
import { FinCheckboxModule } from '@fincloud/ui/checkbox';

@Component({
  imports: [FinCheckboxModule],
  // ...
})
export class YourComponent {}
```

## Basic Usage

### Simple Checkbox

```html
<fin-checkbox label="Accept terms and conditions" [formControl]="termsControl"> </fin-checkbox>
```

```typescript
import { Component } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { FinCheckboxModule } from '@fincloud/ui/checkbox';

@Component({
  selector: 'app-example',
  imports: [FinCheckboxModule, ReactiveFormsModule],
  template: ` <fin-checkbox label="Accept terms and conditions" [formControl]="termsControl"> </fin-checkbox> `,
})
export class ExampleComponent {
  termsControl = new FormControl(false);
}
```

## API Reference

### FinCheckboxComponent

#### Selector

- `fin-checkbox`

#### Inputs

| Name            | Type                                          | Default               | Description                                    |
| --------------- | --------------------------------------------- | --------------------- | ---------------------------------------------- |
| `label`         | `string`                                      | `''`                  | Label text for the checkbox                    |
| `size`          | `FinSize.S \| FinSize.M`                      | `FinSize.M`           | Size of the checkbox                           |
| `labelPosition` | `LabelPosition.AFTER \| LabelPosition.BEFORE` | `LabelPosition.AFTER` | Position of the label relative to the checkbox |

#### Outputs

| Name      | Type                    | Description                                       |
| --------- | ----------------------- | ------------------------------------------------- |
| `changed` | `EventEmitter<boolean>` | Emitted when the checkbox's checked value changes |

#### Methods

| Name                      | Parameters       | Return Type | Description                                      |
| ------------------------- | ---------------- | ----------- | ------------------------------------------------ |
| `setIndeterminateState()` | `value: boolean` | `void`      | Sets the checkbox to indeterminate (mixed) state |

#### Content Projection

| Slot                 | Description                                   |
| -------------------- | --------------------------------------------- |
| `[finCheckboxLabel]` | Custom label content with advanced formatting |

### FinIndeterminateDirective

Directive for managing indeterminate state with child checkboxes.

#### Selector

- `fin-checkbox[finIndeterminate]`

#### Inputs

| Name            | Type            | Description                            |
| --------------- | --------------- | -------------------------------------- |
| `childControls` | `FormControl[]` | Array of child form controls to manage |

#### Usage

```html
<fin-checkbox [finIndeterminate] [childControls]="childCheckboxes" label="Select All"> </fin-checkbox>
```

## Configuration Options

### Size Options

Available sizes from `@fincloud/ui/types`:

- `FinSize.S` - Small checkbox (16px)
- `FinSize.M` - Medium checkbox (24px, default)

### Label Position Options

Available positions from `@fincloud/ui/types`:

- `LabelPosition.AFTER` - Label appears after the checkbox (default)
- `LabelPosition.BEFORE` - Label appears before the checkbox

### States

The checkbox supports three states:

- **Checked** - User has selected the option
- **Unchecked** - User has not selected the option
- **Indeterminate** - Mixed state, typically used for "select all" scenarios

## Advanced Examples

### Custom Label with Formatting

```html
<fin-checkbox [formControl]="customControl">
  <ng-container finCheckboxLabel>
    <label class="fin-w-[20rem] fin-inline-block" finTruncateText>
      <strong>Important:</strong> I agree to the
      <a href="/terms" class="fin-text-blue-600">terms and conditions</a>
      and <a href="/privacy" class="fin-text-blue-600">privacy policy</a>
    </label>
  </ng-container>
</fin-checkbox>
```

### Indeterminate State with Child Checkboxes

```html
<div class="fin-space-y-4">
  <!-- Parent checkbox with indeterminate state -->
  <fin-checkbox [finIndeterminate] [childControls]="childControls" label="Select All Features" [formControl]="parentControl"> </fin-checkbox>

  <!-- Child checkboxes -->
  <div class="fin-ml-8 fin-space-y-2">
    <fin-checkbox label="Feature A" [formControl]="featureAControl"> </fin-checkbox>

    <fin-checkbox label="Feature B" [formControl]="featureBControl"> </fin-checkbox>

    <fin-checkbox label="Feature C" [formControl]="featureCControl"> </fin-checkbox>
  </div>
</div>
```

```typescript
export class IndeterminateExampleComponent {
  parentControl = new FormControl(false);
  featureAControl = new FormControl(false);
  featureBControl = new FormControl(false);
  featureCControl = new FormControl(false);

  childControls = [this.featureAControl, this.featureBControl, this.featureCControl];
}
```

### Different Sizes

```html
<div class="fin-space-y-4">
  <fin-checkbox label="Small checkbox" [size]="'s'" [formControl]="smallControl"> </fin-checkbox>

  <fin-checkbox label="Medium checkbox (default)" [size]="'m'" [formControl]="mediumControl"> </fin-checkbox>
</div>
```

### Label Positioning

```html
<div class="fin-space-y-4">
  <fin-checkbox label="Label after checkbox (default)" [labelPosition]="'after'" [formControl]="afterControl"> </fin-checkbox>

  <fin-checkbox label="Label before checkbox" [labelPosition]="'before'" [formControl]="beforeControl"> </fin-checkbox>
</div>
```

### Event Handling

```html
<fin-checkbox label="Notify on change" [formControl]="eventControl" (changed)="onCheckboxChange($event)"> </fin-checkbox>
```

```typescript
export class EventHandlingComponent {
  eventControl = new FormControl(false);

  onCheckboxChange(checked: boolean): void {
    console.log('Checkbox changed:', checked);

    if (checked) {
      // Handle checked state
      this.enableFeature();
    } else {
      // Handle unchecked state
      this.disableFeature();
    }
  }

  private enableFeature(): void {
    // Implementation
  }

  private disableFeature(): void {
    // Implementation
  }
}
```

### Form Validation

```html
<form [formGroup]="validationForm">
  <fin-checkbox label="I accept the terms and conditions" formControlName="terms"> </fin-checkbox>

  <div *ngIf="validationForm.get('terms')?.invalid && validationForm.get('terms')?.touched" class="fin-text-red-600 fin-text-sm fin-mt-1">You must accept the terms and conditions</div>

  <button type="submit" [disabled]="validationForm.invalid" class="fin-mt-4">Submit</button>
</form>
```

```typescript
import { Validators } from '@angular/forms';

export class ValidationComponent {
  validationForm = new FormGroup({
    terms: new FormControl(false, [Validators.requiredTrue]),
  });

  onSubmit(): void {
    if (this.validationForm.valid) {
      console.log('Form submitted');
    }
  }
}
```

## Best Practices

### Do's

- ✅ Use reactive forms with FormControl for proper validation and state management
- ✅ Provide clear, descriptive labels that explain what the checkbox does
- ✅ Use indeterminate state for "select all" functionality with child checkboxes
- ✅ Handle change events for dynamic behavior based on checkbox state
- ✅ Use appropriate sizing based on the context and available space
- ✅ Implement proper validation for required checkboxes
- ✅ Use custom labels for complex formatting needs
- ✅ Group related checkboxes logically

### Don'ts

- ❌ Don't use template-driven forms for complex validation scenarios
- ❌ Don't forget to provide labels for accessibility
- ❌ Don't use vague labels like "Check this box"
- ❌ Don't ignore the indeterminate state for parent-child relationships
- ❌ Don't make checkboxes too small for touch interfaces
- ❌ Don't use checkboxes for mutually exclusive options (use radio buttons instead)

### Accessibility Guidelines

- Ensure all checkboxes have associated labels
- Use proper ARIA attributes for screen readers
- Support keyboard navigation (Space key to toggle)
- Provide sufficient color contrast for all states
- Use semantic HTML structure
- Include descriptive text for complex checkbox groups
- Ensure minimum touch target size of 44x44px for mobile

### Performance Tips

- Use OnPush change detection strategy (already implemented)
- Avoid frequent programmatic state changes
- Use trackBy functions for checkbox lists
- Consider lazy loading for large checkbox groups

## Troubleshooting

### Common Issues

#### Issue: Checkbox not responding to clicks

**Solution:** Ensure the FormControl is properly bound and the component is imported.

```typescript
// Correct setup
@Component({
  imports: [FinCheckboxModule, ReactiveFormsModule],
  // ...
})
export class MyComponent {
  checkboxControl = new FormControl(false);
}
```

#### Issue: Indeterminate state not working

**Solution:** Verify the finIndeterminate directive is applied and childControls are provided.

```html
<!-- Correct usage -->
<fin-checkbox [finIndeterminate] [childControls]="childControls" [formControl]="parentControl"> </fin-checkbox>
```

#### Issue: Custom label not displaying

**Solution:** Ensure the finCheckboxLabel directive is properly applied.

```html
<!-- Correct custom label -->
<fin-checkbox [formControl]="control">
  <ng-container finCheckboxLabel>
    <span>Custom label content</span>
  </ng-container>
</fin-checkbox>
```

#### Issue: Validation not working

**Solution:** Ensure proper validators are applied and form state is checked.

```typescript
// Correct validation setup
control = new FormControl(false, [Validators.requiredTrue]);

// Check validation state
get isInvalid() {
  return this.control.invalid && this.control.touched;
}
```

#### Issue: Styling not applying correctly

**Solution:** Ensure the FinCheckboxModule is imported and CSS classes are not overridden.

```typescript
@Component({
  imports: [FinCheckboxModule], // Required
  // ...
})
```

## Related Components

- [Radio](../radio/README.md) - For mutually exclusive single selection
- [Dropdown](../dropdown/README.md) - For selecting from a list of options
- [Input](../input/README.md) - For text input fields
- [Button](../button/README.md) - For action buttons in forms
- [Slide Toggle](../slide-toggle/README.md) - For on/off switches

## Storybook

View interactive examples and documentation in Storybook:

- [Checkbox Component](https://lib-ui.neoshare.dev/?path=/docs/fields-checkbox--docs)

## Changelog

See [CHANGELOG.md](../../CHANGELOG.md) for version history and breaking changes.

## Contributing

See [CONTRIBUTING.md](../../CONTRIBUTING.md) for guidelines on contributing to this component.

## License

This component is part of the @fincloud/ui library and is licensed under [MIT License](../../LICENSE).
