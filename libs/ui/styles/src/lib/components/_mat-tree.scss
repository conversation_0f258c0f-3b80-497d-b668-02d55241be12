.mat-tree {
  --mat-tree-node-min-height: auto;
  --mat-tree-container-background-color: transparent;
  --mat-tree-node-text-size: theme('fontSize.text-body-3-size');
  --mat-tree-node-text-color: theme('colors.color-text-primary');
  --mat-tree-node-text-weight: theme('fontWeight.text-body-3-moderate-weight');
}

.fin-tree-node.cdk-drag-preview {
  background: theme('colors.color-surface-primary');
  box-shadow: 0px 1px 8px 0px rgba(38, 40, 62, 0.15);
}
