.mat-mdc-menu-panel {
  max-width: fit-content !important;
  margin: 0.4rem 0;
  &.mat-mdc-elevation-specific {
    box-shadow: 0px 4px 10px 0px rgba(38, 40, 62, 0.16);
  }
  .mat-mdc-menu-content {
    --mat-menu-item-leading-spacing: 1rem;
    --mat-menu-item-trailing-spacing: 1rem;
    --mat-menu-item-with-icon-leading-spacing: 1rem;
    --mat-menu-item-with-icon-trailing-spacing: 1rem;
    --mat-menu-item-label-text-line-height: theme(
      'lineHeight.text-caption-1-line-height'
    );
    --mat-menu-item-spacing: 0;
    --mat-menu-item-icon-color: currentColor;
    --mat-menu-item-label-text-color: theme('colors.color-text-primary');
    font-family: theme('fontFamily.text-body-2-family');
    font-size: theme('fontSize.text-body-2-size');
    font-weight: theme('fontWeight.text-body-2-moderate-weight');
    line-height: theme('lineHeight.text-body-2-line-height');
    padding: 1.2rem;
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
    .mat-mdc-menu-item {
      &:hover:enabled {
        background-color: theme('colors.color-background-tertiary-subtle');
      }
      min-height: 4rem;
      margin-bottom: 0.2rem;
      display: flex;
      align-items: center;
      gap: 0.4rem;
      border-radius: 0.4rem;
      &[disabled] {
        opacity: 1;
        cursor: not-allowed;
        --mat-menu-item-icon-color: theme('colors.color-icons-disabled');
        --mat-menu-item-label-text-color: theme('colors.color-text-disabled');
      }
    }
  }
}
