.fin-toast.mdc-snackbar {
  min-width: 21rem;
  max-width: 61rem;

  .mat-mdc-progress-spinner {
    --mdc-circular-progress-active-indicator-color: theme(
      'colors.color-surface-primary'
    );
  }

  .mdc-snackbar__surface {
    min-width: auto;
    max-width: auto;
    width: 100%;
    border-radius: 9999px;
  }

  &.success {
    --mdc-snackbar-container-color: theme(
      'colors.color-background-success-strong'
    );
  }

  &.error {
    --mdc-snackbar-container-color: theme('colors.color-status-error');
  }

  &.info {
    --mdc-snackbar-container-color: theme('colors.color-status-neutral');
  }
}
