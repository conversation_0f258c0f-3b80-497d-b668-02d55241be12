label.fin-label {
  font-size: theme('fontSize.text-body-2-size');
  font-weight: theme('fontWeight.text-body-2-moderate-weight');
  line-height: theme('lineHeight.text-body-2-line-height');
  color: theme('colors.color-text-primary');
  vertical-align: middle;
  width: 100%;
  &:not(:empty):not(:has(mat-label)) {
    padding-bottom: 0.4rem;
  }
}

.mat-form-field-appearance-outline {
  --mat-form-field-container-height: theme(
    'lineHeight.text-heading-2-line-height'
  );
  --mat-form-field-container-text-size: theme('fontSize.text-body-2-size');
  --mat-form-field-container-text-weight: theme(
    'fontWeight.text-body-2-moderate-weight'
  );
  --mat-form-field-container-vertical-padding: 0.8rem;
  --mdc-outlined-text-field-disabled-input-text-color: theme(
    'colors.color-text-disabled'
  );
  --mdc-outlined-text-field-disabled-outline-color: theme(
    'colors.color-border-default-inactive'
  );
  --mdc-outlined-text-field-outline-color: theme(
    'colors.color-border-default-primary'
  );
  --mdc-outlined-text-field-focus-outline-color: theme(
    'colors.color-border-default-hover'
  );
  --mdc-outlined-text-field-hover-outline-color: theme(
    'colors.color-border-default-hover'
  );
  --mdc-outlined-text-field-focus-outline-width: 0.1rem;
  --mdc-outlined-text-field-input-text-color: theme(
    'colors.color-text-primary'
  );
  --mdc-outlined-text-field-input-text-placeholder-color: theme(
    'colors.color-text-tertiary'
  );
  --mdc-outlined-text-field-caret-color: theme('colors.color-text-primary');

  &.fin-field-readonly {
    cursor: not-allowed;
    .mat-mdc-input-element {
      cursor: not-allowed;
    }
    .mat-mdc-form-field-subscript-wrapper {
      cursor: default;
    }

    --mdc-outlined-text-field-outline-color: theme(
      'colors.color-border-default-primary'
    );
    --mdc-outlined-text-field-focus-outline-color: theme(
      'colors.color-border-default-primary'
    );
    --mdc-outlined-text-field-hover-outline-color: theme(
      'colors.color-border-default-primary'
    );

    --mdc-outlined-text-field-error-outline-color: theme(
      'colors.color-border-default-primary'
    );

    --mdc-outlined-text-field-error-hover-outline-color: theme(
      'colors.color-border-default-primary'
    );

    --mdc-outlined-text-field-error-focus-outline-color: theme(
      'colors.color-border-default-primary'
    );

    .mat-mdc-form-field-error {
      display: none;
    }
  }

  &.mat-mdc-form-field {
    --mat-mdc-form-field-floating-label-scale: 1;
    .mdc-text-field--outlined.mat-mdc-text-field-wrapper {
      padding-left: 1.6rem;
      padding-right: 1.2rem;
      .mat-mdc-form-field-icon-prefix,
      .mat-mdc-form-field-icon-suffix {
        padding: 0;
      }
    }
    .mat-mdc-form-field-infix {
      &:has(.fin-ai-suggestion-active) {
        overflow: hidden;
        display: grid;
        grid-template-areas: 'stack';
        .fin-ai-suggestion,
        > input,
        > .fin-scrollbar {
          grid-area: stack;
        }

        > input,
        textarea {
          color: transparent;
        }
      }
      width: 100%;
    }
    &.mat-form-field-disabled {
      .mdc-text-field--outlined.mdc-text-field--disabled {
        pointer-events: none;
        label {
          pointer-events: none;
        }
        .mat-mdc-form-field-infix {
          pointer-events: none;
          input,
          textarea {
            pointer-events: none;
          }
        }
      }
      &.fin-field-readonly {
        .mdc-text-field--outlined {
          .mdc-notched-outline__leading,
          .mdc-notched-outline__trailing,
          .mdc-notched-outline__notch {
            border-color: theme('colors.color-border-default-primary');
            background-color: theme('colors.color-background-light');
          }
          .mat-mdc-form-field-infix {
            .mat-mdc-form-field-input-control.mdc-text-field__input,
            .mat-mdc-select-value {
              color: theme('colors.color-text-primary');
            }
          }
        }
      }
    }
    &:not(.fin-field-readonly):not(.fin-field-warning):not(
        .mat-form-field-invalid
      ):not(.fin-field-success) {
      .mdc-text-field--outlined:hover:not(:focus-within) {
        .mdc-notched-outline__leading,
        .mdc-notched-outline__trailing,
        .mdc-notched-outline__notch {
          background-color: theme('colors.color-hover-neutral');
          border-color: theme('colors.color-border-default-primary') !important;
        }
      }
      .mdc-text-field--outlined
        .mdc-notched-outline
        .mdc-notched-outline__notch {
        border-left: none !important;
      }
    }
    .mat-mdc-select.mat-mdc-select-empty {
      .mat-mdc-select-placeholder {
        color: theme('colors.color-text-tertiary');
      }
    }
    .mdc-notched-outline--notched {
      z-index: 0;
    }
    .mdc-notched-outline {
      &__leading,
      &__notch,
      &__trailing {
        background-color: theme('colors.color-surface-secondary');
      }
      &__notch {
        border-top: 0.1rem solid;
        border-right: none;
        clip-path: inset(-9em -999em -9em -10em);
        width: 100% !important;
      }
    }
    &.fin-field-readonly {
      .mdc-notched-outline {
        &__leading,
        &__notch,
        &__trailing {
          background-color: theme('colors.color-surface-primary');
        }
      }
    }

    &.mat-form-field-disabled {
      .mdc-notched-outline {
        &__leading,
        &__notch,
        &__trailing {
          background-color: theme('colors.color-background-disabled');
        }
      }
    }

    .mat-mdc-input-element {
      font-weight: theme('fontWeight.text-heading-1-moderate-weight')
        var(--fontWeight-text-heading-1-moderate-weigh);
      &::placeholder {
        font-weight: theme('fontWeight.text-heading-2-moderate-weight');
      }

      &[type='password'] {
        font-weight: theme('fontWeight.text-heading-1-strong-weight');
      }
    }
  }

  &.ng-touched.ng-invalid {
    --mdc-outlined-text-field-error-outline-color: theme(
      'colors.color-border-default-error'
    );

    --mdc-outlined-text-field-error-hover-outline-color: theme(
      'colors.color-border-default-error'
    );

    --mdc-outlined-text-field-error-focus-outline-color: theme(
      'colors.color-border-default-error'
    );
  }

  &.fin-field-warning:not(.fin-field-readonly).ng-touched.ng-invalid {
    --mdc-outlined-text-field-error-outline-color: theme(
      'colors.color-border-default-warning'
    );

    --mdc-outlined-text-field-error-hover-outline-color: theme(
      'colors.color-border-default-warning'
    );

    --mdc-outlined-text-field-error-focus-outline-color: theme(
      'colors.color-border-default-warning'
    );
  }

  &.fin-field-success:not(.fin-field-readonly).ng-touched.ng-valid {
    --mdc-outlined-text-field-outline-color: theme(
      'colors.color-border-default-success'
    );
    --mdc-outlined-text-field-focus-outline-color: theme(
      'colors.color-border-default-success'
    );
    --mdc-outlined-text-field-hover-outline-color: theme(
      'colors.color-border-default-success'
    );
  }

  .mat-mdc-form-field-hint-wrapper,
  .mat-mdc-form-field-error-wrapper {
    div.mat-mdc-form-field-hint-spacer {
      display: none;
    }
    padding: 0;
    font-size: theme('fontSize.text-body-3-size');
    line-height: theme('lineHeight.text-body-3-line-height');
    color: theme('colors.color-status-neutral');
  }

  .mat-mdc-form-field-error {
    --mat-form-field-error-text-color: theme('colors.color-text-secondary');
  }

  &.fin-field-with-progress .mat-mdc-text-field-wrapper {
    margin-bottom: 0.3rem;
  }

  .fin-suffix,
  .fin-prefix {
    color: theme('colors.color-text-tertiary');
    font-size: theme('fontSize.text-body-2-size');
  }
  .fin-prefix:not(:empty) {
    padding-right: 0.8rem;
  }

  .fin-suffix:not(:empty) {
    padding-left: 0.8rem;
  }

  .mat-mdc-form-field-bottom-align.mat-mdc-form-field-hint::before {
    display: none;
  }

  .fin-field-message:has(.fin-message-body:empty) {
    display: none;
  }

  .fin-hint:not(:empty) {
    color: theme('colors.color-text-secondary');
    padding-top: 0.4rem;
    font-family: theme('fontFamily.text-body-3-family');
    font-weight: theme('fontWeight.text-body-3-moderate-weight');
    font-size: theme('fontSize.text-body-3-size');
    line-height: theme('lineHeight.text-body-3-line-height');
  }
}

.fin-field-size-l {
  --mat-form-field-container-height: theme(
    'lineHeight.text-heading-1-line-height'
  );
  --mat-form-field-container-text-size: theme('fontSize.text-body-1-size');
  --mat-form-field-container-vertical-padding: 1.2rem;
}

.fin-password-toggle-disabled {
  opacity: 0.4;
}

.fin-field {
  // remove input number arrows
  // Chrome, Safari, Edge, Opera
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  // Firefox
  input[type='number'] {
    -moz-appearance: textfield;
  }
}
