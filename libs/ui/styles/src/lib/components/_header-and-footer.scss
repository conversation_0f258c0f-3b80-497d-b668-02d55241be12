.fin-hf-common {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  background-color: theme('colors.color-surface-primary');
  color: theme('colors.color-text-primary');
  font-size: theme('fontSize.text-body-1-size');
  line-height: theme('lineHeight.text-body-1-line-height');
  font-weight: theme('fontWeight.text-body-2-strong-weight');
}

.fin-header {
  border-bottom: 1px solid theme('colors.color-border-default-primary');
  &-s {
    padding: 1.2rem 1.6rem;
  }
  &-m {
    padding: 2rem 3.2rem;
  }
}
.fin-footer {
  &-s {
    padding: 1.6rem;
  }
  &-m {
    padding: 2.4rem 3.2rem 3.2rem;
  }
}
