.fin-modal {
  --mat-dialog-container-max-width: 100vw;

  --mdc-dialog-subhead-line-height: theme('lineHeight.text-body-1-line-height');
  --mdc-dialog-subhead-size: theme('fontSize.text-body-1-size');
  --mdc-dialog-subhead-weight: theme('fontWeight.text-body-2-strong-weight');
  --mdc-dialog-subhead-color: theme('colors.color-text-primary');

  .mat-mdc-dialog-container .mat-mdc-dialog-title + .mat-mdc-dialog-content {
    padding-top: 3.2rem;
  }

  .mat-mdc-dialog {
    &-title {
      &::before {
        display: none;
      }
      padding: 2rem 3.2rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid theme('colors.color-border-default-inactive');
      margin: 0;
    }
    &-content {
      --mat-dialog-with-actions-content-padding: 2.4rem 3.2rem;
      --mat-dialog-content-padding: 2.4rem 3.2rem;

      &:has(> .fin-scrollbar),
      &:has(> .fin-modal-slot > .fin-scrollbar) {
        max-height: none;
      }
    }
    &-actions {
      padding: 2.4rem 3.2rem 3.2rem 3.2rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .fin-modal-slots-container {
    .mat-mdc-dialog-content {
      --mat-dialog-content-padding: 0 0;
      --mat-dialog-with-actions-content-padding: 0 0;

      display: flex;
    }

    .fin-modal-slot {
      display: block;

      &-s {
        width: 36rem;
      }

      &-m {
        width: 44rem;
      }

      &-l {
        width: 66.4rem;
      }

      &-xl {
        width: 100%;
      }
    }
  }

  .fin-modal-header:has(.fin-header),
  .fin-modal-footer:has(.fin-footer) {
    padding: 0;
  }

  .fin-modal-header:has(.fin-header) {
    border-bottom: none;
  }

  &-s {
    width: 41rem;
  }

  &-m {
    width: 60rem;
  }

  &-l {
    width: 80rem;
  }

  &-xl {
    width: 102.4rem;
  }

  &-xxl {
    --mdc-dialog-container-color: rgba(248, 248, 248, 0.4);

    width: 100vw;
    height: 100vh;

    .fin-modal {
      &-slots-container {
        flex-grow: 1;
      }
    }

    .mat-mdc-dialog {
      &-content {
        max-height: 100vh;
      }
      &-surface {
        backdrop-filter: blur(3rem);
      }
    }

    .fin-modal-header,
    .fin-modal-footer,
    .fin-hf-common {
      background-color: transparent;
      border-bottom: none;
    }
  }
}
