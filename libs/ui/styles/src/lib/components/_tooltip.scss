.fin-tooltip.tooltip {
  background-color: theme('colors.color-background-tertiary-strong');
  color: theme('colors.color-text-light');
  padding: 0.8rem !important;
  border-radius: 4px;
  z-index: 1000 !important; // same z-index as cdk-overlay-container to avoid bugs

  &.show {
    opacity: 1;
  }

  .tooltip-inner {
    --bg-tooltip-padding-y: 0;
    --bg-tooltip-padding-x: 0;
    --bs-tooltip-color: theme('colors.color-text-light');
    --bs-tooltip-bg: theme('colors.color-background-tertiary-strong');
    --bs-tooltip-border-radius: 0;

    font-weight: theme('fontWeight.text-body-3-moderate-weight');
    font-size: theme('fontSize.text-body-3-size');
    line-height: theme('lineHeight.text-body-3-line-height');
    text-align: left;
    max-width: 24rem;
  }

  .tooltip-arrow {
    visibility: hidden;
  }

  &.fin-tooltip-arrow {
    .tooltip-arrow {
      visibility: visible;
      &::before {
        content: '';
        border-style: solid;
        position: absolute;
      }
    }

    &.bs-tooltip-top .tooltip-arrow {
      top: 100% !important;
      left: -0.5rem !important;

      &::before {
        border-width: 0.5rem 0.5rem 0 0.5rem;
        border-color: theme('colors.color-background-tertiary-strong')
          transparent transparent transparent;
      }
    }

    &.bs-tooltip-bottom .tooltip-arrow {
      left: -0.5rem !important;
      top: -1rem !important;

      &::before {
        border-width: 0.5rem 0.5rem 0.5rem 0.5rem;
        border-color: transparent transparent
          theme('colors.color-background-tertiary-strong') transparent;
      }
    }

    &.bs-tooltip-start .tooltip-arrow {
      right: 0 !important;
      top: -0.5rem !important;

      &::before {
        border-width: 0.5rem 0 0.5rem 0.5rem;
        border-color: transparent transparent transparent
          theme('colors.color-background-tertiary-strong');
      }
    }

    &.bs-tooltip-end .tooltip-arrow {
      left: -0.5rem !important;
      top: -0.5rem !important;

      &::before {
        border-width: 0.5rem 0.5rem 0.5rem 0;
        border-color: transparent
          theme('colors.color-background-tertiary-strong') transparent
          transparent;
      }
    }
  }
}
