.mat-mdc-select-panel,
.mat-mdc-autocomplete-panel {
  --mat-select-trigger-text-font: theme('fontFamily.text-body-2-family'),
    sans-serif;
  --mat-option-label-text-size: theme('fontSize.text-body-2-size');
  --mat-option-focus-state-layer-color: theme('colors.color-hover-tertiary');
  --mat-option-selected-state-layer-color: theme(
    'colors.color-background-tertiary-minimal'
  );
  --mat-option-hover-state-layer-color: theme('colors.color-hover-tertiary');
  --mat-select-container-elevation-shadow: 0px 4px 10px 0px
    rgba(38, 40, 62, 0.16);
  --mat-autocomplete-container-elevation-shadow: 0px 4px 10px 0px
    rgba(38, 40, 62, 0.16);
  --mat-option-selected-state-label-text-color: theme(
    'colors.color-text-primary'
  );
  --mat-option-label-text-color: theme('colors.color-text-primary');
  --mat-option-label-text-weight: theme(
    'fontWeight.text-body-2-moderate-weight'
  );

  padding: 1.2rem !important;
  margin-top: 0.4rem;
  border-radius: 0.4rem !important;

  .mat-mdc-option {
    min-height: 4rem;
    padding: 0;
    border-radius: 0.4rem;

    &:not(:last-child) {
      margin-bottom: 0.2rem;
    }

    .mdc-list-item__primary-text {
      width: 100%;
    }

    &.fin-custom-message {
      --mat-option-label-text-color: theme('colors.color-text-secondary');
      padding: 0 1rem;
      cursor: default;
      font-family: theme('fontFamily.text-body-2-family');
      font-size: theme('fontSize.text-body-2-size');
      font-weight: theme('fontWeight.text-body-2-moderate-weight');
      line-height: theme('lineHeight.text-body-2-line-height');
      &:hover,
      & {
        --mat-option-hover-state-layer-color: transparent;
        --mat-option-focus-state-layer-color: transparent;
      }
    }

    &.mdc-list-item--selected {
      --mat-option-label-text-weight: theme(
        'fontWeight.text-body-2-strong-weight'
      );
      font-size: theme('fontSize.text-body-2-size');
      line-height: theme('lineHeight.text-body-2-line-height');
    }

    .mat-icon {
      margin-right: 0 !important;
      margin-left: 0 !important;
    }
    .mat-pseudo-checkbox.mat-pseudo-checkbox-minimal {
      margin-left: 0;
      transform: scale(0.85);
    }
    &:hover:not(.mdc-list-item--disabled) {
      background-color: var(--mat-option-hover-state-layer-color) !important;
    }
    &.mat-mdc-option-active.mdc-list-item {
      background-color: var(--mat-option-focus-state-layer-color) !important;
    }
  }

  .fin-multiple,
  .mat-mdc-option-multiple {
    --mdc-list-list-item-selected-container-color: theme(
      'colors.color-background-tertiary-minimal'
    );

    .mat-pseudo-checkbox-full {
      display: none;
    }

    &.mdc-list-item--selected {
      background-color: var(--mdc-list-list-item-selected-container-color);
    }
  }
}

.mat-form-field-appearance-outline {
  --mat-select-trigger-text-font: theme('fontFamily.text-body-2-family'),
    sans-serif;
  --mat-select-enabled-trigger-text-color: theme('colors.color-text-primary');
  --mat-select-trigger-text-size: theme('fontSize.text-body-2-size');
  --mat-select-trigger-text-weight: theme(
    'fontWeight.text-body-2-moderate-weight'
  );
  .fin-dropdown-arrow {
    font-size: 2.1rem;
  }
  .fin-dropdown-icons {
    color: theme('colors.color-icons-primary');
  }

  &.mat-form-field-disabled {
    .fin-dropdown-icons {
      color: theme('colors.color-icons-disabled');
    }
    .mdc-notched-outline {
      &__leading,
      &__notch,
      &__trailing {
        background-color: theme('colors.color-background-disabled');
      }
    }
  }

  &.fin-field-readonly.mat-form-field-disabled {
    .mdc-notched-outline {
      &__leading,
      &__notch,
      &__trailing {
        background-color: theme('colors.color-surface-primary');
      }
    }
    .mat-mdc-select-trigger {
      color: theme('colors.color-text-primary');
    }
  }

  .mdc-notched-outline__notch {
    max-width: calc(
      100% - max(1.2rem, var(--mdc-outlined-text-field-container-shape)) * 2
    ) !important;
  }
  .mat-mdc-select-arrow {
    display: none;
  }

  // SIZES
  &.fin-field-size-l {
    --mat-select-trigger-text-size: theme('fontSize.text-body-1-size');
    .fin-select-trigger-label,
    .fin-select-trigger-label-hidden {
      font-family: theme('fontFamily.text-body-1-family');
      font-weight: theme('fontWeight.text-body-1-moderate-weight');
      font-size: theme('fontSize.text-body-1-size');
      line-height: theme('lineHeight.text-body-1-line-height');
    }
  }
  &.fin-field-size-m {
    --mat-select-trigger-text-size: 1.4rem;
    .fin-select-trigger-label,
    .fin-select-trigger-label-hidden {
      font-family: theme('fontFamily.text-body-2-family');
      font-weight: theme('fontWeight.text-body-2-moderate-weight');
      font-size: theme('fontSize.text-body-2-size');
      line-height: theme('lineHeight.text-body-2-line-height');
    }
  }
}
