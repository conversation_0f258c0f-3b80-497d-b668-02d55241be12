$scrollbar-size: 0.6rem;
$scrollbar-radius: 0.4rem;
$scrollbar-color: theme('colors.color-background-dark-moderate');
$scrollbar-track-color: theme('colors.color-background-tertiary-minimal');

::-webkit-scrollbar {
  width: $scrollbar-size;
  height: $scrollbar-size;
}

::-webkit-scrollbar-track {
  background: #{$scrollbar-track-color};
}

::-webkit-scrollbar-thumb {
  background: $scrollbar-color;
  border-radius: $scrollbar-radius;
}

ng-scrollbar.ng-scrollbar {
  --scrollbar-border-radius: #{$scrollbar-radius};
  --scrollbar-thickness: 6;
  --scrollbar-thumb-color: #{$scrollbar-color};
  --scrollbar-track-color: #{$scrollbar-track-color};
  --scrollbar-thumb-hover-color: var(--scrollbar-thumb-color);
  --scrollbar-thumb-transition: height ease-out 150ms, width ease-out 150ms;

  .ng-scrollbar-track {
    border-radius: 0;
  }
}
