@tailwind base;

@layer base {
  /* ===========================
   Chopin Thin (100)
   =========================== */

  /* Thin – Normal */
  @font-face {
    font-family: 'Chopin';
    src:
      url('/assets/fonts/chopin/chopin-thin/Chopin-Thin.woff2') format('woff2'),
      url('/assets/fonts/chopin/chopin-thin/Chopin-Thin.woff') format('woff');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
  }

  /* Thin – Italic */
  @font-face {
    font-family: 'Chopin';
    src:
      url('/assets/fonts/chopin/chopin-thin-italic/Chopin-ThinItalic.woff2')
        format('woff2'),
      url('/assets/fonts/chopin/chopin-thin-italic/Chopin-ThinItalic.woff')
        format('woff');
    font-weight: 100;
    font-style: italic;
    font-display: swap;
  }

  /* ===========================
   Chopin ExtraLight (200)
   =========================== */

  @font-face {
    font-family: 'Chopin';
    src:
      url('/assets/fonts/chopin/chopin-extra-light/Chopin-ExtraLight.woff2')
        format('woff2'),
      url('/assets/fonts/chopin/chopin-extra-light/Chopin-ExtraLight.woff')
        format('woff');
    font-weight: 200;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Chopin';
    src:
      url('/assets/fonts/chopin/chopin-extra-light-italic/Chopin-ExtraLightItalic.woff2')
        format('woff2'),
      url('/assets/fonts/chopin/chopin-extra-light-italic/Chopin-ExtraLightItalic.woff')
        format('woff');
    font-weight: 200;
    font-style: italic;
    font-display: swap;
  }

  /* ===========================
   Chopin Light (300)
   =========================== */

  @font-face {
    font-family: 'Chopin';
    src:
      url('/assets/fonts/chopin/chopin-light/Chopin-Light.woff2')
        format('woff2'),
      url('/assets/fonts/chopin/chopin-light/Chopin-Light.woff') format('woff');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Chopin';
    src:
      url('/assets/fonts/chopin/chopin-light-italic/Chopin-LightItalic.woff2')
        format('woff2'),
      url('/assets/fonts/chopin/chopin-light-italic/Chopin-LightItalic.woff')
        format('woff');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
  }

  /* ===========================
   Chopin Regular (400)
   =========================== */

  @font-face {
    font-family: 'Chopin';
    src:
      url('/assets/fonts/chopin/chopin-regular/Chopin-Regular.woff2')
        format('woff2'),
      url('/assets/fonts/chopin/chopin-regular/Chopin-Regular.woff')
        format('woff');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Chopin';
    src:
      url('/assets/fonts/chopin/chopin-regular-italic/Chopin-RegularItalic.woff2')
        format('woff2'),
      url('/assets/fonts/chopin/chopin-regular-italic/Chopin-RegularItalic.woff')
        format('woff');
    font-weight: 400;
    font-style: italic;
    font-display: swap;
  }

  /* ===========================
   Chopin Medium (500)
   =========================== */

  @font-face {
    font-family: 'Chopin';
    src:
      url('/assets/fonts/chopin/chopin-medium/Chopin-Medium.woff2')
        format('woff2'),
      url('/assets/fonts/chopin/chopin-medium/Chopin-Medium.woff')
        format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Chopin';
    src:
      url('/assets/fonts/chopin/chopin-medium-italic/Chopin-MediumItalic.woff2')
        format('woff2'),
      url('/assets/fonts/chopin/chopin-medium-italic/Chopin-MediumItalic.woff')
        format('woff');
    font-weight: 500;
    font-style: italic;
    font-display: swap;
  }

  /* ===========================
   Chopin SemiBold (600)
   =========================== */

  @font-face {
    font-family: 'Chopin';
    src:
      url('/assets/fonts/chopin/chopin-semi-bold/Chopin-SemiBold.woff2')
        format('woff2'),
      url('/assets/fonts/chopin/chopin-semi-bold/Chopin-SemiBold.woff')
        format('woff');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Chopin';
    src:
      url('/assets/fonts/chopin/chopin-semi-bold-italic/Chopin-SemiBoldItalic.woff2')
        format('woff2'),
      url('/assets/fonts/chopin/chopin-semi-bold-italic/Chopin-SemiBoldItalic.woff')
        format('woff');
    font-weight: 600;
    font-style: italic;
    font-display: swap;
  }

  /* ===========================
   Chopin Bold (700)
   =========================== */

  @font-face {
    font-family: 'Chopin';
    src:
      url('/assets/fonts/chopin/chopin-bold/Chopin-Bold.woff2') format('woff2'),
      url('/assets/fonts/chopin/chopin-bold/Chopin-Bold.woff') format('woff');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Chopin';
    src:
      url('/assets/fonts/chopin/chopin-bold-italic/Chopin-BoldItalic.woff2')
        format('woff2'),
      url('/assets/fonts/chopin/chopin-bold-italic/Chopin-BoldItalic.woff')
        format('woff');
    font-weight: 700;
    font-style: italic;
    font-display: swap;
  }

  /* ===========================
   Chopin ExtraBold (800)
   =========================== */

  @font-face {
    font-family: 'Chopin';
    src:
      url('/assets/fonts/chopin/chopin-extra-bold/Chopin-ExtraBold.woff2')
        format('woff2'),
      url('/assets/fonts/chopin/chopin-extra-bold/Chopin-ExtraBold.woff')
        format('woff');
    font-weight: 800;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Chopin';
    src:
      url('/assets/fonts/chopin/chopin-extra-bold-italic/Chopin-ExtraBoldItalic.woff2')
        format('woff2'),
      url('/assets/fonts/chopin/chopin-extra-bold-italic/Chopin-ExtraBoldItalic.woff')
        format('woff');
    font-weight: 800;
    font-style: italic;
    font-display: swap;
  }

  /* ===========================
   Chopin Black (900)
   =========================== */

  @font-face {
    font-family: 'Chopin';
    src:
      url('/assets/fonts/chopin/chopin-black/Chopin-Black.woff2')
        format('woff2'),
      url('/assets/fonts/chopin/chopin-black/Chopin-Black.woff') format('woff');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Chopin';
    src:
      url('/assets/fonts/chopin/chopin-black-italic/Chopin-BlackItalic.woff2')
        format('woff2'),
      url('/assets/fonts/chopin/chopin-black-italic/Chopin-BlackItalic.woff')
        format('woff');
    font-weight: 900;
    font-style: italic;
    font-display: swap;
  }

  /* ===========================
   Chopin Variable – Roman (weight-axis)
   =========================== */

  @font-face {
    font-family: 'Chopin Variable';
    src:
      url('/assets/fonts/chopin/chopin-variable-roman/ChopinVariable-Roman.woff2')
        format('woff2'),
      url('/assets/fonts/chopin/chopin-variable-roman/ChopinVariable-Roman.woff')
        format('woff');
    font-weight: 100 900;
    font-style: normal;
    font-display: swap;
  }

  /* ===========================
   Chopin Variable – Italic (weight-axis)
   =========================== */

  @font-face {
    font-family: 'Chopin Variable';
    src:
      url('/assets/fonts/chopin/chopin-variable-italic/ChopinVariable-Italic.woff2')
        format('woff2'),
      url('/assets/fonts/chopin/chopin-variable-italic/ChopinVariable-Italic.woff')
        format('woff');
    font-weight: 100 900;
    font-style: italic;
    font-display: swap;
  }
}
