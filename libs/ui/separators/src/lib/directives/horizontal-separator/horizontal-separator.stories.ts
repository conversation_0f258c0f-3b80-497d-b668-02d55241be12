import { BrowserModule } from '@angular/platform-browser';
import {
  Meta,
  StoryObj,
  componentWrapperDecorator,
  moduleMetadata,
} from '@storybook/angular';
import { FinSeparator } from '../../enums/fin-separators';
import { FinSeparatorsModule } from '../../separators.module';
import { FinHorizontalSeparatorDirective } from './horizontal-separator.directive';

const meta: Meta<FinHorizontalSeparatorDirective> = {
  title: 'Directives/Separators/Horizontal Separator',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinSeparatorsModule } from "@fincloud/ui/separators"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=8533-30&t=d8E0N6qWbgN7Bhgr-4',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [BrowserModule, FinSeparatorsModule],
    }),
    componentWrapperDecorator(
      (story) => `<div style="width: 300px">${story}</div>`,
    ),
  ],
  argTypes: {
    type: {
      options: Object.values(FinSeparator),
      control: { type: 'select' },
      description: 'Type of the horizontal separator.',
      defaultValue: FinSeparator.MINIMAL,
      table: {
        defaultValue: { summary: 'FinSeparator.MINIMAL' },
      },
    },
  },

  render: (args) => ({
    template: `
    <hr finHorizontalSeparator [type]="type">
  `,
    props: args,
  }),
  args: {
    type: FinSeparator.MINIMAL,
  },
};
export default meta;
type Story = StoryObj<FinHorizontalSeparatorDirective>;

export const Minimal: Story = {
  args: {
    type: FinSeparator.MINIMAL,
  },
};

export const Subtle: Story = {
  args: {
    type: FinSeparator.SUBTLE,
  },
};

export const Strong: Story = {
  args: {
    type: FinSeparator.STRONG,
  },
};
