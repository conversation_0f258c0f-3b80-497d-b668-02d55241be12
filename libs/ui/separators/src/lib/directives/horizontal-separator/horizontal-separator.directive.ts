import { Directive, HostBinding, Input } from '@angular/core';
import { FinSeparator } from '../../enums/fin-separators';
import { SEPARATOR_COLOR_MAPPING } from '../../utils/separator-color-mapping';

/**
 * Visual horizontal divider used to separate content or sections.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/directives-horizontal-separator--docs Storybook Reference}
 */

@Directive({
  selector: 'hr[finHorizontalSeparator]',
  standalone: true,
  host: {
    class: 'fin-horizontal-separator',
  },
})
export class FinHorizontalSeparatorDirective {
  /** Type of the horizontal separator. */
  @Input() type: FinSeparator = FinSeparator.MINIMAL;

  @HostBinding('class')
  get separatorClass(): string {
    return `fin-border-t fin-opacity-100 ${SEPARATOR_COLOR_MAPPING[this.type]}`;
  }
}
