import { Directive, HostBinding, Input } from '@angular/core';
import { FinSeparator } from '../../enums/fin-separators';
import { SEPARATOR_COLOR_MAPPING } from '../../utils/separator-color-mapping';

/**
 * Visual vertical divider used to separate content or sections.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/directives-vertical-separator--docs Storybook Reference}
 */
@Directive({
  selector: '[finVerticalSeparator]',
  standalone: true,
  host: {
    class: 'fin-vertical-separator',
    '[class]': 'separatorClass',
  },
})
export class FinVerticalSeparatorDirective {
  /** Type of the vertical separator. */
  @Input() type: FinSeparator = FinSeparator.MINIMAL;

  @HostBinding('class')
  get separatorClass(): string {
    return `fin-border-r ${SEPARATOR_COLOR_MAPPING[this.type]} fin-w-[0.1rem] fin-h-full`;
  }
}
