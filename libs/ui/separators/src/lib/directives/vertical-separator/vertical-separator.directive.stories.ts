import { BrowserModule } from '@angular/platform-browser';
import {
  Meta,
  StoryObj,
  componentWrapperDecorator,
  moduleMetadata,
} from '@storybook/angular';
import { FinSeparator } from '../../enums/fin-separators';
import { FinSeparatorsModule } from '../../separators.module';
import { FinVerticalSeparatorDirective } from './vertical-separator.directive';

const meta: Meta<FinVerticalSeparatorDirective> = {
  title: 'Directives/Separators/Vertical Separator',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinSeparatorsModule } from "@fincloud/ui/separators"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=8533-30&t=d8E0N6qWbgN7Bhgr-4',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [BrowserModule, FinSeparatorsModule],
    }),
    componentWrapperDecorator(
      (story) => `<div style="width: 1px; height: 300px">${story}</div>`,
    ),
  ],
  argTypes: {
    type: {
      options: Object.values(FinSeparator),
      control: { type: 'select' },
      description: 'Type of the vertical separator.',
    },
  },

  render: (args) => ({
    template: `
    <div finVerticalSeparator [type]="type">
  `,
    props: args,
  }),
  args: {
    type: FinSeparator.MINIMAL,
  },
};
export default meta;
type Story = StoryObj<FinVerticalSeparatorDirective>;

export const Minimal: Story = {
  args: {
    type: FinSeparator.MINIMAL,
  },
};

export const Subtle: Story = {
  args: {
    type: FinSeparator.SUBTLE,
  },
};

export const Strong: Story = {
  args: {
    type: FinSeparator.STRONG,
  },
};
