import {
  booleanAttribute,
  Component,
  Input,
  ViewEncapsulation,
} from '@angular/core';

@Component({
  selector: '[finAiShadowEffect]',
  standalone: true,
  imports: [],
  templateUrl: './ai-shadow-effect.component.html',
  styleUrl: './ai-shadow-effect.component.scss',
  encapsulation: ViewEncapsulation.None,
})
export class FinAIShadowEffectComponent {
  /** 'Whether the shadow is visible or not.' */
  @Input({ transform: booleanAttribute })
  finAiShadowEffect = false;
}
