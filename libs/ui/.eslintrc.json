{"extends": ["../../.eslintrc.json"], "ignorePatterns": ["!**/*", "storybook-static"], "overrides": [{"files": ["*.ts"], "extends": ["plugin:@nx/angular", "plugin:@angular-eslint/template/process-inline-templates"], "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "fin", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "fin"}], "@angular-eslint/no-host-metadata-property": 0}}, {"files": ["*.html"], "extends": ["plugin:@nx/angular-template", "plugin:prettier/recommended"], "rules": {"@angular-eslint/template/prefer-control-flow": "error", "prettier/prettier": ["error", {"endOfLine": "auto"}]}}, {"files": ["*.json"], "parser": "jsonc-eslint-parser", "rules": {"@nx/dependency-checks": "error"}}]}