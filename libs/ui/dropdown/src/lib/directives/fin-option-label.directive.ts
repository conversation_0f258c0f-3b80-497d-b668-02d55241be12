import { Directive, Input, OnInit, TemplateRef } from '@angular/core';
import { CtxOptionTemplateDirective } from '../models/context-option-directive';

@Directive({
  selector: '[finOptionLabel]',
  standalone: true,
})
export class FinOptionLabelDirective<T> implements OnInit {
  @Input('finOptionLabel') data: T[] = [];
  optionTemplate!: TemplateRef<unknown>;

  constructor(private _rowTemplate: TemplateRef<unknown>) {}

  ngOnInit(): void {
    this.optionTemplate = this._rowTemplate;
  }

  static ngTemplateContextGuard<T>(
    directive: FinOptionLabelDirective<T>,
    context: unknown,
  ): context is CtxOptionTemplateDirective<T> {
    return true;
  }
}
