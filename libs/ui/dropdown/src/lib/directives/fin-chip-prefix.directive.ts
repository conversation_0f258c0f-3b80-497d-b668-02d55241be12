import { Directive, Input, OnInit, TemplateRef } from '@angular/core';
import { CtxOptionTemplateDirective } from '../models/context-option-directive';

@Directive({
  selector: 'ng-template[finChipPrefix]',
  standalone: true,
})
export class FinChipPrefixDirective<T> implements OnInit {
  @Input('finChipPrefix') data: T[] | undefined = [];
  chipTemplate!: TemplateRef<unknown>;

  constructor(private _chipPrefixTemplate: TemplateRef<unknown>) {}

  ngOnInit(): void {
    this.chipTemplate = this._chipPrefixTemplate;
  }

  static ngTemplateContextGuard<T>(
    directive: FinChipPrefixDirective<T>,
    context: unknown,
  ): context is CtxOptionTemplateDirective<T> {
    return true;
  }
}
