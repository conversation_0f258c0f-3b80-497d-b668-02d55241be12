import { Directive, Input, OnInit, TemplateRef } from '@angular/core';
import { CtxOptionTemplateDirective } from '../models/context-option-directive';

@Directive({
  selector: '[finOptionPrefix]',
  standalone: true,
})
export class FinOptionPrefixDirective<T> implements OnInit {
  @Input('finOptionPrefix') data: T[] = [];
  optionTemplate!: TemplateRef<unknown>;

  constructor(private _rowTemplate: TemplateRef<unknown>) {}

  ngOnInit(): void {
    this.optionTemplate = this._rowTemplate;
  }

  static ngTemplateContextGuard<T>(
    directive: FinOptionPrefixDirective<T>,
    context: unknown,
  ): context is CtxOptionTemplateDirective<T> {
    return true;
  }
}
