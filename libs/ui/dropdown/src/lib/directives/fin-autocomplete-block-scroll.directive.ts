import { OverlayConfig } from '@angular/cdk/overlay';
import { DestroyRef, Directive, NgZone, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatAutocompleteTrigger } from '@angular/material/autocomplete';
import { debounceTime, filter, fromEvent, map } from 'rxjs';

@Directive({
  // eslint-disable-next-line @angular-eslint/directive-selector
  selector: '[matAutocomplete][finAutocompleteBlockScroll]',
  standalone: true,
})
export class FinAutocompleteBlockScrollDirective implements OnInit {
  constructor(
    private matAutocompleteTrigger: MatAutocompleteTrigger,
    private destroyRef: DestroyRef,
    private ngZone: NgZone,
  ) {}

  ngOnInit(): void {
    this.setOverlayConfig();

    this.ngZone.runOutsideAngular(() => {
      this.setScrollEvent();
    });
  }

  private setScrollEvent() {
    fromEvent(window, 'wheel')
      .pipe(
        map(() => {
          const overlay = document.getElementsByClassName(
            'fin-autocomplete-block-scroll',
          )[0];
          overlay?.classList.remove('fin-pointer-events-none');

          return overlay;
        }),
        filter((overlay) => !!overlay),
        debounceTime(300),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((overlay) => {
        overlay.classList.add('fin-pointer-events-none');
      });
  }

  private setOverlayConfig() {
    // Workaround for autocomplete scroll block in fin modal
    // https://github.com/angular/components/issues/26484

    (this.matAutocompleteTrigger as any)._getOverlayConfig =
      (): OverlayConfig => {
        return new OverlayConfig({
          positionStrategy: (
            this.matAutocompleteTrigger as any
          )._getOverlayPosition(),
          scrollStrategy: (
            this.matAutocompleteTrigger as any
          )._scrollStrategy(),
          width: (this.matAutocompleteTrigger as any)._getPanelWidth(),
          direction: (this.matAutocompleteTrigger as any)._dir ?? undefined,
          panelClass: (this.matAutocompleteTrigger as any)._defaults
            ?.overlayPanelClass,
          hasBackdrop: true,
          backdropClass: [
            'cdk-overlay-transparent-backdrop',
            'fin-autocomplete-block-scroll',
            'fin-pointer-events-none',
          ],
        });
      };
  }
}
