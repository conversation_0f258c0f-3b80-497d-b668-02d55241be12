import { Directive, Input, OnInit, TemplateRef } from '@angular/core';
import { CtxOptionTemplateDirective } from '../models/context-option-directive';

@Directive({
  selector: '[finOptionSuffix]',
  standalone: true,
})
export class FinOptionSuffixDirective<T> implements OnInit {
  @Input('finOptionSuffix') data: T[] = [];
  optionTemplate!: TemplateRef<unknown>;

  constructor(private _rowTemplate: TemplateRef<unknown>) {}

  ngOnInit(): void {
    this.optionTemplate = this._rowTemplate;
  }

  static ngTemplateContextGuard<T>(
    directive: FinOptionSuffixDirective<T>,
    context: unknown,
  ): context is CtxOptionTemplateDirective<T> {
    return true;
  }
}
