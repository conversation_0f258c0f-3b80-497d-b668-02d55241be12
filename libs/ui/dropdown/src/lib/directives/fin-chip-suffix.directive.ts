import { Directive, Input, OnInit, TemplateRef } from '@angular/core';
import { CtxOptionTemplateDirective } from '../models/context-option-directive';

@Directive({
  selector: 'ng-template[finChipSuffix]',
  standalone: true,
})
export class FinChipSuffixDirective<T> implements OnInit {
  @Input('finChipSuffix') data: T[] = [];
  chipTemplate!: TemplateRef<unknown>;

  constructor(private _rowTemplate: TemplateRef<unknown>) {}

  ngOnInit(): void {
    this.chipTemplate = this._rowTemplate;
  }

  static ngTemplateContextGuard<T>(
    directive: FinChipSuffixDirective<T>,
    context: unknown,
  ): context is CtxOptionTemplateDirective<T> {
    return true;
  }
}
