import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { MatPseudoCheckboxModule } from '@angular/material/core';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinOptionLabelDirective } from '../../directives/fin-option-label.directive';
import { FinOptionPrefixDirective } from '../../directives/fin-option-prefix.directive';
import { FinOptionSuffixDirective } from '../../directives/fin-option-suffix.directive';
import { FinDropdownOption } from '../../models/fin-dropdown-option';

@Component({
  selector: 'fin-dropdown-option',
  standalone: true,
  imports: [CommonModule, MatPseudoCheckboxModule, FinTruncateTextModule],
  templateUrl: './dropdown-option.component.html',
  styleUrl: './dropdown-option.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    '[class]': 'optionClasses',
  },
})
export class FinDropdownOptionComponent<T> {
  @Input() option!: FinDropdownOption;
  /** A custom property name which holds the label. */
  @Input() labelPropertyName = 'label';
  @Input() isMatOptionsSelected = false;
  @Input() multiple = false;
  @Input() autocomplete = false;
  @Input() showChips = false;
  @Input() optionSuffix!: FinOptionSuffixDirective<T>;
  @Input() optionPrefix!: FinOptionPrefixDirective<T>;
  @Input() optionLabel!: FinOptionLabelDirective<T>;
  protected get optionClasses() {
    let optionClasses = 'fin-dropdown-option';
    if (this.showChips) {
      optionClasses += ' fin-multiple-chips';
    }
    if (this.multiple && this.autocomplete) {
      optionClasses += ' fin-multiple-autocomplete';
    }
    return optionClasses;
  }
}
