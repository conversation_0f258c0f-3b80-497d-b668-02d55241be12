<div class="fin-flex fin-gap-x-[0.8rem] fin-items-center fin-px-[0.8rem]">
  @if (optionPrefix) {
    <span>
      <ng-container
        [ngTemplateOutlet]="optionPrefix.optionTemplate"
        [ngTemplateOutletContext]="{ $implicit: option }"
      ></ng-container>
    </span>
  }

  <span class="fin-w-full" finTruncateText>
    @if (optionLabel) {
      <ng-container
        [ngTemplateOutlet]="optionLabel.optionTemplate"
        [ngTemplateOutletContext]="{ $implicit: option }"
      ></ng-container>
    } @else {
      {{ option[labelPropertyName] }}
    }
  </span>

  @if (optionSuffix) {
    <span>
      <ng-container
        [ngTemplateOutlet]="optionSuffix.optionTemplate"
        [ngTemplateOutletContext]="{ $implicit: option }"
      ></ng-container>
    </span>
  }

  @if (multiple && isMatOptionsSelected) {
    <div
      class="fin-w-size-spacing-16 fin-h-size-spacing-16 fin-ml-size-spacing-8"
    >
      <mat-pseudo-checkbox
        [state]="isMatOptionsSelected ? 'checked' : 'unchecked'"
        appearance="minimal"
      ></mat-pseudo-checkbox>
    </div>
  }
</div>
