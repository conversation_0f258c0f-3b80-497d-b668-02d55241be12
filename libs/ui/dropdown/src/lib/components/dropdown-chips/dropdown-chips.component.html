<mat-chip-set>
  @for (option of options; track option[valuePropertyName]) {
    <mat-chip (removed)="removeSelectedOption(option)">
      <div
        class="fin-w-full fin-flex fin-items-center fin-gap-x-size-spacing-8"
      >
        @if (chipPrefix) {
          <ng-container
            [ngTemplateOutlet]="chipPrefix.chipTemplate"
            [ngTemplateOutletContext]="{ $implicit: option }"
          ></ng-container>
        }

        <span finTruncateText class="text-label">
          {{ option[labelPropertyName] }}
        </span>

        @if (chipSuffix) {
          <ng-container
            [ngTemplateOutlet]="chipSuffix.chipTemplate"
            [ngTemplateOutletContext]="{ $implicit: option }"
          ></ng-container>
        }
        @if (showClose) {
          <button
            matChipRemove
            fin-button-action
            class="hover:fin-bg-color-hover-tertiary"
          >
            <fin-icon name="close"></fin-icon>
          </button>
        }
      </div>
    </mat-chip>
  }
</mat-chip-set>
