import { CommonModule } from '@angular/common';
import {
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import { MatChipsModule } from '@angular/material/chips';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinSize } from '@fincloud/ui/types';
import { FinChipPrefixDirective } from '../../directives/fin-chip-prefix.directive';
import { FinChipSuffixDirective } from '../../directives/fin-chip-suffix.directive';
import { FinDropdownOption } from '../../models/fin-dropdown-option';

@Component({
  selector: 'fin-dropdown-chips',
  standalone: true,
  imports: [
    CommonModule,
    MatChipsModule,
    FinButtonModule,
    FinIconModule,
    FinTruncateTextModule,
  ],
  templateUrl: './dropdown-chips.component.html',
  styleUrl: './dropdown-chips.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinDropdownChipsComponent<T> {
  /** Specifies the selected options to be rendered into chips. */
  @Input() options: FinDropdownOption[] = [];

  /** Specifies whether a close button should be visible. */
  @Input({ transform: booleanAttribute }) showClose = true;

  /** Event emitted when the back button is clicked */
  @Output() removeOption = new EventEmitter<FinDropdownOption>();

  @Input() labelPropertyName = 'label';
  @Input() valuePropertyName = 'value';
  @Input() chipPrefix!: FinChipPrefixDirective<T>;
  @Input() chipSuffix!: FinChipSuffixDirective<T>;

  protected size = FinSize;
  protected removeSelectedOption(removedOption: FinDropdownOption): void {
    this.removeOption.emit(removedOption);
  }
}
