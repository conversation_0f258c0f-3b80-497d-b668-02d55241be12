import { Overlay } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  ContentChild,
  DestroyRef,
  ElementRef,
  EventEmitter,
  Inject,
  Injector,
  Input,
  OnChanges,
  Optional,
  Output,
  QueryList,
  SimpleChanges,
  ViewChild,
  ViewChildren,
  booleanAttribute,
  forwardRef,
  numberAttribute,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import {
  MAT_AUTOCOMPLETE_SCROLL_STRATEGY,
  MatAutocomplete,
  MatAutocompleteModule,
  MatAutocompleteTrigger,
} from '@angular/material/autocomplete';
import { MatPseudoCheckboxModule } from '@angular/material/core';
import { MatDialogRef } from '@angular/material/dialog';
import {
  MAT_SELECT_SCROLL_STRATEGY,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
} from '@angular/material/select';
import { FinAvatarModule } from '@fincloud/ui/avatar-default';
import { FinButtonModule } from '@fincloud/ui/button';
import {
  FinFieldMessageBodyDirective,
  FinFieldMessageService,
} from '@fincloud/ui/field-message';
import { FinIconModule } from '@fincloud/ui/icon';
import {
  FinPreventScrollDirective,
  FinScrollbarService,
} from '@fincloud/ui/scrollbar';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinErrorSpace, FinSize } from '@fincloud/ui/types';
import {
  FIN_MAT_FORM_FIELD_DEFAULT_OPTIONS,
  FinAngularMaterialModule,
  createBlockScrollStrategyFactory,
} from '@fincloud/utils/angular-material';
import { FinControlValueAccessor } from '@fincloud/utils/control-value-accessor';
import { FinExecuteFuncPipe } from '@fincloud/utils/pipes';
import { FinFieldService } from '@fincloud/utils/services';
import { setTimeoutUnpatched } from '@fincloud/utils/unpatched-api';
import { isArray, isEqual, isObject, map, uniqBy } from 'lodash-es';
import { pairwise, shareReplay, startWith } from 'rxjs';
import { FinAutocompleteBlockScrollDirective } from '../../directives/fin-autocomplete-block-scroll.directive';
import { FinChipPrefixDirective } from '../../directives/fin-chip-prefix.directive';
import { FinChipSuffixDirective } from '../../directives/fin-chip-suffix.directive';
import { FinInitialMessageDirective } from '../../directives/fin-initial-message.directive';
import { FinNoResultsMessageDirective } from '../../directives/fin-no-results-message.directive';
import { FinOptionLabelDirective } from '../../directives/fin-option-label.directive';
import { FinOptionPrefixDirective } from '../../directives/fin-option-prefix.directive';
import { FinOptionSuffixDirective } from '../../directives/fin-option-suffix.directive';
import { FinCustomMessagesConfig } from '../../models/fin-custom-messages-config';
import { FinDropdownOption } from '../../models/fin-dropdown-option';
import { FinControlValueLengthPipe } from '../../pipes/fin-control-value-length.pipe';
import { FIN_CUSTOM_MESSAGES } from '../../utils/fin-custom-messages-token';
import { FinDropdownChipsComponent } from '../dropdown-chips/dropdown-chips.component';
import { FinDropdownOptionComponent } from '../dropdown-option/dropdown-option.component';

/**
 * A component that allows users to select an option from a list.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-dropdown--docs Storybook Reference}
 */
@Component({
  selector: 'fin-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FinAngularMaterialModule,
    MatSelectModule,
    FinIconModule,
    MatAutocompleteModule,
    MatPseudoCheckboxModule,
    FinTruncateTextModule,
    FinButtonModule,
    FinIconModule,
    FinTruncateTextModule,
    FinAvatarModule,
    FinDropdownOptionComponent,
    FinDropdownChipsComponent,
    MatPseudoCheckboxModule,
    FinExecuteFuncPipe,
    FinControlValueLengthPipe,
    FinAutocompleteBlockScrollDirective,
  ],
  templateUrl: './dropdown.component.html',
  styleUrl: './dropdown.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FinDropdownComponent),
      multi: true,
    },
    FIN_MAT_FORM_FIELD_DEFAULT_OPTIONS,
    FinFieldService,
    FinFieldMessageService,
    {
      provide: MAT_SELECT_SCROLL_STRATEGY,
      deps: [Overlay],
      useFactory: createBlockScrollStrategyFactory,
    },
    {
      provide: MAT_AUTOCOMPLETE_SCROLL_STRATEGY,
      deps: [Overlay],
      useFactory: createBlockScrollStrategyFactory,
    },
  ],
  host: {
    class: 'fin-dropdown',
  },
  hostDirectives: [
    {
      directive: FinPreventScrollDirective,
    },
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinDropdownComponent<T = FinDropdownOption>
  extends FinControlValueAccessor
  implements AfterViewInit, OnChanges
{
  /** Label for the input field. */
  @Input() label = '';

  /** Placeholder for the input field. */
  @Input() placeholder = '';

  /** Size of the input. */
  @Input() size: FinSize.M | FinSize.L = FinSize.M;

  /** All of the defined select options. */
  @Input() options: FinDropdownOption[] = [];

  /** A custom property name which holds the label. */
  @Input() labelPropertyName = 'label';

  /** A custom property name which holds the value. */
  @Input() valuePropertyName = 'value';

  /** Whether the user should be allowed to select multiple options. */
  @Input({ transform: booleanAttribute }) multiple = false;

  /** Enables chips preview.  */
  @Input({ transform: booleanAttribute }) showChips = false;

  /** Enables the autocomplete option. */
  @Input({ transform: booleanAttribute }) autocomplete = false;

  /** Whether the form field should reserve space for error. */
  @Input() dynamicErrorSpace!: FinErrorSpace;

  /** Switch readonly mode. */
  @Input({ transform: booleanAttribute }) readonly = false;

  /** Minimum number of characters used to switch between `finInitialMessage` and `finNoResultsMessage`. */
  @Input({ transform: numberAttribute }) messageThreshold = 3;

  /** To be used only with external messages */
  @Input() externalFieldMessages: FinFieldMessageBodyDirective | null = null;

  /** Hides the arrow of the dropdown. To be deprecated in a future version. */
  @Input({ transform: booleanAttribute }) hideArrow = false;

  /** Custom display function. */
  @Input() inputDisplayFn?: (value: any) => string;

  /** Event emitted when the selected value has been changed */
  @Output() selectionChange = new EventEmitter<any>();

  /** Event emitted when the autocomplete input value has been changed */
  @Output() autoCompleteInputChange = new EventEmitter<string>();

  /** Emitted when a chip is to be removed. */
  @Output() chipRemoved = new EventEmitter<FinDropdownOption>();

  @ViewChild(MatSelect) protected matSelect?: MatSelect;
  @ViewChild(MatAutocomplete) protected autocompleteField?: MatAutocomplete;
  @ViewChild('triggerLabel') protected triggerLabel?: ElementRef;
  @ViewChild('matAutocompleteTrigger')
  protected matAutocompleteTrigger?: MatAutocompleteTrigger;
  @ViewChild('autocompleteInput')
  protected autocompleteInputRef?: ElementRef;
  @ViewChildren('clearButton')
  protected clearButton?: QueryList<ElementRef<HTMLButtonElement>>;
  @ViewChildren('toggleOptionsPanelBtn')
  protected toggleOptionsPanelBtn?: QueryList<ElementRef<HTMLButtonElement>>;
  @ViewChild('suffixWrapper')
  protected suffixWrapper!: ElementRef<HTMLDivElement>;

  @ContentChild(FinOptionSuffixDirective)
  protected finOptionSuffixDirective!: FinOptionSuffixDirective<T>;

  @ContentChild(FinOptionPrefixDirective)
  protected finOptionPrefixDirective!: FinOptionPrefixDirective<T>;

  @ContentChild(FinOptionLabelDirective)
  protected finOptionLabelDirective!: FinOptionLabelDirective<T>;

  @ContentChild(FinChipPrefixDirective)
  protected finChipPrefixDirective!: FinChipPrefixDirective<T>;

  @ContentChild(FinChipSuffixDirective)
  protected finChipSuffixDirective!: FinChipSuffixDirective<T>;

  @ContentChild(FinInitialMessageDirective)
  protected finInitialMessageDirective!: FinInitialMessageDirective;

  @ContentChild(FinNoResultsMessageDirective)
  protected finNoResultsMessageDirective!: FinNoResultsMessageDirective;

  protected sizes = FinSize;
  protected selectedOptionsCounter = 0;
  private triggerDelimiter = ', ';
  protected allOptions: FinDropdownOption[] = [];
  protected selectedOptions: FinDropdownOption[] = [];
  protected currentControlValue = null;
  protected customMessages!: FinCustomMessagesConfig;

  /** Returns the active validation message. */
  protected getMessage$ = this.finFieldMessageService?.getMessage$.pipe(
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  constructor(
    private finInputService: FinFieldService,
    @Optional() private finFieldMessageService: FinFieldMessageService,
    @Inject(FIN_CUSTOM_MESSAGES)
    private finCustomMessages: FinCustomMessagesConfig,
    private destroyRef: DestroyRef,
    private finScrollbarService: FinScrollbarService,
    injector: Injector,
    @Optional() private dialogRef: MatDialogRef<any>,
  ) {
    super(injector);
    this.customMessages = this.finCustomMessages;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['readonly']) {
      const { currentValue, previousValue } = changes['readonly'];

      if (currentValue) {
        this.control.disable();
      }

      if (!currentValue && previousValue) {
        this.control.enable({ emitEvent: false });
      }

      if (currentValue && this.showChips) {
        this.prepareOptions();
      }
    }

    const currentOptions = changes['options']?.currentValue;
    if (isArray(currentOptions) && currentOptions.length) {
      this.prepareOptions();
    }
  }

  ngAfterViewInit(): void {
    this.control.statusChanges
      .pipe(startWith(this.control.errors), takeUntilDestroyed(this.destroyRef))
      .subscribe((status) => {
        this.finInputService.controlErrors$.next(this.control.errors);
        if (status === 'INVALID' && this.dialogRef) {
          setTimeoutUnpatched(() => {
            this.matAutocompleteTrigger?.updatePosition();
          }, 300);
        }
      });

    this.control.valueChanges
      .pipe(
        startWith(this.control.value),
        pairwise(),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(([previousValue, currentValue]) => {
        if (this.showChips) {
          if (!currentValue.length && previousValue.length) {
            this.selectedOptions = [];
          }

          // prevent chips from being cleared when manually deleting text from the input
          if (this.control.value === '') {
            this.control.setValue(this.currentControlValue, {
              emitEvent: false,
            });
          }

          this.prepareOptions();
        }

        if (this.control.value && this.multiple && !this.autocomplete) {
          this.selectedOptionsCounter = this.calculateSelectedOptions();
        }
      });

    if (this.control.value && this.multiple) {
      this.calculateSelectedOptions();
    }
  }

  protected clearField(event: Event) {
    event.stopPropagation();
    if (this.multiple) {
      this.control.reset([]);
    } else {
      this.control.reset();
    }

    if (this.multiple && this.triggerLabel) {
      this.triggerLabel.nativeElement.textContent = '';
    }

    if (this.autocomplete) {
      this.autocompleteField?.options.forEach((option) => {
        option.deselect();
      });
    }

    this.selectedOptions = [];

    this.selectionChange.emit(this.control.value);
    this.matAutocompleteTrigger?.closePanel();
    this.setCurrentControlValue();
  }

  protected displayFn = (controlValue: string) => {
    let selectedOption: FinDropdownOption | undefined;

    if (controlValue) {
      selectedOption = this.options.find(
        (option) => option[this.valuePropertyName] === controlValue,
      );
    }

    if (!selectedOption && isObject(controlValue)) {
      return controlValue[this.labelPropertyName] as string;
    }
    if (!selectedOption && !isObject(controlValue)) {
      return controlValue;
    }
    if (selectedOption) {
      return selectedOption[this.labelPropertyName] as string;
    }

    if (this.showChips) {
      return '';
    }

    return '';
  };

  protected onSelectionChange(event: MatSelectChange) {
    this.matSelect?.close();
    this.selectionChange.emit(event.value);
  }

  protected onAutocompleteEnter(event: Event) {
    const selectedOption = this.options.find(
      (option) =>
        JSON.stringify(option[this.valuePropertyName]) ===
        JSON.stringify(this.control.value),
    );

    if (selectedOption) {
      this.onAutocompleteChange(event, selectedOption);
    }
  }

  protected onAutocompleteChange(event: Event, option: FinDropdownOption) {
    this.autocompleteField?._keyManager.setActiveItem(-1);
    if (this.showChips || this.multiple) {
      event.stopPropagation();
      this.matAutocompleteTrigger?.closePanel();
      const selectedValue = map(this.selectedOptions, this.valuePropertyName);

      const isValueExists = selectedValue.some((currentOption) =>
        isEqual(currentOption, option[this.valuePropertyName]),
      );
      if (!isValueExists) {
        this.control.setValue([
          ...selectedValue,
          option[this.valuePropertyName],
        ]);

        this.selectedOptions = [...this.selectedOptions, option];
      }

      if (isValueExists) {
        this.control.setValue(
          [...selectedValue].filter(
            (currentOption) =>
              !isEqual(currentOption, option[this.valuePropertyName]),
          ),
        );

        this.selectedOptions = this.selectedOptions.filter(
          (selectedOption) => !isEqual(selectedOption, option),
        );
      }
      if (this.showChips) {
        this.filterOptions();
      }
      this.selectedOptionsCounter = this.calculateSelectedOptions();
      this.selectionChange.emit(this.selectedOptions);
      this.setCurrentControlValue(
        map(this.selectedOptions, this.valuePropertyName),
      );
      if (
        this.options.length === this.selectedOptions.length &&
        (this.multiple || this.showChips)
      ) {
        this.matAutocompleteTrigger?.closePanel();
      }
    } else {
      this.selectionChange.emit(option[this.valuePropertyName]);
      this.setCurrentControlValue(option[this.valuePropertyName]);
    }
  }

  protected onAutocompleteInputChange() {
    const { value } = this.control;

    this.autoCompleteInputChange.emit(value);

    if (!value) {
      this.currentControlValue = null;
      this.autocompleteField?._keyManager.setActiveItem(0);
    }
  }

  protected onAutocompleteFocus() {
    this.setCurrentControlValue();
  }

  protected onAutocompleteBlur(event: FocusEvent) {
    const isClickInSuffix = this.suffixWrapper?.nativeElement.contains(
      event?.relatedTarget as Node,
    );
    const isClearButtonClicked =
      event.relatedTarget === this.clearButton?.first?.nativeElement;
    const isToggleOptionsPanelBtnClicked =
      event.relatedTarget === this.toggleOptionsPanelBtn?.first?.nativeElement;

    if (isClearButtonClicked || isToggleOptionsPanelBtnClicked) {
      return;
    }

    if (
      this.autocomplete &&
      (this.showChips || this.multiple) &&
      isClickInSuffix
    ) {
      if (this.control.value) {
        setTimeoutUnpatched(() => {
          this.control.setValue(this.currentControlValue, {
            emitEvent: false,
          });
        }, 100);
      }

      this.matAutocompleteTrigger?.closePanel();
      return;
    }

    if (this.autocomplete && isClickInSuffix) {
      this.matAutocompleteTrigger?.closePanel();
      return;
    }

    if (
      this.control.value &&
      JSON.stringify(this.currentControlValue) !==
        JSON.stringify(this.control.value)
    ) {
      this.control.setValue(this.currentControlValue, { emitEvent: false });
      this.control.updateValueAndValidity();
      this.matAutocompleteTrigger?.closePanel();
    }
  }

  protected onRemovedChip(removedChip: FinDropdownOption) {
    const updatedOptions = this.selectedOptions.filter(
      (option: FinDropdownOption) =>
        option[this.valuePropertyName] !== removedChip[this.valuePropertyName],
    );
    this.control.setValue(
      updatedOptions.map((option) => option[this.valuePropertyName]),
    );
    this.selectedOptionsCounter = this.calculateSelectedOptions();
    this.filterOptions(removedChip);

    this.chipRemoved.emit(removedChip);
  }

  protected toggleOptionsPanel(e: Event) {
    e.stopPropagation();

    if (!this.autocomplete) {
      this.matSelect?.toggle();
    }

    if (this.autocomplete) {
      this.toggleAutocompletePanel();
    }
  }

  private toggleAutocompletePanel() {
    if (this.autocompleteField?.isOpen) {
      this.matAutocompleteTrigger?.closePanel();
    } else {
      this.matAutocompleteTrigger?.openPanel();
      this.autocompleteInputRef?.nativeElement.focus();
    }
  }

  private prepareOptions() {
    this.options = uniqBy(this.options, this.valuePropertyName);
    this.allOptions = this.options;
    if (this.control.value) {
      this.filterOptions();
    }
  }

  private filterOptions(removedOption?: FinDropdownOption) {
    if (!removedOption && isArray(this.control.value)) {
      const selectedValues = this.control.value;
      const options = this.options.filter(
        (option) => !selectedValues.includes(option[this.valuePropertyName]),
      );

      this.selectedOptions = this.allOptions.filter(
        (option) => !options.includes(option),
      );
    }

    if (removedOption) {
      this.selectedOptions = this.selectedOptions.filter(
        (element) =>
          element[this.valuePropertyName] !==
          removedOption[this.valuePropertyName],
      );
    }
  }

  protected compareFn = (
    optionValue: unknown,
    selectedOptionValue: unknown,
  ) => {
    return optionValue && selectedOptionValue && isObject(optionValue)
      ? JSON.stringify(optionValue) === JSON.stringify(selectedOptionValue)
      : optionValue === selectedOptionValue;
  };

  protected checkVisibility(
    selectedOptions: FinDropdownOption[],
    option: FinDropdownOption,
  ): boolean {
    return !!selectedOptions.find((selectedOption) => {
      return isEqual(selectedOption, option);
    });
  }

  protected onAutocompleteClosed() {
    this.finScrollbarService.enable();
  }
  protected onAutocompleteOpened() {
    this.finScrollbarService.disable();
  }

  private calculateSelectedOptions() {
    const triggerLabel = this.triggerLabel?.nativeElement;
    const selectedOptions = this.getSelectedLabels();
    let visibleOptions = 0;

    if (triggerLabel) {
      selectedOptions.forEach((option: string, index: number) => {
        // On each iteration, convert the options to a string. For example:
        // index = 0 -> Option 1
        // index = 1 -> Option 1, Option 2
        const visibleText = selectedOptions
          .slice(0, index + 1)
          .join(this.triggerDelimiter);

        // Assign the text to the trigger content
        triggerLabel.textContent = visibleText;

        // Check if the content width is less than or equal to the scroll width.
        // If so, the element is visible.
        if (triggerLabel.scrollWidth <= triggerLabel.clientWidth) {
          visibleOptions++;
        }
      });

      if (!selectedOptions.length) {
        triggerLabel.textContent = '';
      }
    }

    // The non-visible options are all options minus the visible ones
    return selectedOptions.length - visibleOptions;
  }

  private getSelectedLabels(): string[] {
    const controlValue: unknown[] = this.control.value || this.selectedOptions;
    return controlValue
      .map((value) => {
        const match = this.options.find(
          (option) =>
            JSON.stringify(option[this.valuePropertyName]) ===
            JSON.stringify(value),
        );
        return match ? match[this.labelPropertyName] : null;
      })
      .filter(Boolean) as string[];
  }

  private setCurrentControlValue(option?: any) {
    if (option) {
      this.currentControlValue = option;
    } else {
      this.currentControlValue = this.control.value;
    }
  }

  protected controlHasNoValue(value: any) {
    return !(value?.length ?? isObject(value));
  }

  protected isOptionSelected(
    value: any,
    currentControlValue: any,
    multiple: boolean | null,
  ) {
    if (multiple) {
      return currentControlValue?.includes(value);
    }

    return JSON.stringify(value) === JSON.stringify(currentControlValue);
  }
}
