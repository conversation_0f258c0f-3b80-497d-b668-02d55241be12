import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FinDropdownComponent } from './dropdown.component';
import { FinDropdownOption } from '../../models/fin-dropdown-option';

describe('DropdownComponent', () => {
  let component: FinDropdownComponent;
  let fixture: ComponentFixture<FinDropdownComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FinDropdownComponent],
    }).compileComponents();

    fixture = TestBed.createComponent<FinDropdownComponent<FinDropdownOption<any>>>(FinDropdownComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
