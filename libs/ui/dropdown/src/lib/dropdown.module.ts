import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FinDropdownComponent } from './components/dropdown/dropdown.component';
import { FinChipPrefixDirective } from './directives/fin-chip-prefix.directive';
import { FinChipSuffixDirective } from './directives/fin-chip-suffix.directive';
import { FinInitialMessageDirective } from './directives/fin-initial-message.directive';
import { FinNoResultsMessageDirective } from './directives/fin-no-results-message.directive';
import { FinOptionLabelDirective } from './directives/fin-option-label.directive';
import { FinOptionPrefixDirective } from './directives/fin-option-prefix.directive';
import { FinOptionSuffixDirective } from './directives/fin-option-suffix.directive';
import { FinWordExtractorPipe } from './pipes/fin-word-extractor.pipe';

@NgModule({
  imports: [
    CommonModule,
    FinDropdownComponent,
    FinOptionSuffixDirective,
    FinOptionPrefixDirective,
    FinOptionLabelDirective,
    FinChipPrefixDirective,
    FinChipSuffixDirective,
    FinWordExtractorPipe,
    FinInitialMessageDirective,
    FinNoResultsMessageDirective,
  ],
  exports: [
    FinDropdownComponent,
    FinOptionSuffixDirective,
    FinOptionPrefixDirective,
    FinOptionLabelDirective,
    FinChipPrefixDirective,
    FinChipSuffixDirective,
    FinWordExtractorPipe,
    FinInitialMessageDirective,
    FinNoResultsMessageDirective,
  ],
})
export class FinDropdownModule {}
