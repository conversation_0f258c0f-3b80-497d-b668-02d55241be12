import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'finWordExtractor',
  standalone: true,
})
export class FinWordExtractorPipe implements PipeTransform {
  transform(value: any, position: 'first' | 'last'): string {
    if (value) {
      const option = value.trim().split(' ');
      if (position === 'first') {
        return option[0];
      } else if (position === 'last') {
        return option[option.length - 1];
      }
    }

    return '';
  }
}
