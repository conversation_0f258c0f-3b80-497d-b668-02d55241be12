# Dropdown Component

A comprehensive dropdown component for selecting options from a list. Supports single and multiple selection, autocomplete functionality, custom templates, chips mode, and extensive customization options.

## Installation

```bash
npm install @fincloud/ui
```

```typescript
import { FinDropdownModule } from '@fincloud/ui/dropdown';

@Component({
  imports: [FinDropdownModule],
  // ...
})
export class YourComponent {}
```

## Basic Usage

### Simple Dropdown

```html
<fin-dropdown label="Select Option" placeholder="Choose an option" [formControl]="optionControl" [options]="options"> </fin-dropdown>
```

```typescript
import { Component } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { FinDropdownModule, FinDropdownOption } from '@fincloud/ui/dropdown';

@Component({
  selector: 'app-example',
  imports: [FinDropdownModule, ReactiveFormsModule],
  template: ` <fin-dropdown label="Select Option" placeholder="Choose an option" [formControl]="optionControl" [options]="options"> </fin-dropdown> `,
})
export class ExampleComponent {
  optionControl = new FormControl('');

  options: FinDropdownOption[] = [
    { label: 'Option 1', value: 'option1' },
    { label: 'Option 2', value: 'option2' },
    { label: 'Option 3', value: 'option3' },
  ];
}
```

## API Reference

### FinDropdownComponent

#### Selector

- `fin-dropdown`

#### Inputs

| Name                    | Type                                   | Default     | Description                                                          |
| ----------------------- | -------------------------------------- | ----------- | -------------------------------------------------------------------- |
| `label`                 | `string`                               | `''`        | Label for the dropdown field                                         |
| `placeholder`           | `string`                               | `''`        | Placeholder text for the dropdown                                    |
| `size`                  | `FinSize.M \| FinSize.L`               | `FinSize.M` | Size of the dropdown field                                           |
| `options`               | `FinDropdownOption[]`                  | `[]`        | Array of options to display                                          |
| `labelPropertyName`     | `string`                               | `'label'`   | Custom property name for option labels                               |
| `valuePropertyName`     | `string`                               | `'value'`   | Custom property name for option values                               |
| `multiple`              | `boolean`                              | `false`     | Allow multiple option selection                                      |
| `showChips`             | `boolean`                              | `false`     | Display selected options as chips                                    |
| `autocomplete`          | `boolean`                              | `false`     | Enable autocomplete functionality                                    |
| `readonly`              | `boolean`                              | `false`     | Switch to readonly mode                                              |
| `messageThreshold`      | `number`                               | `3`         | Minimum characters to switch between initial and no results messages |
| `hideArrow`             | `boolean`                              | `false`     | Hide the dropdown arrow (deprecated)                                 |
| `inputDisplayFn`        | `(value: any) => string`               | `undefined` | Custom display function for input                                    |
| `dynamicErrorSpace`     | `FinErrorSpace`                        | `undefined` | Error space configuration                                            |
| `externalFieldMessages` | `FinFieldMessageBodyDirective \| null` | `null`      | External field messages                                              |

#### Outputs

| Name                      | Type                              | Description                                   |
| ------------------------- | --------------------------------- | --------------------------------------------- |
| `selectionChange`         | `EventEmitter<any>`               | Emitted when the selected value changes       |
| `autoCompleteInputChange` | `EventEmitter<string>`            | Emitted when autocomplete input value changes |
| `chipRemoved`             | `EventEmitter<FinDropdownOption>` | Emitted when a chip is removed                |

#### Content Projection

| Slot                    | Description                                |
| ----------------------- | ------------------------------------------ |
| `[finFieldLabel]`       | Custom label content                       |
| `[finFieldPrefix]`      | Content displayed as prefix in the field   |
| `[finFieldSuffix]`      | Content displayed as suffix in the field   |
| `[finFieldHint]`        | Hint text displayed below the field        |
| `[finInitialMessage]`   | Custom initial message for autocomplete    |
| `[finNoResultsMessage]` | Custom no results message for autocomplete |

### Directives

#### finOptionPrefix

Template directive for customizing option prefix content.

**Selector:** `[finOptionPrefix]`

**Usage:**

```html
<ng-template [finOptionPrefix]="options" let-option>
  <fin-icon [name]="option.icon"></fin-icon>
</ng-template>
```

#### finOptionSuffix

Template directive for customizing option suffix content.

**Selector:** `[finOptionSuffix]`

**Usage:**

```html
<ng-template [finOptionSuffix]="options" let-option>
  <fin-badge [text]="option.count"></fin-badge>
</ng-template>
```

#### finOptionLabel

Template directive for customizing option label content.

**Selector:** `[finOptionLabel]`

**Usage:**

```html
<ng-template [finOptionLabel]="options" let-option>
  <strong>{{ option.title }}</strong>
  <p>{{ option.description }}</p>
</ng-template>
```

#### finChipPrefix

Template directive for customizing chip prefix content.

**Selector:** `ng-template[finChipPrefix]`

**Usage:**

```html
<ng-template [finChipPrefix]="options" let-option>
  <fin-avatar [name]="option.label"></fin-avatar>
</ng-template>
```

#### finChipSuffix

Template directive for customizing chip suffix content.

**Selector:** `ng-template[finChipSuffix]`

**Usage:**

```html
<ng-template [finChipSuffix]="options" let-option>
  <fin-icon name="close"></fin-icon>
</ng-template>
```

#### finInitialMessage

Directive for custom initial message content.

**Selector:** `[finInitialMessage]`

#### finNoResultsMessage

Directive for custom no results message content.

**Selector:** `[finNoResultsMessage]`

## Configuration Options

### FinDropdownOption Interface

```typescript
interface FinDropdownOption<V = any> {
  [key: string]: unknown;
  value?: V;
  label?: string;
  // Additional custom properties
}
```

### Custom Messages Configuration

Configure default messages using the `FIN_CUSTOM_MESSAGES` injection token:

```typescript
import { FIN_CUSTOM_MESSAGES } from '@fincloud/ui/dropdown';

providers: [
  {
    provide: FIN_CUSTOM_MESSAGES,
    useValue: {
      initialMessage: 'Start typing to search...',
      noResultsMessage: 'No matching options found',
      initialSearchMessage: 'Enter search term',
      searchPlaceholder: 'Search options',
      result: 'result',
      results: 'results',
    },
  },
];
```

### Size Options

Available sizes from `@fincloud/ui/types`:

- `FinSize.M` - Medium (default)
- `FinSize.L` - Large

## Advanced Examples

### Multiple Selection with Chips

```html
<fin-dropdown label="Select Multiple Options" placeholder="Choose options" [formControl]="multipleControl" [options]="options" [multiple]="true" [showChips]="true"> </fin-dropdown>
```

```typescript
export class MultipleSelectionComponent {
  multipleControl = new FormControl(['option1', 'option3']);

  options: FinDropdownOption[] = [
    { label: 'Option 1', value: 'option1' },
    { label: 'Option 2', value: 'option2' },
    { label: 'Option 3', value: 'option3' },
    { label: 'Option 4', value: 'option4' },
  ];
}
```

### Autocomplete with Custom Messages

```html
<fin-dropdown label="Search Options" placeholder="Type to search" [formControl]="searchControl" [options]="filteredOptions" [autocomplete]="true" [messageThreshold]="2">
  <ng-container finInitialMessage> Start typing to find options... </ng-container>

  <ng-container finNoResultsMessage> No options match your search </ng-container>
</fin-dropdown>
```

```typescript
export class AutocompleteComponent {
  searchControl = new FormControl('');
  allOptions: FinDropdownOption[] = [
    { label: 'Apple', value: 'apple' },
    { label: 'Banana', value: 'banana' },
    { label: 'Cherry', value: 'cherry' },
  ];
  filteredOptions: FinDropdownOption[] = [];

  constructor() {
    this.searchControl.valueChanges.subscribe((value) => {
      this.filterOptions(value);
    });
  }

  private filterOptions(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredOptions = this.allOptions;
      return;
    }

    this.filteredOptions = this.allOptions.filter((option) => option.label?.toLowerCase().includes(searchTerm.toLowerCase()));
  }
}
```

### Custom Option Templates

```html
<fin-dropdown label="Users" placeholder="Select users" [formControl]="userControl" [options]="users" [showChips]="true">
  <!-- Custom option prefix with avatar -->
  <ng-template [finOptionPrefix]="users" let-option>
    <fin-avatar-default [firstName]="option.firstName" [lastName]="option.lastName" [size]="'xs'"> </fin-avatar-default>
  </ng-template>

  <!-- Custom option label with name and email -->
  <ng-template [finOptionLabel]="users" let-option>
    <div class="fin-flex fin-flex-col">
      <span class="fin-font-semibold">{{ option.name }}</span>
      <span class="fin-text-sm fin-text-gray-500">{{ option.email }}</span>
    </div>
  </ng-template>

  <!-- Custom option suffix with status -->
  <ng-template [finOptionSuffix]="users" let-option>
    <fin-badge [text]="option.status" [appearance]="option.status === 'active' ? 'success' : 'warning'"> </fin-badge>
  </ng-template>

  <!-- Custom chip prefix for selected items -->
  <ng-template [finChipPrefix]="users" let-option>
    <fin-avatar-default [firstName]="option.firstName" [lastName]="option.lastName" [size]="'xs'"> </fin-avatar-default>
  </ng-template>
</fin-dropdown>
```

```typescript
export class CustomTemplateComponent {
  userControl = new FormControl([]);

  users: FinDropdownOption[] = [
    {
      label: 'John Doe',
      value: 'john',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      status: 'active',
    },
    {
      label: 'Jane Smith',
      value: 'jane',
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      status: 'inactive',
    },
  ];
}
```

### Custom Property Names

```html
<fin-dropdown label="Products" placeholder="Select products" [formControl]="productControl" [options]="products" [labelPropertyName]="'name'" [valuePropertyName]="'id'"> </fin-dropdown>
```

```typescript
export class CustomPropertiesComponent {
  productControl = new FormControl('');

  products = [
    { id: 1, name: 'Product A', category: 'Electronics' },
    { id: 2, name: 'Product B', category: 'Clothing' },
    { id: 3, name: 'Product C', category: 'Books' },
  ];
}
```

### Event Handling

```html
<fin-dropdown label="Options" [formControl]="eventControl" [options]="options" [autocomplete]="true" [showChips]="true" (selectionChange)="onSelectionChange($event)" (autoCompleteInputChange)="onInputChange($event)" (chipRemoved)="onChipRemoved($event)"> </fin-dropdown>
```

```typescript
export class EventHandlingComponent {
  eventControl = new FormControl([]);
  options: FinDropdownOption[] = [
    { label: 'Option 1', value: 'opt1' },
    { label: 'Option 2', value: 'opt2' },
    { label: 'Option 3', value: 'opt3' },
  ];

  onSelectionChange(value: any): void {
    console.log('Selection changed:', value);
  }

  onInputChange(searchTerm: string): void {
    console.log('Input changed:', searchTerm);
    // Implement server-side filtering here
  }

  onChipRemoved(option: FinDropdownOption): void {
    console.log('Chip removed:', option);
  }
}
```

## Best Practices

### Do's

- ✅ Use reactive forms with FormControl for proper validation and state management
- ✅ Provide clear, descriptive labels and placeholders
- ✅ Use appropriate option data structure with consistent property names
- ✅ Implement proper filtering for autocomplete functionality
- ✅ Use custom templates for complex option layouts
- ✅ Handle selection change events for dynamic behavior
- ✅ Use chips mode for multiple selection when appropriate
- ✅ Provide custom messages for better user experience

### Don'ts

- ❌ Don't use template-driven forms for complex validation
- ❌ Don't forget to provide labels for accessibility
- ❌ Don't use generic placeholder text like "Select option"
- ❌ Don't ignore option filtering performance with large datasets
- ❌ Don't overuse custom templates for simple dropdowns
- ❌ Don't forget to handle loading states for async data

### Accessibility Guidelines

- Ensure all dropdowns have associated labels
- Use proper ARIA attributes for screen readers
- Support keyboard navigation (arrow keys, enter, escape)
- Provide sufficient color contrast for all states
- Include descriptive option text
- Use semantic HTML structure

### Performance Tips

- Use OnPush change detection strategy (already implemented)
- Implement virtual scrolling for large option lists
- Debounce autocomplete input changes
- Use trackBy functions for option lists
- Lazy load options when possible
- Cache filtered results for better performance

## Troubleshooting

### Common Issues

#### Issue: Options not displaying

**Solution:** Ensure the options array is properly formatted and the component is imported.

```typescript
// Correct option structure
options: FinDropdownOption[] = [
  { label: 'Option 1', value: 'opt1' },
  { label: 'Option 2', value: 'opt2' }
];

// Ensure module is imported
@Component({
  imports: [FinDropdownModule],
  // ...
})
```

#### Issue: Custom templates not rendering

**Solution:** Verify template syntax and ensure the directive is properly applied.

```html
<!-- Correct template usage -->
<ng-template [finOptionPrefix]="options" let-option>
  <fin-icon [name]="option.icon"></fin-icon>
</ng-template>
```

#### Issue: Autocomplete not filtering

**Solution:** Ensure you're updating the options array based on input changes.

```typescript
// Implement proper filtering
onInputChange(searchTerm: string): void {
  this.filteredOptions = this.allOptions.filter(option =>
    option.label?.toLowerCase().includes(searchTerm.toLowerCase())
  );
}
```

#### Issue: Multiple selection not working

**Solution:** Ensure both `multiple` and `showChips` are set correctly.

```html
<fin-dropdown [multiple]="true" [showChips]="true" [formControl]="multipleControl"> </fin-dropdown>
```

#### Issue: Custom messages not showing

**Solution:** Provide the FIN_CUSTOM_MESSAGES token or use directive-based messages.

```typescript
// Using injection token
providers: [
  {
    provide: FIN_CUSTOM_MESSAGES,
    useValue: { initialMessage: 'Custom message' },
  },
];

// Or using directives
```

```html
<fin-dropdown>
  <ng-container finInitialMessage>Custom initial message</ng-container>
</fin-dropdown>
```

## Related Components

- [Input](../input/README.md) - For text input fields
- [Button](../button/README.md) - For action buttons in dropdowns
- [Icon](../icon/README.md) - For option icons and indicators
- [Badge](../badges/README.md) - For option status indicators
- [Avatar](../avatar/README.md) - For user option displays
- [Checkbox](../checkbox/README.md) - For multiple selection alternatives
- [Radio](../radio/README.md) - For single selection alternatives

## Storybook

View interactive examples and documentation in Storybook:

- [Dropdown Component](https://lib-ui.neoshare.dev/?path=/docs/fields-dropdown--docs)

## Changelog

See [CHANGELOG.md](../../CHANGELOG.md) for version history and breaking changes.

## Contributing

See [CONTRIBUTING.md](../../CONTRIBUTING.md) for guidelines on contributing to this component.

## License

This component is part of the @fincloud/ui library and is licensed under [MIT License](../../LICENSE).
