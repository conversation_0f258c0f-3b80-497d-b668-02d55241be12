import {
  componentWrapperDecorator,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { FinProgressBarComponent } from './progress-bar.component';

const meta: Meta<FinProgressBarComponent> = {
  component: FinProgressBarComponent,
  title: 'Components/Progress Bar',
  decorators: [
    componentWrapperDecorator(
      (story) =>
        `<div class="fin-flex fin-items-center fin-justify-center fin-h-80 fin-w-[60rem]">${story}</div>`,
    ),
  ],
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinProgressBarModule } from "@fincloud/ui/progress-bar"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6547-16746&m=dev',
    },
  },
};

export default meta;

type Story = StoryObj<FinProgressBarComponent>;

export const Empty: Story = {
  args: {
    segments: [],
  },
  argTypes: {
    selectedIndex: {
      control: false,
      type: 'function',
    },
    min: {
      control: { type: 'number' },
    },
    max: {
      control: { type: 'number' },
    },
  },
};

export const HalfFull: Story = {
  args: {
    segments: [
      {
        width: 50,
        color: 'fin-bg-color-background-success-strong',
        tooltip: {
          mainText: 'Tooltip segment',
          secondaryText: '2.00 €',
        },
      },
    ],
  },
};

export const WithSegments: Story = {
  args: {
    baseColor: 'fin-bg-color-transparency-primary-subtle',
    segments: [
      {
        width: 20,
        color: 'fin-bg-color-background-success-strong',
        tooltip: {
          mainText: 'Tooltip segment',
        },
      },
      {
        width: 30,
        color: 'fin-bg-color-brand-primary',
        tooltip: {
          mainText: 'Tooltip segment 2',
        },
      },
    ],
  },
  argTypes: {
    ...Empty.argTypes,
  },
  parameters: {
    ...Empty.parameters,
  },
};

export const DifferentColors: Story = {
  args: {
    baseColor: 'fin-bg-color-transparency-secondary-minimal',
    segments: [
      {
        width: 20,
        color: 'fin-bg-color-background-secondary-moderate',
        tooltip: {
          mainText: 'Tooltip segment',
        },
      },
      {
        width: 30,
        color: 'fin-bg-color-background-secondary-strong',
        tooltip: {
          mainText: 'Tooltip segment 2',
        },
      },
    ],
  },
  argTypes: {
    ...Empty.argTypes,
  },
  parameters: {
    ...Empty.parameters,
  },
};

export const MinAndMax: Story = {
  args: {
    ...WithSegments.args,
    min: 12,
    max: 25,
    amountSuffix: ' %',
  },
  argTypes: {
    ...WithSegments.argTypes,
  },
  parameters: {
    ...WithSegments.parameters,
  },
  render: (args) => ({
    props: { ...args, minAmount: 12, maxAmount: 25 },
    template: `
      <fin-progress-bar
        [segments]="segments"
        [baseColor]="baseColor"
        [min]="min"
        [max]="max"
        [amountSuffix]="amountSuffix"
      > 
      </fin-progress-bar>
     `,
  }),
};

export const NumberPipe: Story = {
  args: {
    ...MinAndMax.args,
  },
  argTypes: {
    ...MinAndMax.argTypes,
  },
  parameters: {
    ...MinAndMax.parameters,
  },
  render: (args) => ({
    props: { ...args, minAmount: 12, maxAmount: 25 },
    template: `
      <fin-progress-bar
        [segments]="segments"
        [baseColor]="baseColor"
        [min]="min | number: '1.2-2'"
        [max]="max | number: '1.2-2'"
        [amountSuffix]="amountSuffix"
      > 
      </fin-progress-bar>
     `,
  }),
};
