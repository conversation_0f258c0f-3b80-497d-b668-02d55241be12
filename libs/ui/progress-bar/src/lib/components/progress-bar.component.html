<div
  [ngClass]="baseColor"
  class="fin-w-full fin-h-[0.8rem] fin-flex fin-flex-row in-overflow-hidden fin-rounded-l-2xl fin-rounded-r-2xl"
>
  @for (segment of segments; track segment; let i = $index) {
    <div
      finTooltip
      [showArrow]="true"
      class="last:fin-rounded-r-2xl first:fin-rounded-l-2xl fin-cursor-pointer"
      [ngClass]="segment.color"
      [style.width.%]="segment.width"
      [content]="segment.tooltip ? tooltipTemplate : null"
      [context]="{ tooltip: segment.tooltip }"
      (click)="onSelectSegment(i)"
    ></div>
  }

  <ng-template #tooltipTemplate let-tooltip="tooltip">
    <div>{{ tooltip.mainText }}</div>
    @if (tooltip.secondaryText) {
      {{ tooltip.secondaryText }}
    }
  </ng-template>
</div>

<div class="fin-flex fin-justify-between">
  @if (min || min === 0) {
    <div
      class="fin-rounded-[0.4rem] fin-px-[0.8rem] fin-py-[0.3rem] fin-mt-[0.8rem] fin-text-body-3-strong fin-text-color-text-secondary fin-bg-color-background-tertiary-minimal"
    >
      {{ min }}{{ amountSuffix }}
    </div>
  }

  @if (max || max === 0) {
    <div
      class="fin-rounded-[0.4rem] fin-px-[0.8rem] fin-py-[0.3rem] fin-mt-[0.8rem] fin-text-body-3-strong fin-text-color-text-secondary fin-bg-color-background-tertiary-minimal"
    >
      {{ max }}{{ amountSuffix }}
    </div>
  }
</div>
