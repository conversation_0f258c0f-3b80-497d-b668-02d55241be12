import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  numberAttribute,
  Output,
} from '@angular/core';
import { FinTooltipModule } from '@fincloud/ui/tooltip';
import { FinProgressBarOption } from '../models/fin-progress-bar-option';
// eslint-disable-next-line @fincloud/ns/no-declare-constant

/**
 * A visual indicator of progress towards a goal or completion.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-progress-bar--docs Storybook Reference}
 */
@Component({
  selector: 'fin-progress-bar',
  standalone: true,
  imports: [CommonModule, FinTooltipModule],
  templateUrl: './progress-bar.component.html',
  styleUrl: './progress-bar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'fin-w-full',
  },
})
export class FinProgressBarComponent {
  /** Segment of the progress bar. */
  @Input() segments: FinProgressBarOption[] = [];

  /** The minimum value of the progress bar. */
  @Input({ transform: numberAttribute }) min: number | undefined = undefined;

  /** The maximum value of the progress bar. */
  @Input({ transform: numberAttribute }) max: number | undefined = undefined;

  /** Suffix for the minimum and maximum. */
  @Input() amountSuffix = '';

  /** Underlying color of the progress bar. */
  @Input()
  baseColor: string | undefined = 'fin-bg-color-background-tertiary-minimal';

  /** Event emitted when the segment selection has changed. */
  @Output() selectedIndex = new EventEmitter<number>();

  protected onSelectSegment(index: number) {
    this.selectedIndex.emit(index);
  }
}
