:host {
  @apply fin-flex;
  @apply fin-items-center;
  @apply fin-gap-[1.6rem];
  @apply fin-px-[2.4rem];
  @apply fin-py-[1.2rem];
  @apply fin-min-h-[4.8rem];

  &.fin-warning-basic-bg {
    background-color: theme('colors.color-background-light');
  }
  .fin-warning-basic-icon {
    color: theme('colors.color-icons-dark');
  }

  &.fin-warning-warning-bg {
    background-color: theme('colors.color-background-warning-minimal');
  }
  .fin-warning-warning-icon {
    color: theme('colors.color-icons-warning');
  }

  &.fin-warning-informative-bg {
    background-color: theme('colors.color-background-secondary-minimal');
  }
  .fin-warning-informative-icon {
    color: theme('colors.color-icons-informative');
  }

  &.fin-warning-attention-bg {
    background-color: theme('colors.color-background-attention-minimal');
  }
  .fin-warning-attention-icon {
    color: theme('colors.color-icons-error');
  }

  &.fin-warning-neutral-bg {
    background-color: theme('colors.color-surface-tertiary');
  }
  .fin-warning-neutral-icon {
    color: theme('colors.color-icons-dark');
  }

  &.fin-warning-success-bg {
    background-color: theme('colors.color-background-success-minimal');
  }
  .fin-warning-success-icon {
    color: theme('colors.color-icons-success');
  }

  &.fin-warning-elevation-m {
    box-shadow: 0px 4px 10px 0px theme('colors.color-text-primary' / 16%);
  }

  ::ng-deep {
    .fin-warning-warning-icon path {
      fill: theme('colors.color-icons-warning');
    }
    .fin-warning-basic-icon path {
      color: theme('colors.color-icons-dark');
    }
    .fin-warning-informative-icon path {
      color: theme('colors.color-icons-informative');
    }
    .fin-warning-attention-icon path {
      color: theme('colors.color-icons-error');
    }
    .fin-warning-neutral-icon path {
      color: theme('colors.color-icons-dark');
    }
    .fin-warning-success-icon path {
      color: theme('colors.color-icons-success');
    }
  }
}
