import { FinButtonModule } from '@fincloud/ui/button';
import {
  Args,
  componentWrapperDecorator,
  Meta,
  moduleMetadata,
  StoryObj,
} from '@storybook/angular';
import { FinWarningMessageAppearance } from '../enums/fin-warning-message-appearance';
import { FinWarningMessageComponent } from './warning-message.component';

const meta: Meta<FinWarningMessageComponent> = {
  component: FinWarningMessageComponent,
  title: 'Components/Warning Message',
  decorators: [
    moduleMetadata({
      imports: [FinButtonModule],
    }),
    componentWrapperDecorator(
      (story) => `<div class="fin-w-[532px]">${story}</div>`,
    ),
  ],
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinWarningMessageModule } from "@fincloud/ui/warning-message"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=21316-10955&t=4aYqYVD728hKqmX4-4',
    },
  },
};
export default meta;

type Story = StoryObj<FinWarningMessageComponent>;

const renderTemplate = (args: Args) => ({
  props: args,
  template: `
      <fin-warning-message
        [label]="label"
        [showIcon]="showIcon"
        [iconName]="iconName"
        [iconSrc]="iconSrc"
        [appearance]="appearance"
        [showElevation]="showElevation"
        >
      </fin-warning-message>
    `,
});

const renderTemplateWithContent = (args: Args) => ({
  props: args,
  template: `
      <fin-warning-message
        [label]="label"
        [showIcon]="showIcon"
        [showElevation]="showElevation"
        [appearance]="appearance" showElevation>
        <button fin-button [appearance]="'secondary'">Button</button>
      </fin-warning-message>
    `,
});

export const Basic: Story = {
  args: {
    label: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    showIcon: true,
    appearance: FinWarningMessageAppearance.BASIC,
  },
  argTypes: {
    label: {
      control: 'text',
    },
    showIcon: {
      options: [true, false],
      control: { type: 'radio' },
    },
    iconName: {
      control: 'text',
    },
    iconSrc: {
      control: 'text',
    },
    showElevation: {
      options: [true, false],
      control: { type: 'radio' },
    },
    appearance: {
      control: { type: 'select' },
      options: [...Object.values(FinWarningMessageAppearance)],
    },
  },
};

export const Warning: Story = {
  args: {
    ...Basic.args,
    appearance: FinWarningMessageAppearance.WARNING,
  },
  argTypes: {
    ...Basic.argTypes,
  },
  parameters: {
    ...Basic.parameters,
  },
  render: (args) => renderTemplate(args),
};
export const Informative: Story = {
  args: {
    ...Basic.args,
    appearance: FinWarningMessageAppearance.INFORMATIVE,
  },
  argTypes: {
    ...Basic.argTypes,
  },
  parameters: {
    ...Basic.parameters,
  },
  render: (args) => renderTemplate(args),
};

export const Attention: Story = {
  args: {
    ...Basic.args,
    appearance: FinWarningMessageAppearance.ATTENTION,
  },
  argTypes: {
    ...Basic.argTypes,
  },
  parameters: {
    ...Basic.parameters,
  },
  render: (args) => renderTemplate(args),
};

export const Neutral: Story = {
  args: {
    ...Basic.args,
    appearance: FinWarningMessageAppearance.NEUTRAL,
  },
  argTypes: {
    ...Basic.argTypes,
  },
  parameters: {
    ...Basic.parameters,
  },
  render: (args) => renderTemplate(args),
};

export const Success: Story = {
  args: {
    ...Basic.args,
    appearance: FinWarningMessageAppearance.SUCCESS,
  },
  argTypes: {
    ...Basic.argTypes,
  },
  parameters: {
    ...Basic.parameters,
  },
  render: (args) => renderTemplate(args),
};

export const WithoutIcon: Story = {
  args: {
    ...Basic.args,
    showIcon: false,
  },
  argTypes: {
    ...Basic.argTypes,
  },
  parameters: {
    ...Basic.parameters,
  },
  render: (args) => renderTemplate(args),
};

export const WithCustomIcon: Story = {
  args: {
    ...Basic.args,
    appearance: FinWarningMessageAppearance.WARNING,
    iconSrc: '/assets/storybook/input/chat-bubble.svg',
  },
  argTypes: {
    ...Basic.argTypes,
  },
  parameters: {
    ...Basic.parameters,
  },
  render: (args) => renderTemplate(args),
};

export const WithElevation: Story = {
  args: {
    ...Basic.args,
    showIcon: true,
    showElevation: true,
  },
  argTypes: {
    ...Basic.argTypes,
  },
  parameters: {
    ...Basic.parameters,
  },
  render: (args) => renderTemplate(args),
};

export const WithContent: Story = {
  args: {
    ...Basic.args,
    appearance: FinWarningMessageAppearance.INFORMATIVE,
    label:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    showIcon: true,
  },
  argTypes: {
    ...Basic.argTypes,
  },
  parameters: {
    ...Basic.parameters,
  },
  render: (args) => renderTemplateWithContent(args),
};
