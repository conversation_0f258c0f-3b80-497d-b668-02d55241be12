import {
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  Input,
  OnInit,
} from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';
import { FinAngularMaterialModule } from '@fincloud/utils/angular-material';
import { FinWarningMessageAppearance } from '../enums/fin-warning-message-appearance';
import { FIN_WARNING_MESSAGE_ICON_MAPPING } from '../utils/fin-warning-message-mapping';
/**
 * Displays a message to alert users of potential issues or important information.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-warning-message--docs Storybook Reference}
 */
@Component({
  selector: 'fin-warning-message',
  standalone: true,
  imports: [MatIconModule, FinAngularMaterialModule, FinIconModule],
  templateUrl: './warning-message.component.html',
  styleUrl: './warning-message.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'fin-warning-message',
    '[class]': 'warningCssClasses',
    '[class.fin-warning-elevation-m]': 'showElevation',
  },
})
export class FinWarningMessageComponent implements OnInit {
  /** Warning message label. */
  @Input() label = '';

  /** Shows the default icon for the current state. */
  @Input() showIcon = true;

  /** Name of an icon within a [Material Icons](https://fonts.google.com/icons) font set. */
  @Input() iconName = '';

  /** URL pointing to the icon source. */
  @Input() iconSrc = '';

  /** Appearance of the warning message. */
  @Input() appearance = FinWarningMessageAppearance.SUCCESS;

  /** Show whether the warning message should have elevation. */
  @Input({ transform: booleanAttribute }) showElevation = false;

  protected iconSize: FinSize = FinSize.L;

  ngOnInit(): void {
    if (this.showIcon && !this.iconName && !this.iconSrc) {
      this.iconName = FIN_WARNING_MESSAGE_ICON_MAPPING[this.appearance];
    }
  }

  protected get warningCssClasses() {
    return `fin-warning-${this.appearance}-bg`;
  }
}
