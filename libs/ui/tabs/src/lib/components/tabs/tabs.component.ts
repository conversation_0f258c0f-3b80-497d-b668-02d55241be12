import { SharedResizeObserver } from '@angular/cdk/observers/private';
import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  booleanAttribute,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ContentChildren,
  DestroyRef,
  ElementRef,
  EventEmitter,
  Input,
  NgZone,
  numberAttribute,
  Output,
  QueryList,
  ViewChild,
  ViewChildren,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  MatTabChangeEvent,
  MatTabGroup,
  MatTabHeader,
  MatTabsModule,
} from '@angular/material/tabs';
import { FinBadgesModule } from '@fincloud/ui/badges';
import { FinSize } from '@fincloud/ui/types';
import { detectChangesOnce } from '@fincloud/utils/angular-functions';
import { emitPerJsFrame } from '@fincloud/utils/rxjs-operators';
import { animationFrameScheduler, delay, observeOn, startWith } from 'rxjs';
import { FinTabType } from '../../enums/fin-tab-type';
import { FinTabChangeEvent } from '../../models/fin-tab-change-event';
import { FIN_TABS } from '../../utils/fin-tabs-token';
import { FinTabComponent } from '../tab/tab.component';
/**
 * A component that organizes content into separate views or sections.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-tabs--docs Storybook Reference}
 */
@Component({
  selector: 'fin-tabs',
  standalone: true,
  imports: [CommonModule, MatTabsModule, FinBadgesModule],
  templateUrl: './tabs.component.html',
  styleUrl: './tabs.component.scss',
  host: {
    class: 'fin-tabs',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: FIN_TABS,
      useExisting: FinTabsComponent,
    },
  ],
})
export class FinTabsComponent implements AfterViewInit {
  /** Specifies the tab mode. */
  @Input() type: FinTabType = FinTabType.PRIMARY;

  /** Specifies the tab height. */
  @Input() size: FinSize.XL | FinSize.L = FinSize.XL;

  /** Specifies the index of the currently selected tab. */
  @Input({ transform: numberAttribute }) selectedIndex = 0;

  /** If set to `true` the tabs will fill the header. */
  @Input({ transform: booleanAttribute }) stretchTabs = false;

  /** If set to `true` the tabs will keep the index of the selected tab on change the tab elements. */
  @Input({ transform: booleanAttribute }) dynamicIndex = true;

  /** Event emitted when focus has changed within a tab group. */
  @Output() focusChange = new EventEmitter<FinTabChangeEvent>();

  /** Event emitted when the tab selection has change. `EventEmitter<number>()`*/
  @Output() selectedIndexChange = new EventEmitter<number>();

  /** Event emitted when the tab selection has change. `EventEmitter<FinTabChangeEvent>()`*/
  @Output() selectedTabChange = new EventEmitter<FinTabChangeEvent>();

  @ViewChild(MatTabGroup) protected tabsGroup!: MatTabGroup;
  @ViewChildren(MatTabHeader, { read: ElementRef })
  protected matPaginatedTabHeader!: QueryList<MatTabHeader>;

  @ContentChildren(FinTabComponent)
  private _tabs: QueryList<FinTabComponent> | undefined;

  protected tabs: FinTabComponent[] = [];
  protected tabType = FinTabType;

  constructor(
    private cd: ChangeDetectorRef,
    private destroyRef: DestroyRef,
    private sharedResizeObserver: SharedResizeObserver,
    private ngZone: NgZone,
  ) {}

  protected onFocusChange(event: MatTabChangeEvent) {
    this.focusChange.emit(event);
  }
  protected onSelectedIndexChange(index: number) {
    this.selectedIndexChange.emit(index);
  }
  protected onSelectedTabChange(event: MatTabChangeEvent) {
    this.selectedTabChange.emit(event);
  }

  ngAfterViewInit(): void {
    this._tabs?.changes
      .pipe(
        emitPerJsFrame(),
        startWith(this._tabs),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((tabs: QueryList<FinTabComponent>) => {
        this.tabs = tabs.toArray();
        detectChangesOnce(this.cd);

        if (this.tabsGroup) {
          this.tabsGroup.selectedIndex = this.selectedIndex;
        }
      });

    this.ngZone.runOutsideAngular(() => {
      // Listens for tabs layout resize and scrolls to the active tabs.
      this.scrollToActiveTabsOnResize();
    });
  }

  private scrollToActiveTabsOnResize() {
    if (this.tabsGroup) {
      this.sharedResizeObserver
        .observe(this.tabsGroup._elementRef.nativeElement)
        .pipe(
          observeOn(animationFrameScheduler),
          delay(100),
          takeUntilDestroyed(this.destroyRef),
        )
        .subscribe(() => {
          window.dispatchEvent(new Event('resize'));
          this.tabsGroup._tabHeader._scrollToLabel(
            this.tabsGroup.selectedIndex ?? 0,
          );
        });
    }
  }
}
