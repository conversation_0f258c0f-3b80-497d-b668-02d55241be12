import {
  AfterContentInit,
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  ContentChild,
  Input,
} from '@angular/core';
import { FinTabBodyDirective } from '../../directives/tab-body.directive';
import { FinTabLabelDirective } from '../../directives/tab-label.directive';

@Component({
  selector: 'fin-tab',
  standalone: true,
  imports: [],
  templateUrl: './tab.component.html',
  styleUrl: './tab.component.scss',
  host: {
    class: 'fin-tab',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  exportAs: 'finTabs',
})
export class FinTabComponent implements AfterContentInit {
  /** Specifies if the tab is disabled. */
  @Input({ transform: booleanAttribute })
  disabled = false;

  /** Specifies if the tab is hidden. */
  @Input({ transform: booleanAttribute }) isHidden = false;

  @ContentChild(FinTabBodyDirective)
  tabBody!: FinTabBodyDirective;

  @ContentChild(FinTabLabelDirective)
  tabLabel!: FinTabLabelDirective;

  labelText = '';

  ngAfterContentInit(): void {
    const view = this.tabLabel?.template.createEmbeddedView({});
    view.detectChanges();

    this.labelText = this.collectPlainText(view.rootNodes);
    view.destroy();
  }

  private collectPlainText(nodes: Node[]): string {
    let text = '';
    nodes.forEach((node) => {
      if (node.nodeType === Node.TEXT_NODE) {
        if (node.textContent && node.textContent !== 'container') {
          text = node.textContent;
        }
      }
      if (node.hasChildNodes()) {
        text = this.collectPlainText(Array.from(node.childNodes));
      }
    });
    return text;
  }
}
