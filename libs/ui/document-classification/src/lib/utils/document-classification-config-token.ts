import { InjectionToken } from '@angular/core';
import { DocumentClassificationConfig } from '../models/document-classification-loading-config';

/**
 * Injection token for document classification loading configuration
 *
 * This token supports hierarchical configuration - you can provide different
 * configurations at different levels (app, module, component) for maximum flexibility.
 *
 */
export const DOCUMENT_CLASSIFICATION_LOADING_CONFIG =
  new InjectionToken<DocumentClassificationConfig>(
    'DOCUMENT_CLASSIFICATION_LOADING_CONFIG',
  );
