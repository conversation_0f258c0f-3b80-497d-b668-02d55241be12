import { CommonModule } from '@angular/common';
import { FormControl, Validators } from '@angular/forms';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinCheckboxModule } from '@fincloud/ui/checkbox';
import { FinFieldMessageModule } from '@fincloud/ui/field-message';
import { FinIconModule } from '@fincloud/ui/icon';
import {
  FIN_CURRENCY_MASK,
  FIN_DECIMAL_MASK,
  FIN_INTEGER_MASK,
  FIN_MONTHS_MASK,
  FIN_PERCENTAGE_MASK,
  FIN_REGION_LOCALE_ID,
  LocaleId,
} from '@fincloud/ui/input';
import {
  componentWrapperDecorator,
  moduleMetadata,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { NgxCurrencyInputMode } from 'ngx-currency';
import { FinDocumentClassificationModule } from '../../document-classification.module';
import { FinDocumentClassificationComponent } from './document-classification.component';

const MASK_CONFIG_BASE = {
  [LocaleId.DE]: {
    align: 'left',
    allowNegative: false,
    allowZero: true,
    decimal: ',',
    thousands: '.',
    nullable: true,
    min: null,
    max: null,
    inputMode: NgxCurrencyInputMode.Natural,
    precision: 0,
    prefix: '',
    suffix: '',
  },
  [LocaleId.EN]: {
    align: 'left',
    allowNegative: false,
    allowZero: true,
    decimal: '.',
    thousands: ',',
    nullable: true,
    min: null,
    max: null,
    inputMode: NgxCurrencyInputMode.Natural,
    precision: 0,
    prefix: '',
    suffix: '',
  },
};
const CURRENCY_MASK_CONFIG = {
  [LocaleId.DE]: {
    ...MASK_CONFIG_BASE[LocaleId.DE],
    precision: 2,
    suffix: ' €',
  },
  [LocaleId.EN]: {
    ...MASK_CONFIG_BASE[LocaleId.EN],
    precision: 2,
    prefix: '€',
  },
};
const PERCENTAGE_MASK_CONFIG = {
  [LocaleId.DE]: {
    ...MASK_CONFIG_BASE[LocaleId.DE],
    precision: 3,
    suffix: ' %',
  },
  [LocaleId.EN]: {
    ...MASK_CONFIG_BASE[LocaleId.EN],
    precision: 3,
    suffix: ' %',
  },
};
const DECIMAL_MASK_CONFIG = {
  [LocaleId.DE]: {
    ...MASK_CONFIG_BASE[LocaleId.DE],
    precision: 2,
  },
  [LocaleId.EN]: {
    ...MASK_CONFIG_BASE[LocaleId.EN],
    precision: 2,
  },
};

const meta: Meta<FinDocumentClassificationComponent> = {
  component: FinDocumentClassificationComponent,
  title: 'Components/Document Classification',
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        FinDocumentClassificationModule,
        FinCheckboxModule,
        FinIconModule,
        FinButtonModule,
        FinFieldMessageModule,
        FinButtonModule,
      ],
      providers: [
        {
          provide: FIN_CURRENCY_MASK,
          useValue: CURRENCY_MASK_CONFIG,
        },
        {
          provide: FIN_PERCENTAGE_MASK,
          useValue: PERCENTAGE_MASK_CONFIG,
        },
        {
          provide: FIN_DECIMAL_MASK,
          useValue: DECIMAL_MASK_CONFIG,
        },
        {
          provide: FIN_MONTHS_MASK,
          useValue: MASK_CONFIG_BASE,
        },
        {
          provide: FIN_INTEGER_MASK,
          useValue: MASK_CONFIG_BASE,
        },
        {
          provide: FIN_REGION_LOCALE_ID,
          useValue: LocaleId.DE,
        },
      ],
    }),
    componentWrapperDecorator(
      (story) => `<div class="fin-w-[800px]">${story}</div>`,
    ),
  ],
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinDocumentClassificationModule } from "@fincloud/ui/document-classification"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/QdgzuRY5ZluGdKBU4NQRy9/Inbox?node-id=3734-145233&m=dev',
    },
  },
};
export default meta;

type Story = StoryObj<
  FinDocumentClassificationComponent & {
    finContent: string;
    finPrefix: string;
    finInputPrefix: string;
    finInputSuffix: string;
    finFooterSuffix: string;
  }
>;

const invalidField = new FormControl('', Validators.required);
invalidField.markAsTouched();

const validField = new FormControl('document_name.pdf', Validators.required);
validField.markAsTouched();

const checkboxField = new FormControl(false);

const fullExample = `
    <fin-document-classification
      [formControl]="formControl"
      [placeholder]="placeholder"
      [readonly]="readonly"
      [expanded]="expanded"
      [disabled]="disabled"
      [loading]="loading"
    >
      <ng-template #finPrefix>
        <div (click)="$event.stopPropagation()">
          <fin-checkbox size="s" [formControl]="checkboxField"></fin-checkbox>
        </div>
      </ng-template>

      <ng-template #finInputPrefix>
        <fin-icon src="assets/storybook/icons/ai.svg"></fin-icon>
      </ng-template>

      <ng-template #finContent>
        <div class="fin-flex fin-justify-between fin-items-center">
          <div>Original file name: document_name.pdf</div>

          <div class="fin-flex fin-gap-[0.4rem]">
            <button size="xs" fin-button-icon appearance="informative" shape="rectangle">
              <fin-icon name="preview" class="fin-text-color-icons-dark"></fin-icon>
            </button>

            <button size="xs" fin-button-icon appearance="informative" shape="rectangle">
              <fin-icon name="folder" class="fin-text-color-icons-dark"></fin-icon>
            </button>

            <button size="xs" fin-button-icon appearance="informative" shape="rectangle">
              <fin-icon name="edit" class="fin-text-color-icons-dark"></fin-icon>
            </button>

            <button size="xs" fin-button-icon appearance="informative" shape="rectangle">
              <fin-icon name="more_vert" class="fin-text-color-icons-dark"></fin-icon>
            </button>
          </div>
        </div>
      </ng-template>
    </fin-document-classification>
  `;

const warningExample = `
    <fin-document-classification
      [formControl]="formControl"
      [placeholder]="placeholder"
      [readonly]="readonly"
      [expanded]="expanded"
    >
      <ng-template #finPrefix>
        <div (click)="$event.stopPropagation()">
          <fin-checkbox size="s" [formControl]="checkboxField"></fin-checkbox>
        </div>
      </ng-template>

      <ng-template #finInputPrefix>
        <fin-icon src="assets/storybook/icons/ai.svg"></fin-icon>
      </ng-template>

      <ng-template #finContent>
        Content
      </ng-template>

      <fin-field-messages>
        <ng-template finFieldMessage [type]="type" [errorKey]="errorKey">
          Some message.
        </ng-template>
      </fin-field-messages> 
    </fin-document-classification>
  `;

export const Default: Story = {
  args: {
    expanded: false,
    readonly: false,
    expandDisabled: false,
    loading: false,
    placeholder: '',
  },
  argTypes: {
    finContent: {
      description: 'Place content in the expanded area',
      control: false,
      table: {
        category: 'Templates',
      },
    },
    finPrefix: {
      description: 'Place content before the input',
      control: false,
      table: {
        category: 'Templates',
      },
    },
    finInputPrefix: {
      description: 'Place an input prefix',
      control: false,
      table: {
        category: 'Templates',
      },
    },
    finInputSuffix: {
      description: 'Place an input suffix',
      table: {
        category: 'Templates',
      },
    },
    finFooterSuffix: {
      description: 'Place a footer suffix',
      table: {
        category: 'Templates',
      },
    },
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: validField,
        checkboxField,
      },
      template: `
        <fin-document-classification
          [formControl]="formControl"
          [placeholder]="placeholder"
          [readonly]="readonly"
          [expanded]="expanded"
          [disabled]="disabled"
        >
          <ng-template #finPrefix>
            <div (click)="$event.stopPropagation()">
              <fin-checkbox size="s" [formControl]="checkboxField"></fin-checkbox>
            </div>
          </ng-template>

          <ng-template #finInputPrefix>
            <fin-icon src="assets/storybook/icons/ai.svg"></fin-icon>
          </ng-template>

          <ng-template #finContent>
            Content
          </ng-template>
        </fin-document-classification>
      `,
    };
  },
};

export const ReadOnly: Story = {
  args: {
    ...Default.args,
    readonly: true,
  },
  argTypes: { ...Default.argTypes },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: validField,
        checkboxField,
      },
      template: fullExample,
    };
  },
};

export const Expanded: Story = {
  args: {
    ...Default.args,
    readonly: true,
    expanded: true,
  },
  argTypes: { ...Default.argTypes },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: validField,
        checkboxField,
      },
      template: fullExample,
    };
  },
};

export const DisabledPanel: Story = {
  args: {
    ...Default.args,
    readonly: true,
    expanded: true,
    expandDisabled: true,
  },
  argTypes: { ...Default.argTypes },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: validField,
        checkboxField,
      },
      template: fullExample,
    };
  },
};

export const Loading: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => {
    let loading = true;

    return {
      props: {
        ...args,
        loading,
        formControl: validField,
        checkboxField,
        resetLoadingAnimation() {
          loading = false;
          this['loading'] = loading;

          setTimeout(() => {
            loading = true;
            this['loading'] = loading;
          });
        },
      },
      template: `
        <fin-document-classification
          [formControl]="formControl"
          [placeholder]="placeholder"
          [readonly]="readonly"
          [expanded]="expanded"
          [disabled]="disabled"
          [loading]="loading"
        >
          <ng-template #finPrefix>
            <div (click)="$event.stopPropagation()">
              <fin-checkbox size="s" [formControl]="checkboxField"></fin-checkbox>
            </div>
          </ng-template>

          <ng-template #finInputPrefix>
            <fin-icon src="assets/storybook/icons/ai.svg"></fin-icon>
          </ng-template>

          <ng-template #finContent>
            <div class="fin-flex fin-justify-between fin-items-center">
              Original file name: document_name.pdf
            </div>
          </ng-template>
        </fin-document-classification>

        <button class="fin-mt-[3rem]" fin-button (click)="resetLoadingAnimation()">Reset loading animation</button>
      `,
    };
  },
};

export const WithAffixes: Story = {
  args: {
    ...Default.args,
    expanded: true,
  },
  argTypes: { ...Default.argTypes },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: validField,
        checkboxField,
      },
      template: `
        <fin-document-classification
          [formControl]="formControl"
          [placeholder]="placeholder"
          [readonly]="readonly"
          [expanded]="expanded"
        >
          <ng-template #finPrefix>
            <div (click)="$event.stopPropagation()">
              <fin-checkbox size="s" [formControl]="checkboxField"></fin-checkbox>
            </div>
          </ng-template>

          <ng-template #finInputPrefix>
            <fin-icon src="assets/storybook/icons/ai.svg"></fin-icon>
          </ng-template>

          <ng-template #finInputSuffix>
            <button fin-button size="s" appearance="stealth">Cancel</button>
          </ng-template>

          <ng-template #finContent>
            Content
          </ng-template>

          <ng-template #finFooterSuffix>
            <button fin-button size="s" appearance="stealth" class="fin-mt-[0.4rem]">Ignore</button>
          </ng-template>
        </fin-document-classification>
      `,
    };
  },
};

export const ValidationSuccess: Story = {
  args: {
    ...Default.args,
    readonly: false,
    expanded: false,
  },
  argTypes: { ...Default.argTypes },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: validField,
        checkboxField,
        type: 'success',
        errorKey: '',
      },
      template: warningExample,
    };
  },
};

export const ValidationWarning: Story = {
  args: {
    ...Default.args,
    readonly: false,
    expanded: false,
  },
  argTypes: { ...Default.argTypes },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: invalidField,
        checkboxField,
        type: 'warning',
        errorKey: 'required',
      },
      template: warningExample,
    };
  },
};

export const ValidationError: Story = {
  args: {
    ...Default.args,
    readonly: false,
    expanded: false,
  },
  argTypes: { ...Default.argTypes },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: invalidField,
        checkboxField,
        type: 'error',
        errorKey: 'required',
      },
      template: warningExample,
    };
  },
};
