import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  ContentChild,
  DestroyRef,
  ElementRef,
  EventEmitter,
  Inject,
  Injector,
  Input,
  OnChanges,
  OnInit,
  Optional,
  Output,
  QueryList,
  Renderer2,
  SimpleChanges,
  TemplateRef,
  ViewChildren,
  booleanAttribute,
  forwardRef,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NG_VALUE_ACCESSOR } from '@angular/forms';
import { MatError } from '@angular/material/form-field';
import { FinAnimationsModule } from '@fincloud/ui/animations';
import { FinButtonAppearance, FinButtonModule } from '@fincloud/ui/button';
import {
  FinAccordionComponent,
  FinExpansionPanelModule,
} from '@fincloud/ui/expansion-panel';
import { FinFieldMessageService } from '@fincloud/ui/field-message';
import { FinInputModule } from '@fincloud/ui/input';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinSize } from '@fincloud/ui/types';
import { FinControlValueAccessor } from '@fincloud/utils/control-value-accessor';
import { FinFieldService } from '@fincloud/utils/services';
import {
  BehaviorSubject,
  Observable,
  Subject,
  delay,
  distinctUntilChanged,
  filter,
  map,
  merge,
  scan,
  shareReplay,
  startWith,
  switchMap,
  take,
  tap,
  timer,
  withLatestFrom,
} from 'rxjs';
import { DocumentClassificationConfig } from '../../models/document-classification-loading-config';
import { DocumentClassificationState } from '../../models/document-classification-state';
import { DOCUMENT_CLASSIFICATION_LOADING_CONFIG } from '../../utils/document-classification-config-token';
import { DEFAULT_DOCUMENT_CLASSIFICATION_CONFIG } from '../../utils/document-classification-default-config';
import { FinDocumentClassificationLoadingComponent } from '../document-classification-loading/document-classification-loading.component';
/**
 * A component for classifying documents with templates for prefix, suffix and content.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-document-classification--docs Storybook Reference}
 */
@Component({
  selector: 'fin-document-classification',
  standalone: true,
  imports: [
    CommonModule,
    FinExpansionPanelModule,
    FinInputModule,
    FinAnimationsModule,
    MatError,
    FinButtonModule,
    FinTruncateTextModule,
    FinDocumentClassificationLoadingComponent,
  ],
  templateUrl: './document-classification.component.html',
  styleUrl: './document-classification.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    '[class]': 'cssClasses',
  },
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FinDocumentClassificationComponent),
      multi: true,
    },
    FinFieldService,
    FinFieldMessageService,
  ],
})
export class FinDocumentClassificationComponent
  extends FinControlValueAccessor
  implements OnInit, OnChanges, AfterViewInit
{
  @Input({ transform: booleanAttribute }) expanded = false;
  @Input({ transform: booleanAttribute }) expandDisabled = false;
  @Input({ transform: booleanAttribute }) readonly = false;
  @Input({ transform: booleanAttribute }) loading = false;
  @Input({ transform: booleanAttribute }) aiPrediction = false;
  @Input() rootFolderId = '';

  @Input() placeholder = '';

  @Output() canceledLoading = new EventEmitter<void>();

  @ContentChild('finContent')
  protected contentTemplate?: TemplateRef<unknown>;

  @ContentChild('finPrefix')
  protected prefixTemplate?: TemplateRef<unknown>;

  @ContentChild('finInputPrefix')
  protected inputPrefixTemplate?: TemplateRef<unknown>;

  @ContentChild('finInputSuffix')
  protected inputSuffixTemplate?: TemplateRef<unknown>;

  @ContentChild('finFooterSuffix')
  protected footerSuffixTemplate?: TemplateRef<unknown>;

  @ViewChildren(FinAccordionComponent)
  private finAccordion!: QueryList<FinAccordionComponent>;

  protected finButtonAppearance = FinButtonAppearance;
  protected finButtonSize = FinSize;

  private initialState: DocumentClassificationState = {
    expanded: false,
    expandDisabled: false,
    readonly: false,
    loading: false,
    controlValue: '',
    placeholder: '',
    blurAnimationState: 'clear',
    loadingTimeoutMessage: [],
    showLoadingMessage: false,
    originalDocumentName: '',
    aiPrediction: false,
    rootFolderIdChanged: false,
    maxLength: this.injectedConfig?.maxLength,
    fileNameExtension: '',
    loadingMessageWithFileNameWidth: 'unset',
    loadingFileNameWidth: 'unset',
    componentWidth: 0,
  };

  /**
   * Consolidated state subject —
   * Each partial patch of the UI is funneled through this BehaviorSubject and *reduced* into a
   * single immutable view‑state.  This keeps all UI flags in one place, preventing boolean
   * explosion and eliminating multiple `@Input` setters and `ngOnChanges` branches.
   */
  private stateSubject$$ = new BehaviorSubject<
    Partial<DocumentClassificationState>
  >(this.initialState);

  /**
   * Emits `{ rootFolderIdChanged: true }` every time the `rootFolderId` input changes,
   * signalling downstream streams that a *new* regeneration has finished so they
   * can clear the blur animation.
   */
  private rootFolderIdChanged$: Observable<
    Partial<DocumentClassificationState>
  > = this.stateSubject$$.pipe(
    distinctUntilChanged(),
    filter((state) => !!state.rootFolderIdChanged),
    map((state) => {
      return { rootFolderIdChanged: state.rootFolderIdChanged };
    }),
  );

  private timer$: Observable<Partial<DocumentClassificationState>> =
    this.stateSubject$$.pipe(
      map((state) => state.loading),
      filter(Boolean),
      tap(() => {
        this.finAccordion?.first.closeAll();
      }),
      switchMap(() =>
        // Start a single‑shot timer **only** when `loading` flips to true.  After
        // `timeoutDuration` (default 5 s) it emits a flag that shows a timeout message.
        // Using `switchMap()` guarantees that every fresh “loading = true” cancels the
        // previous timer, so rapid toggles don’t accumulate multiple timers.
        {
          return timer(this.documentClassificationConfig.timeoutDuration).pipe(
            take(1),
            map(() => {
              return {
                showLoadingMessage: true,
                blurAnimationState:
                  'clear' as DocumentClassificationState['blurAnimationState'],
              };
            }),
          );
        },
      ),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

  private truncateLoadingMessage$$ = new Subject<boolean>();
  /*
   * Single public state observable
   * We merge three intent streams (imperative patches, form‑value changes, and timeout ticks)
   * and fold them with `scan()` so that every piece of UI derives from *one* immutable object.
   * This pattern mimics a Redux reducer and works hand‑in‑hand with `ChangeDetectionStrategy.OnPush`.
   */
  protected state$ = merge(this.stateSubject$$, this.timer$).pipe(
    scan(
      (state, command) => (
        this.handleCommand(state, command),
        this.handleLoadingTimeMessage(state),
        this.handleBlurAnimationChanges(state),
        this.handleHostClasses(this.renderer, this.elementRef)(state),
        state
      ),
      { ...this.initialState },
    ),

    shareReplay({ bufferSize: 1, refCount: true }),
  );

  /**
   * Emits a single state‑patch that finishes the loading workflow when either
   *   - the form value changes while loading, or
   *   - a new `rootFolderId` arrives.
   *
   * Adds a short `delay()` to wait a blur‑animation to finish,
   * then clears `loading` and `rootFolderIdChanged`.
   */
  private classificationReceived$: Observable<
    Partial<DocumentClassificationState>
  > = merge(this.rootFolderIdChanged$).pipe(
    withLatestFrom(this.state$),
    filter(([, state]) => {
      return (
        !!state.loading || (state.rootFolderIdChanged && state.aiPrediction)
      );
    }),
    delay(2_000),

    map(() => ({
      loading: false,
      rootFolderIdChanged: false,
      aiPrediction: false,
    })),
    tap((state) => {
      this.updateState({ ...state, blurAnimationState: 'clear' });
    }),
  );

  protected getMessage$ = this.finFieldMessageService?.getMessage$.pipe(
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  // Exposed as a getter (instead of a constant) so that subclasses or future feature flags can
  // override the base CSS class without touching the template.
  protected get cssClasses(): string {
    return 'fin-document-classification';
  }

  private documentClassificationConfig: DocumentClassificationConfig;

  protected btnCancelLabel = '';

  constructor(
    injector: Injector,
    private finFieldMessageService: FinFieldMessageService,
    private finFieldService: FinFieldService,
    private destroyRef: DestroyRef,
    @Optional()
    @Inject(DOCUMENT_CLASSIFICATION_LOADING_CONFIG)
    private injectedConfig: DocumentClassificationConfig,
    private elementRef: ElementRef,
    private renderer: Renderer2,
  ) {
    super(injector);

    // Merge consumer‑provided config with library defaults.
    // Spread syntax keeps the operation cheap (shallow copy) and makes precedence explicit:
    // caller overrides library defaults *per property* without mutating either source.
    this.documentClassificationConfig = {
      ...DEFAULT_DOCUMENT_CLASSIFICATION_CONFIG,
      ...this.injectedConfig,
    };

    this.btnCancelLabel = this.documentClassificationConfig.btnCancelLabel;
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Convert the Angular change‑detection callback into an *intent* that feeds our reducer,
    // instead of updating the template directly.  This keeps all visual changes flowing through
    // the same reactive pipeline (`state$`), avoiding split‑brain state.

    this.updateState({
      expanded: this.expanded,
      expandDisabled: this.expandDisabled,
      readonly: this.readonly,
      loading: this.loading,
      placeholder: this.placeholder,

      aiPrediction: this.aiPrediction,
      rootFolderIdChanged:
        changes['rootFolderId']?.currentValue !==
        changes['rootFolderId']?.previousValue,
    });
  }

  ngOnInit(): void {
    // Initialize state with current input values
    this.updateState({
      expanded: this.expanded,
      expandDisabled: this.expandDisabled,
      readonly: this.readonly,
      loading: this.loading,
      placeholder: this.placeholder,
      blurAnimationState: 'clear',
      loadingTimeoutMessage: this.createLoadingMessage(
        this.documentClassificationConfig.timeoutMessage,
      ),
      fileNameExtension: this.getFileNameExtension(),
      showLoadingMessage: false,
      originalDocumentName: '',

      aiPrediction: this.aiPrediction,
      rootFolderIdChanged: false,
    });

    this.classificationReceived$.subscribe();
  }

  ngAfterViewInit(): void {
    this.control.statusChanges
      .pipe(startWith(this.control.errors), takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.finFieldService.controlErrors$.next(this.control.errors);
      });

    this.updateState({
      componentWidth: this.elementRef.nativeElement.offsetWidth,
    });
  }

  protected onClick(event: Event, isReadonly: boolean) {
    if (!isReadonly) {
      event.stopPropagation();
    }
  }

  protected onBlur(controlValue: unknown) {
    this.blurred.emit(controlValue);
  }

  /**
   * Cancels the loading workflow and notifies parent components.
   *
   * *Why this explicit handler?*
   * Giving consumers a dedicated `canceledLoading` output lets them abort HTTP requests or free
   * backend resources instead of merely stopping a spinner on the UI.
   */
  protected onCancelLoading(event: Event) {
    event.stopPropagation();
    this.updateState({
      loading: false,
      rootFolderIdChanged: false,
    });
    this.canceledLoading.emit();
  }

  /**
   * Helper method to update the consolidated state
   */
  private updateState(
    partialState: Partial<DocumentClassificationState>,
  ): void {
    this.stateSubject$$.next(partialState);
  }

  /**
   * Transitions the blur animation between `'blurred'` and `'clear'`.
   * We keep the logic here (imperative) rather than in the template so that the template stays
   * declarative and testable.  Centralising the decision also prevents conflicting bindings when
   * more flags are added in the future.
   */
  private handleBlurAnimationChanges(state: DocumentClassificationState): void {
    if (
      state.rootFolderIdChanged &&
      state.aiPrediction &&
      !state.showLoadingMessage
    ) {
      Object.assign(state, { blurAnimationState: 'blurred' });
    }
  }

  private handleHostClasses(
    renderer: Renderer2,
    elementRef: ElementRef,
  ): (state: DocumentClassificationState) => void {
    return (state: DocumentClassificationState) => {
      if (state.loading) {
        renderer.addClass(
          elementRef.nativeElement,
          'fin-document-classification-loading',
        );
      } else {
        renderer.removeClass(
          elementRef.nativeElement,
          'fin-document-classification-loading',
        );
      }
    };
  }

  private handleLoadingTimeMessage(state: DocumentClassificationState): void {
    if (!state.loading || state.rootFolderIdChanged) {
      Object.assign(state, {
        // loadingTimeoutMessage: '',
        showLoadingMessage: false,
      });
    }
  }

  private handleCommand(
    state: DocumentClassificationState,
    command: Partial<DocumentClassificationState>,
  ): void {
    Object.assign(state, command);
  }

  private createLoadingMessage(message: string) {
    const placeholder = '{filename}';
    const filename = this.control.value;
    let replaced = [];
    if (filename) {
      const index = filename.lastIndexOf('.');
      const replacement = index > 0 ? filename.substring(0, index) : filename;
      const parts = message.split(new RegExp(`(${placeholder})`));

      replaced = parts.map((part) =>
        part === placeholder ? replacement : part,
      );
    }

    return replaced;
  }

  private getFileNameExtension() {
    const filename: string = this.control.value;
    let extension = '';

    if (filename) {
      extension = filename
        .substring(filename.lastIndexOf('.'))
        .replace('.', '');
    }

    return extension;
  }

  onUpdateState(state: Partial<DocumentClassificationState>) {
    this.updateState(state);
  }
}
