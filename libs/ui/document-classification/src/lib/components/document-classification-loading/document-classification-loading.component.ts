import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  ContentChild,
  DestroyRef,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  QueryList,
  SimpleChanges,
  TemplateRef,
  ViewChildren,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FinAnimationsModule } from '@fincloud/ui/animations';
import { FinButtonAppearance, FinButtonModule } from '@fincloud/ui/button';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinSize } from '@fincloud/ui/types';
import { pxToRem } from '@fincloud/utils/functions';
import { animationFrameScheduler, observeOn } from 'rxjs';
import { DocumentClassificationState } from '../../models/document-classification-state';
import { FinGetFileExtensionPipe } from '../../pipes/get-file-extension.pipe';
import { FinGetFileNamePipe } from '../../pipes/get-file-name.pipe';

@Component({
  selector: 'fin-document-classification-loading',
  standalone: true,
  imports: [
    CommonModule,
    FinAnimationsModule,
    FinGetFileExtensionPipe,
    FinGetFileNamePipe,
    FinTruncateTextModule,
    FinButtonModule,
  ],
  templateUrl: './document-classification-loading.component.html',
  styleUrl: './document-classification-loading.component.scss',
  // changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinDocumentClassificationLoadingComponent
  implements AfterViewInit, OnChanges
{
  @Input() state!: DocumentClassificationState;
  @Input() controlValue!: string;
  @Input() btnCancelLabel!: string;
  @Input() maxWidth!: number;

  @Output() canceledLoading = new EventEmitter<Event>();
  @Output() updateState = new EventEmitter<
    Partial<DocumentClassificationState>
  >();

  @ContentChild('finInputPrefix')
  protected inputPrefixTemplate?: TemplateRef<unknown>;

  @ViewChildren('loadingMessageFileName')
  private loadingMessageFileName!: QueryList<ElementRef<HTMLElement>>;

  @ViewChildren('loadingMessage')
  private loadingMessage!: QueryList<ElementRef<HTMLElement>>;

  protected finButtonAppearance = FinButtonAppearance;
  protected finButtonSize = FinSize;

  constructor(private destroyRef: DestroyRef) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (this.loadingMessage) {
      this.setupFileNameTruncation();
    }
  }

  ngAfterViewInit(): void {
    this.setupFileNameTruncation();
  }

  /**
   * Cancels the loading workflow and notifies parent components.
   *
   * *Why this explicit handler?*
   * Giving consumers a dedicated `canceledLoading` output lets them abort HTTP requests or free
   * backend resources instead of merely stopping a spinner on the UI.
   */
  protected onCancelLoading(event: Event) {
    event.stopPropagation();
    this.canceledLoading.emit();
  }

  private setupFileNameTruncation() {
    const paddingAroundText = 104;
    const componentWidth = this.maxWidth - paddingAroundText;
    const loadingMessageWithFileNameWidth =
      this.loadingMessage.first?.nativeElement.getBoundingClientRect().width;
    if (
      loadingMessageWithFileNameWidth > componentWidth &&
      componentWidth > 0
    ) {
      this.updateState.emit({
        loadingFileNameWidth: pxToRem(componentWidth),
      });
    }

    this.loadingMessage.changes
      .pipe(
        observeOn(animationFrameScheduler),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(() => {
        const { componentWidth, loadingMsgWithoutFileNameWidth } =
          this.calcLoadingMsgTruncationWidth();

        if (
          loadingMessageWithFileNameWidth > componentWidth &&
          componentWidth > 0
        ) {
          this.updateState.emit({
            loadingMessageWithFileNameWidth: pxToRem(
              componentWidth - loadingMsgWithoutFileNameWidth,
            ),
          });
        }
      });
  }

  private calcLoadingMsgTruncationWidth() {
    const componentWidth = this.maxWidth;
    const loadingMessageFileNameWidth =
      this.loadingMessageFileName.first?.nativeElement.getBoundingClientRect()
        .width;
    const loadingMessageWithFileNameWidth =
      this.loadingMessage.first?.nativeElement.getBoundingClientRect().width;

    const loadingMsgWithoutFileNameWidth =
      loadingMessageWithFileNameWidth - loadingMessageFileNameWidth;
    return { componentWidth, loadingMsgWithoutFileNameWidth };
  }
}
