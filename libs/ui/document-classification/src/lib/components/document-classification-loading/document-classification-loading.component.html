<fin-border-sweep
  class="fin-h-[4rem]"
  [enableAnimation]="state.loading && !!state.showLoadingMessage"
>
  <div
    class="fin-loading-message fin-px-[1.6rem] fin-grow fin-h-full fin-flex fin-flex-col fin-justify-center"
  >
    <div
      class="fin-text-body-2-moderate fin-flex fin-justify-between fin-items-center"
      [class.expand-width]="state.blurAnimationState === 'blurred'"
      [class.fin-blur-fadeout]="state.blurAnimationState === 'blurred'"
      [class.fin-is-loading]="state.loading"
      [class.fin-blur]="state.blurAnimationState === 'blurred'"
    >
      @if (!state.showLoadingMessage) {
        <div #loadingMessage class="fin-flex">
          @if (inputPrefixTemplate && !state.loading) {
            <div class="fin-pe-[0.8rem]">
              <ng-container
                [ngTemplateOutlet]="inputPrefixTemplate"
              ></ng-container>
            </div>
          }
          <div
            finTruncateText
            [content]="controlValue"
            [style.width]="state.loadingFileNameWidth"
            [disableTooltip]="!(controlValue | finGetFileExtension)"
          >
            {{ controlValue | finGetFileName }}
          </div>
          @if (state.loadingFileNameWidth === 'unset') {
            .
          }

          <div>
            {{ controlValue | finGetFileExtension }}
          </div>
        </div>
      } @else {
        <div #loadingMessage class="fin-flex">
          @for (
            loadingTimeoutMessagePart of state.loadingTimeoutMessage;
            track $index
          ) {
            @if (
              controlValue ===
              loadingTimeoutMessagePart + '.' + state.fileNameExtension
            ) {
              <div
                #loadingMessageFileName
                finTruncateText
                [content]="controlValue"
                class="fin-flex-1 fin-text-body-2-strong"
                [style.width]="state.loadingMessageWithFileNameWidth"
              >
                {{ loadingTimeoutMessagePart }}.
              </div>
              <div class="fin-text-body-2-strong">
                {{ state.fileNameExtension }}
              </div>
            } @else {
              <div class="fin-flex-shrink-0 fin-px-1">
                {{ loadingTimeoutMessagePart }}
              </div>
            }
          }
        </div>
      }
      @if (state.showLoadingMessage) {
        <button
          #loadingCancelBtn
          fin-button
          [appearance]="finButtonAppearance.STEALTH"
          [size]="finButtonSize.XS"
          class="fin-text-body-3-strong"
          (click)="onCancelLoading($event)"
        >
          {{ btnCancelLabel }}
        </button>
      }
    </div>
  </div>
</fin-border-sweep>
