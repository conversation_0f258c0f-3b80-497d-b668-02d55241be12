/**
 * Interface defining the consolidated component state
 */
type blurAnimationState = 'blurred' | 'clear';
export interface DocumentClassificationState {
  expanded: boolean;
  expandDisabled: boolean;
  readonly: boolean;
  loading: boolean;
  controlValue: string;
  placeholder: string;
  blurAnimationState: blurAnimationState;
  loadingTimeoutMessage: string[];
  showLoadingMessage: boolean;
  originalDocumentName: string;
  aiPrediction: boolean;
  rootFolderIdChanged: boolean;
  maxLength: number;
  componentWidth: number;
  fileNameExtension: string;
  loadingMessageWithFileNameWidth: string;
  loadingFileNameWidth: string;
}
