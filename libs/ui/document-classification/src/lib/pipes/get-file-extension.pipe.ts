import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'finGetFileExtension',
  standalone: true,
})
export class FinGetFileExtensionPipe implements PipeTransform {
  transform(value: string): string {
    if (typeof value !== 'string' || !value.includes('.')) {
      return '';
    }

    const parts = value.split('.');
    const extension = parts.pop();

    return extension && parts.length ? extension : '';
  }
}
