import { CommonModule } from '@angular/common';
import { FinSeparatorsModule } from '@fincloud/ui/separators';
import { FinTabsModule } from '@fincloud/ui/tabs';
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { FinContainerModule } from '../container.module';
import { FinContainerDirective } from './container.directive';

const meta: Meta<FinContainerDirective> = {
  title: 'Directives/Container',
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        FinSeparatorsModule,
        FinTabsModule,
        FinContainerModule,
      ],
    }),
  ],
  parameters: {
    backgrounds: {
      default: 'dark',
      values: [{ name: 'dark', value: '#f8f8f8' }],
    },
    docs: {
      description: {
        component:
          '`import { FinContainerModule } from "@fincloud/ui/container"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=20781-164745&t=3Ij2EDHwp7sB5gbs-4',
    },
  },
};
export default meta;

type Story = StoryObj<FinContainerDirective>;

export const Default: Story = {
  args: {
    boxShadow: true,
    borderRadius: true,
  },
  argTypes: {
    boxShadow: {
      description: 'Applies box shadow effect to the container.',
    },
    borderRadius: {
      description: 'Applies rounded corners to the container.',
    },
    hasBoxShadow: {
      description:
        '<span style="color:red">**Deprecated**</span><br> Use `boxShadow` instead',
    },
    hasBorderRadius: {
      description:
        '<span style="color:red">**Deprecated**</span><br> Use `borderRadius` instead',
    },
  },
  render: (args) => ({
    props: args,
    template: `
    <div finContainer [boxShadow]="boxShadow" [borderRadius]="borderRadius">
      <div class="fin-p-[2rem]">
        Custom content
      </div>
    </div>
    `,
  }),
};

export const CustomExample: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: (args) => ({
    props: args,
    template: `
      <div finContainer [boxShadow]="boxShadow" [borderRadius]="borderRadius">
        <div class="fin-w-[44rem]">
          <div
            class="fin-rounded-t-[0.8rem] fin-flex fin-justify-between fin-px-[1.6rem] fin-py-[1.2rem]"
          >
            <div class="fin-text-color-text-primary fin-text-body-1-strong">Some text</div>
            <div class="fin-text-color-text-interactive fin-text-body-3-strong">Some text</div>
          </div>
          <hr finHorizontalSeparator type="Subtle" />

          <fin-tabs #finTabs type="Secondary" size="l">
            <fin-tab>
              <ng-template finTabLabel>
                Tab 1
              </ng-template>

              <ng-template finTabBody>
                <div class="fin-p-[2rem]">
                  Tab content 1
                </div>
              </ng-template>
            </fin-tab>

            <fin-tab>
              <ng-template finTabLabel>
                Tab 2
              </ng-template>

              <ng-template finTabBody>
                <div class="fin-p-[2rem]">
                  Tab content 2
                </div>
              </ng-template>
            </fin-tab>
          </fin-tabs>
        </div>
      </div>
    `,
  }),
};
