import { booleanAttribute, Directive, Input } from '@angular/core';

/**
 * A layout component used to wrap other components.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/directives-container--docs Storybook Reference}
 */
@Directive({
  selector: '[finContainer]',
  standalone: true,
  host: {
    '[class]': 'containerClasses',
  },
})
export class FinContainerDirective {
  /** @deprecated use instead `boxShadow` */
  @Input({ transform: booleanAttribute }) hasBoxShadow = true;
  /** @deprecated use instead `borderRadius` */
  @Input({ transform: booleanAttribute }) hasBorderRadius = true;

  /** Applies box shadow effect to the container. */
  @Input({ transform: booleanAttribute }) boxShadow = this.hasBoxShadow;

  /** Applies rounded corners to the container. */
  @Input({ transform: booleanAttribute }) borderRadius = this.hasBorderRadius;

  protected get containerClasses(): string {
    let classes = 'fin-bg-color-surface-primary';

    if (this.boxShadow && this.hasBoxShadow) {
      classes += ' fin-shadow-[0_0.4rem_1rem_0_rgba(38,40,62,0.16)]';
    }

    if (this.borderRadius && this.hasBorderRadius) {
      classes += ' fin-rounded-[0.8rem]';
    }

    return classes;
  }
}
