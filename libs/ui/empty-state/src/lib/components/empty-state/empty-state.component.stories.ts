import { CommonModule } from '@angular/common';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinIconModule } from '@fincloud/ui/icon';
import { moduleMetadata, type Meta, type StoryObj } from '@storybook/angular';
import { FinEmptyStateType } from '../../enums/fin-empty-state-type';
import { FinEmptyStateComponent } from './empty-state.component';

const meta: Meta<FinEmptyStateComponent> = {
  component: FinEmptyStateComponent,
  title: 'Components/Empty State',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinEmptyStateModule } from "@fincloud/ui/empty-state"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6680-6508&p=f&m=dev',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [CommonModule, FinIconModule, FinButtonModule],
    }),
  ],
};

export default meta;

type Story = StoryObj<
  FinEmptyStateComponent & {
    finIcon: string;
    finTextContent: string;
    finDescription: string;
    finActions: string;
  }
>;

export const Complex: Story = {
  args: {
    title: 'No content',
    type: FinEmptyStateType.COMPLEX,
  },
  argTypes: {
    title: {
      control: { type: 'text' },
    },
    finIcon: {
      description: 'Icon or image template.',
      defaultValue: { summary: 'attribute' },
      table: {
        category: 'Templates',
      },
    },
    finTextContent: {
      description: 'Text content template.',
      defaultValue: { summary: 'attribute' },
      table: {
        category: 'Templates',
      },
    },
    finDescription: {
      description: 'Additional description template',
      defaultValue: { summary: 'attribute' },
      table: {
        category: 'Templates',
      },
    },
    finActions: {
      description: 'Actions template',
      defaultValue: { summary: 'attribute' },
      table: {
        category: 'Templates',
      },
    },
  },
  render: (args) => {
    return {
      props: args,
      template: `
       <fin-empty-state
          [type]="type"
          [title]="title"
        >
          <ng-template #finIcon>
            <img
              src="/assets/storybook/icons/frame.svg"
              alt="image"
              class="fin-w-[8.8rem]"
            />
          </ng-template>
          <ng-template #finTextContent>
             You can add content from the editor panel on the left. Hover on group
             and click + to add subgroups to it, hover on subgroup and click + to add
             fields to it.
          </ng-template>
          <ng-template #finDescription
             >Vestibulum feugiat cursus magna
             <a fin-button-link class="fin-text-body-3-moderate">ac venenatis.</a>
          </ng-template>
          <ng-template #finActions>
             <button
               fin-button
               [size]="'m'"
               [shape]="'round'"
               [appearance]="'secondary'"
             >
               Action
             </button>
             <button
               fin-button
               [size]="'m'"
               [shape]="'round'"
               [appearance]="'primary'"
             >
               Action
             </button>
          </ng-template>
       </fin-empty-state>
       `,
    };
  },
};

export const ComplexNoDescription: Story = {
  args: {
    ...Complex.args,
  },
  argTypes: {
    ...Complex.argTypes,
  },
  render: (args) => {
    return {
      props: args,
      template: `
       <fin-empty-state
          [type]="type"
          [title]="title"
        >
          <ng-template #finIcon>
            <img
              src="/assets/storybook/icons/frame.svg"
              alt="image"
              class="fin-w-[8.8rem]"
            />
          </ng-template>
          <ng-template #finTextContent>
             You can add content from the editor panel on the left. Hover on group
             and click + to add subgroups to it, hover on subgroup and click + to add
             fields to it.
          </ng-template>
          <ng-template #finActions>
             <button
               fin-button
               [size]="'m'"
               [shape]="'round'"
               [appearance]="'secondary'"
             >
               Action
             </button>
             <button
               fin-button
               [size]="'m'"
               [shape]="'round'"
               [appearance]="'primary'"
             >
               Action
             </button>
          </ng-template>
       </fin-empty-state>
       `,
    };
  },
};

export const ComplexSingleAction: Story = {
  args: {
    ...Complex.args,
  },
  argTypes: {
    ...Complex.argTypes,
  },
  render: (args) => {
    return {
      props: args,
      template: `
       <fin-empty-state
          [type]="type"
          [title]="title"
        >
          <ng-template #finIcon>
            <img
              src="/assets/storybook/icons/frame.svg"
              alt="image"
              class="fin-w-[8.8rem]"
            />
          </ng-template>
          <ng-template #finTextContent>
             You can add content from the editor panel on the left. Hover on group
             and click + to add subgroups to it, hover on subgroup and click + to add
             fields to it.
          </ng-template>
          <ng-template #finActions>
             <button
               fin-button
               [size]="'m'"
               [shape]="'round'"
               [appearance]="'secondary'"
             >
               Action
             </button>
          </ng-template>
       </fin-empty-state>
       `,
    };
  },
};

export const ComplexNoActions: Story = {
  args: {
    ...Complex.args,
  },
  argTypes: {
    ...Complex.argTypes,
  },
  render: (args) => {
    return {
      props: args,
      template: `
       <fin-empty-state
          [type]="type"
          [title]="title"
        >
          <ng-template #finIcon>
            <img
              src="/assets/storybook/icons/frame.svg"
              alt="image"
              class="fin-w-[8.8rem]"
            />
          </ng-template>
          <ng-template #finTextContent>
             You can add content from the editor panel on the left. Hover on group
             and click + to add subgroups to it, hover on subgroup and click + to add
             fields to it.
          </ng-template>
          <ng-template #finDescription
             >Vestibulum feugiat cursus magna
             <a fin-button-link class="fin-text-body-3-moderate">ac venenatis.</a>
          </ng-template>
       </fin-empty-state>
       `,
    };
  },
};

export const ComplexTitleWithImage: Story = {
  args: {
    ...Complex.args,
    title: 'Upload',
  },
  argTypes: {
    ...Complex.argTypes,
  },
  render: (args) => {
    return {
      props: args,
      template: `
       <fin-empty-state
          [type]="type"
          [title]="title"
        >
          <ng-template #finIcon>
            <img
              src="/assets/storybook/icons/upload.svg"
              alt="image"
              class="fin-w-[8.8rem]"
            />
          </ng-template>
       </fin-empty-state>
       `,
    };
  },
};

export const ComplexTitleWithImageAndTextContent: Story = {
  args: {
    title: 'Select a PNG file',
    type: FinEmptyStateType.COMPLEX,
  },
  argTypes: {
    ...Complex.argTypes,
  },
  render: (args) => {
    return {
      props: args,
      template: `
       <fin-empty-state
          [type]="type"
          [title]="title"
        >
          <ng-template #finIcon>
            <img
              src="/assets/storybook/icons/image.png"
              alt="image"
              class="fin-w-[4.8rem]"
            />
          </ng-template>
          <ng-template #finTextContent>
             or drag & drop it here
          </ng-template>
       </fin-empty-state>
       `,
    };
  },
};

export const Basic: Story = {
  args: {
    title: 'Nothing to show here',
    type: FinEmptyStateType.BASIC,
  },
  argTypes: {
    ...Complex.argTypes,
  },
  render: (args) => {
    return {
      props: args,
      template: `
       <fin-empty-state
          [type]="type"
          [title]="title"
        >
          <ng-template #finIcon>
            <img
              src="/assets/storybook/icons/document.svg"
              alt="image"
              class="fin-w-[3.2rem]"
            />
          </ng-template>
       </fin-empty-state>
       `,
    };
  },
};

export const BasicWithoutIcon: Story = {
  args: {
    ...Basic.args,
  },
  argTypes: {
    ...Complex.argTypes,
  },
  render: (args) => {
    return {
      props: args,
      template: `
       <fin-empty-state [type]="type" [title]="title"></fin-empty-state>
       `,
    };
  },
};
