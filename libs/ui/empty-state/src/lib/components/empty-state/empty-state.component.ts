import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  ContentChild,
  Input,
  TemplateRef,
} from '@angular/core';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinEmptyStateType } from '../../enums/fin-empty-state-type';

/**
 * A component that represents empty state.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-empty-state--docs Storybook Reference}
 */

@Component({
  selector: 'fin-empty-state',
  standalone: true,
  imports: [CommonModule, FinIconModule],
  templateUrl: './empty-state.component.html',
  styleUrl: './empty-state.component.scss',
  host: {
    class: 'fin-empty-state',
    '[class]': 'typeCssClasses',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinEmptyStateComponent {
  /** Specifies the empty state type. */
  @Input() type: FinEmptyStateType = FinEmptyStateType.COMPLEX;

  /** Specifies the title. */
  @Input() title = '';

  @ContentChild('finIcon')
  protected iconTemplate?: TemplateRef<unknown>;

  @ContentChild('finTextContent')
  protected textContentTemplate?: TemplateRef<unknown>;

  @ContentChild('finDescription')
  protected descriptionTemplate?: TemplateRef<unknown>;

  @ContentChild('finActions')
  protected actionsTemplate?: TemplateRef<unknown>;

  protected emptyStateType = FinEmptyStateType;

  get typeCssClasses() {
    return `fin-empty-state-${this.type}`;
  }
}
