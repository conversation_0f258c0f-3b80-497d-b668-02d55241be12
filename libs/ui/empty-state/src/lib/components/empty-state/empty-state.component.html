<div class="fin-content fin-flex fin-justify-center fin-items-center">
  @if (iconTemplate) {
    <ng-container [ngTemplateOutlet]="iconTemplate"></ng-container>
  }

  @if (title) {
    <div class="fin-title fin-tracking-normal">
      {{ title }}
    </div>
  }

  @if (textContentTemplate) {
    <div
      class="fin-text-body-2-moderate fin-text-color-text-secondary fin-text-center fin-tracking-normal"
    >
      <ng-container [ngTemplateOutlet]="textContentTemplate"></ng-container>
    </div>
  }
</div>

@if (descriptionTemplate) {
  <div
    class="fin-text-color-text-tertiary fin-text-body-2-moderate fin-text-center fin-tracking-normal"
  >
    <ng-container [ngTemplateOutlet]="descriptionTemplate"></ng-container>
  </div>
}

@if (actionsTemplate) {
  <div class="fin-actions fin-flex fin-flex-row fin-gap-x-[1.6rem]">
    <ng-container [ngTemplateOutlet]="actionsTemplate"></ng-container>
  </div>
}
