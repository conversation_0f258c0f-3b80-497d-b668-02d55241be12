.fin-avatar {
  &-image {
    background-repeat: 'no-repeat';
    background-size: cover;
  }
  &-letters {
    text-transform: uppercase;
    color: #fff;
  }
  &-xxl {
    &.fin-avatar {
      width: 9.6rem;
      height: 9.6rem;
    }
    .fin-avatar-letters {
      @apply fin-text-heading-1-moderate;
    }
  }
  &-xl {
    &.fin-avatar {
      width: 5.6rem;
      height: 5.6rem;
    }
    .fin-avatar-letters {
      @apply fin-text-heading-2-moderate;
    }
  }
  &-l {
    &.fin-avatar {
      width: 4rem;
      height: 4rem;
    }
    .fin-avatar-letters {
      @apply fin-text-body-1-moderate;
    }
  }
  &-m {
    &.fin-avatar {
      width: 3.2rem;
      height: 3.2rem;
    }
    .fin-avatar-letters {
      @apply fin-text-body-2-moderate;
    }
  }
  &-s {
    &.fin-avatar {
      width: 2.4rem;
      height: 2.4rem;
    }
    .fin-avatar-letters {
      font-size: theme('fontSize.text-caption-1-size');
      line-height: theme('lineHeight.text-body-1-line-height');
      font-weight: theme('fontWeight.text-body-3-moderate-weight');
    }
  }
  &-xs {
    &.fin-avatar {
      width: 1.6rem;
      height: 1.6rem;
    }
    .fin-avatar-letters {
      font-size: 0.8rem;
      line-height: theme('lineHeight.text-caption-1-line-height');
      font-weight: theme('fontWeight.text-body-3-moderate-weight');
    }
  }
}
