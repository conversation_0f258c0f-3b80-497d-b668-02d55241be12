import { FinSize } from '@fincloud/ui/types';
import type { Meta, StoryObj } from '@storybook/angular';
import { FinAvatarDefaultComponent } from './avatar-default.component';

const meta: Meta<FinAvatarDefaultComponent> = {
  component: FinAvatarDefaultComponent,
  title: 'Components/Avatars/Avatar Default',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinAvatarModule } from "@fincloud/ui/avatar-default"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-WIP?type=design&node-id=6528-194&mode=dev',
    },
  },
};
export default meta;
type Story = StoryObj<FinAvatarDefaultComponent>;

export const SizeXXL: Story = {
  argTypes: {
    size: {
      options: Object.values(FinSize),
      control: { type: 'select' },
    },
  },
  args: {
    firstName: 'John',
    lastName: 'Dou',
    size: FinSize.XXL,
    image: '',
  },
};

export const SizeXL: Story = {
  argTypes: {
    ...SizeXXL.argTypes,
  },
  args: {
    ...SizeXXL.args,
    size: FinSize.XL,
  },
  parameters: {
    ...SizeXXL.parameters,
  },
};

export const SizeL: Story = {
  argTypes: {
    ...SizeXXL.argTypes,
  },
  args: {
    ...SizeXXL.args,
    size: FinSize.L,
  },
  parameters: {
    ...SizeXXL.parameters,
  },
};

export const SizeM: Story = {
  argTypes: {
    ...SizeXXL.argTypes,
  },
  args: {
    ...SizeXXL.args,
    size: FinSize.M,
  },
  parameters: {
    ...SizeXXL.parameters,
  },
};

export const SizeS: Story = {
  argTypes: {
    ...SizeXXL.argTypes,
  },
  args: {
    ...SizeXXL.args,
    size: FinSize.S,
  },
  parameters: {
    ...SizeXXL.parameters,
  },
};

export const SizeXS: Story = {
  argTypes: {
    ...SizeXXL.argTypes,
  },
  args: {
    ...SizeXXL.args,
    size: FinSize.XS,
  },
  parameters: {
    ...SizeXXL.parameters,
  },
};

export const WithImage: Story = {
  argTypes: {
    ...SizeXXL.argTypes,
  },
  args: {
    image: 'https://i.ibb.co/WvYWsPx/19e2c7d9663c55908261f6219d68a381.png',
  },
  parameters: {
    ...SizeXXL.parameters,
  },
};
