import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FinSize } from '@fincloud/ui/types';

/**
 * Displays an avatar image.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-avatar-default--docs Storybook Reference}
 */
@Component({
  selector: 'fin-avatar-default',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './avatar-default.component.html',
  styleUrl: './avatar-default.component.scss',
  host: {
    class: 'fin-avatar-default',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinAvatarDefaultComponent {
  /** User first name. */
  @Input()
  set firstName(name: string) {
    this.firstNameLetter = name[0];
  }

  /** User last name. */
  @Input()
  set lastName(name: string) {
    this.lastNameLetter = name[0];
  }

  /** Size of the avatar. */
  @Input() size:
    | FinSize.XXL
    | FinSize.XL
    | FinSize.L
    | FinSize.M
    | FinSize.S
    | FinSize.XS = FinSize.XXL;

  /** Source of the avatar, can be URL or Base64 format. */
  @Input() image: string | null = '';

  protected firstNameLetter = '';
  protected lastNameLetter = '';

  protected get backgroundImage() {
    return this.image ? { 'background-image': 'url(' + this.image + ')' } : {};
  }
}
