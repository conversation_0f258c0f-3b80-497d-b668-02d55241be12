import { CommonModule } from '@angular/common';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinContainerModule } from '@fincloud/ui/container';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinSize } from '@fincloud/ui/types';
import {
  componentWrapperDecorator,
  moduleMetadata,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { FinCardLabelComponent } from './card-label.component';

const meta: Meta<FinCardLabelComponent> = {
  component: FinCardLabelComponent,
  title: 'Components/Card Label',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinCardLabelModule } from "@fincloud/ui/card-label"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6857-7668&t=8PBMODCjUOS8TJnA-4',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        FinContainerModule,
        FinButtonModule,
        FinIconModule,
        FinTruncateTextModule,
      ],
    }),
    componentWrapperDecorator(
      (story) => `<div class="fin-w-[800px]">${story}</div>`,
    ),
  ],
};
export default meta;
type Story = StoryObj<
  FinCardLabelComponent & {
    finCardIcon: string;
    finCardLabel: string;
  }
>;

export const Default: Story = {
  args: {
    label: 'FAQ',
    iconName: '',
    iconSrc: '',
    size: FinSize.S,
  },
  argTypes: {
    label: {
      control: { type: 'text' },
    },
    iconName: {
      control: { type: 'text' },
    },
    iconSrc: {
      control: { type: 'text' },
    },
    size: {
      options: [FinSize.S, FinSize.M],
      control: { type: 'select' },
    },
    finCardIcon: {
      description: 'Place the content to be rendered in the place of the icon.',
      table: {
        category: 'Templates',
      },
    },
    finCardLabel: {
      description:
        'Place the content to be rendered in the place of the label.',
      table: {
        category: 'Templates',
      },
    },
  },
  render: (args) => {
    return {
      props: args,
    };
  },
};

export const Examples: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: () => {
    return {
      template: `
        <div>
          <div finContainer class="fin-px-[2.4rem] fin-pb-[2.4rem] fin-mb-[2rem]">
            <fin-card-label
              label="FAQ"
            ></fin-card-label>

            <p>
             Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.
            </p>
          </div>

          <div finContainer class="fin-px-[2.4rem] fin-pb-[2.4rem] fin-mb-[2rem]">
            <fin-card-label
              label="FAQ"
              iconName="account_circle"
            ></fin-card-label>

            <p>
             Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.
            </p>
          </div>

          <div finContainer class="fin-px-[2.4rem] fin-pb-[2.4rem] fin-mb-[2rem]">
            <fin-card-label
              label="FAQ"
              size="m" 
            ></fin-card-label>

            <p>
             Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.
            </p>
          </div>

          <div finContainer class="fin-px-[2.4rem] fin-pb-[2.4rem] fin-mb-[2rem]">
            <fin-card-label
              label="FAQ"
              iconName="account_circle"
              size="m"   
            ></fin-card-label>

            <p>
             Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.
            </p>
          </div>        
        </div>
    `,
    };
  },
};

export const CustomIcon: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: () => {
    return {
      template: `
          <div finContainer class="fin-px-[2.4rem] fin-pb-[2.4rem] fin-mb-[2rem]">
            <fin-card-label
              label="FAQ"
              size="m"   
            >
              <ng-container finCardIcon>
                <button fin-button-action [size]="size" type="informative">
                  <fin-icon name="account_circle"></fin-icon>
                </button>
              </ng-container>
            </fin-card-label>

            <p>
             Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.
            </p>
          </div>          
    `,
    };
  },
};

export const CustomTruncatedLabel: Story = {
  args: {
    ...Default.args,
  },
  argTypes: {
    ...Default.argTypes,
  },
  render: () => {
    return {
      template: `
          <div finContainer class="fin-px-[2.4rem] fin-pb-[2.4rem] fin-mb-[2rem]">
            <fin-card-label
              size="m"   
            >
              <ng-container finCardLabel>
                <label class="fin-w-[16rem]" finTruncateText>
                  Long label to be truncated
                </label>
              </ng-container>
            </fin-card-label>

            <p>
             Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.
            </p>
          </div>          
    `,
    };
  },
};
