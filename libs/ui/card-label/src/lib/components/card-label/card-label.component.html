<div
  class="fin-flex fin-items-center fin-gap-[0.8rem] fin-text-color-text-primary fin-max-w-[90%]"
>
  @if (label) {
    <label class="fin-w-full" finTruncateText> {{ label }} </label>
  } @else {
    <ng-content select="[finCardLabel]"></ng-content>
  }

  @if (iconName || iconSrc) {
    <fin-icon [size]="size" [name]="iconName" [src]="iconSrc"></fin-icon>
  } @else {
    <ng-content select="[finCardIcon]"></ng-content>
  }
</div>
<hr finHorizontalSeparator class="fin-flex-grow" />
