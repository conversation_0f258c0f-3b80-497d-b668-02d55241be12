import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSeparatorsModule } from '@fincloud/ui/separators';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinSize } from '@fincloud/ui/types';

/**
 * A label component used within card layouts to display titles.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-card-label--docs Storybook Reference}
 */
@Component({
  selector: 'fin-card-label',
  standalone: true,
  imports: [
    CommonModule,
    FinSeparatorsModule,
    FinIconModule,
    FinTruncateTextModule,
  ],
  templateUrl: './card-label.component.html',
  styleUrl: './card-label.component.scss',
  host: {
    '[class]': 'cardCssClasses',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinCardLabelComponent {
  /** Card label. */
  @Input() label = '';

  /** Size of the badge. */
  @Input() size: FinSize.S | FinSize.M = FinSize.S;

  /** Name of an icon within a [Material Icons](https://fonts.google.com/icons) font set. */
  @Input() iconName = '';

  /** URL pointing to the icon source. */
  @Input() iconSrc = '';

  protected get cardCssClasses(): string {
    return `fin-card-label fin-card-label-${this.size}`;
  }
}
