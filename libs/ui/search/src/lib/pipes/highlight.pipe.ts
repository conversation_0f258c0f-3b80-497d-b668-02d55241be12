import { Pipe, PipeTransform } from '@angular/core';

/**
 * Transforms the input text by highlighting occurrences of the search term(s).
 * Matches are wrapped in a `<span>` element with a specific CSS class for styling.
 *
 * @remarks
 * - The search term(s) are treated as case-insensitive.
 * - Special characters in the search term(s) are escaped to avoid issues with regular expressions.
 * - Multiple search terms can be provided, separated by whitespace, and all matches will be highlighted.
 */
@Pipe({ name: 'highlight', standalone: true })
export class HighlightPipe implements PipeTransform {
  transform(text: unknown | string = '', search = ''): string {
    const targetText = text as string;
    // Nothing to highlight
    if (!search.trim()) {
      return targetText;
    }

    // Escape RegExp specials, split on whitespace, join with alternation (|) for one regex
    const pattern = search
      .trim()
      .split(/\s+/)
      .map((w) => w.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'))
      .join('|');

    // Build a single, case-insensitive / global regex
    const regex = new RegExp(pattern, 'gi');

    // Replace matches with <b>…</b>
    return targetText.replace(
      regex,
      (match) =>
        `<span class="fin-bg-color-background-failure-subtle">${match}</span>`,
    );
  }
}
