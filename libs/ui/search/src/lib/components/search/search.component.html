<mat-form-field
  class="fin-block fin-field"
  [ngClass]="'fin-field-size-' + size"
  [subscriptSizing]="dynamicErrorSpace"
>
  <div
    matPrefix
    class="fin-prefix fin-flex fin-items-center fin-justify-center fin-gap-[0.8rem]"
  >
    <fin-icon name="search"></fin-icon>
  </div>

  @if (autocomplete) {
    <input
      matInput
      [placeholder]="placeholder"
      [formControl]="control"
      [matAutocomplete]="autoComplete"
      (input)="onInputChange()"
      (focus)="onFocus()"
      (keyup.enter)="onPressEnter()"
      (blur)="blur($event)"
    />
    <mat-autocomplete
      #autoComplete="matAutocomplete"
      (optionSelected)="onAutocompleteOptionChange($event)"
      [displayWith]="displayFn"
      (opened)="onAutocompleteOpened()"
      (closed)="onAutocompleteClosed()"
      hideSingleSelectionIndicator
    >
      @if (options.length) {
        @for (option of options; track option[valuePropertyName]) {
          <mat-option
            [value]="option[valuePropertyName]"
            class="fin-px-[0.8rem]"
          >
            <span
              [innerHTML]="
                option[labelPropertyName] | highlight: formControl.value
              "
            ></span>
          </mat-option>
        }
      } @else {
        <mat-option [value]="control.value" class="fin-custom-message">
          @if (control.value && control.value.length >= 3) {
            @if (finNoResultsMessageDirective) {
              <ng-content select="[finNoResultsMessage]"></ng-content>
            } @else {
              {{ customMessages.noResultsMessage }}
            }
          } @else {
            @if (finInitialMessageDirective) {
              <ng-content select="[finInitialMessage]"></ng-content>
            } @else {
              {{ customMessages.initialSearchMessage }}
            }
          }
        </mat-option>
      }
    </mat-autocomplete>
  } @else {
    <input
      matInput
      type="text"
      [formControl]="control"
      [placeholder]="placeholder"
      (input)="onInputChange()"
      (focus)="onFocus()"
    />
  }

  <div
    matSuffix
    class="fin-suffix fin-flex fin-items-center fin-justify-end fin-gap-[0.8rem]"
    (click)="$event.stopPropagation()"
  >
    <ng-content select="[finFieldSuffix]"></ng-content>

    @if (numberOfResults || numberOfResults === 0) {
      <fin-badge-status
        class="fin-whitespace-nowrap"
        [type]="badgeType.DRAFT"
        [text]="
          numberOfResults +
          ' ' +
          (numberOfResults === 1
            ? customMessages.result
            : customMessages.results)
        "
        [size]="sizes.S"
      ></fin-badge-status>
    }

    @if (control.value?.length) {
      <button type="button" class="btn-clean" (click)="clearField($event)">
        <fin-icon
          [size]="sizes.S"
          name="close"
          class="fin-dropdown-icons fin-text-color-icons-primary"
        ></fin-icon>
      </button>
    }
    @if (showLoader) {
      <fin-loader></fin-loader>
    }
  </div>
</mat-form-field>
