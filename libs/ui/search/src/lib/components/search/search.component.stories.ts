import { CommonModule } from '@angular/common';
import { FormControl } from '@angular/forms';
import { FinButtonModule } from '@fincloud/ui/button';
import { FIN_CUSTOM_MESSAGES } from '@fincloud/ui/dropdown';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';
import {
  componentWrapperDecorator,
  moduleMetadata,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { FinSearchModule } from '../../search.module';
import { FinSearchComponent } from './search.component';

const meta: Meta = {
  component: FinSearchComponent,
  title: 'Components/Search',
  parameters: {
    docs: {
      description: {
        component: '`import { FinSearchModule } from "@fincloud/ui/search"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6168-1609&m=dev',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [CommonModule, FinSearchModule, FinButtonModule, FinIconModule],
      providers: [
        {
          provide: FIN_CUSTOM_MESSAGES,
          useValue: {
            initialSearchMessage:
              ' Enter 3 letters, 1 digit, or 1 special character to begin search.',
            noResultsMessage: 'No results found.',
            searchPlaceholder: 'Search',
            result: 'result',
            results: 'results',
          },
        },
      ],
    }),
    componentWrapperDecorator(
      (story) => `<div class="fin-w-[30rem]">${story}</div>`,
    ),
  ],
};
export default meta;
type Story = StoryObj<
  FinSearchComponent & {
    finInputSuffix: string;
    finInitialMessageDirective: string;
    finNoResultsMessageDirective: string;
  }
>;

export const Primary: Story = {
  args: {
    placeholder: 'Search',
    showLoader: false,
    numberOfResults: null,
    autocomplete: false,
    options: [],
    dynamicErrorSpace: 'dynamic',
    size: FinSize.M,
  },
  argTypes: {
    finInputSuffix: {
      description: 'Custom suffix template',
      table: {
        category: 'Templates',
      },
    },
    inputChange: {
      control: false,
    },
    inputFocus: {
      control: false,
    },
    autoCompleteOptionChange: {
      control: false,
    },
    pressEnter: {
      control: false,
    },
    dynamicErrorSpace: {
      options: ['dynamic', 'fixed'],
      control: { type: 'select' },
    },
    size: {
      options: [FinSize.M, FinSize.L],
      control: { type: 'select' },
    },
    numberOfResults: {
      control: { type: 'number' },
    },
    finInitialMessageDirective: {
      description: 'Custom message to show initially before options are loaded',
      table: {
        category: 'Templates',
      },
    },
    finNoResultsMessageDirective: {
      description: 'Custom message to show when there are no results',
      table: {
        category: 'Templates',
      },
    },
  },
  render: (args) => ({
    props: { ...args, formControl: new FormControl('') },
  }),
};

export const LoaderState: Story = {
  args: {
    ...Primary.args,
    showLoader: true,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => ({
    props: { ...args, formControl: new FormControl('') },
  }),
};

export const WithAutocompleteOptions: Story = {
  args: {
    ...Primary.args,
    autocomplete: true,
    options: [
      {
        label: 'Label 1',
        value: 'value1',
      },
      {
        label: 'Label 2',
        value: 'value2',
      },
    ],
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => ({
    props: { ...args, formControl: new FormControl('') },
  }),
};

export const WithoutAutocompleteOptions: Story = {
  args: {
    ...Primary.args,
    autocomplete: true,
    options: [],
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => ({
    props: { ...args, formControl: new FormControl('') },
  }),
};

export const WithCustomMessages: Story = {
  args: {
    ...Primary.args,
    autocomplete: true,
    options: [],
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => ({
    props: { ...args, formControl: new FormControl('') },
    template: `
      <fin-search 
        [formControl]="formControl"
        [autocomplete]="true"
        [options]="[]"
      >
          <ng-container finInitialMessage>Custom initial message</ng-container>
          <ng-container finNoResultsMessage>
            Custom message when there are no results
          </ng-container>
      </fin-search>
     `,
  }),
};

export const WithSuffix: Story = {
  args: {
    ...Primary.args,
    showLoader: true,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => ({
    props: { ...args, formControl: new FormControl('') },
    template: `
      <fin-search [formControl]="formControl">
        <ng-container finFieldSuffix>
          <button type="button" fin-button-icon size="xs">
            <fin-icon name="add"></fin-icon>
          </button>
        </ng-container>
      </fin-search>
     `,
  }),
};

export const WithBadge: Story = {
  args: {
    ...Primary.args,
    numberOfResults: 12,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => ({
    props: { ...args, formControl: new FormControl('') },
  }),
};
