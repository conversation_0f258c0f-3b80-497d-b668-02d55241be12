import { Overlay } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import {
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  ContentChild,
  EventEmitter,
  forwardRef,
  Inject,
  Injector,
  Input,
  Output,
  ViewChild,
} from '@angular/core';
import { NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import {
  MAT_AUTOCOMPLETE_SCROLL_STRATEGY,
  MatAutocompleteModule,
  MatAutocompleteSelectedEvent,
  MatAutocompleteTrigger,
} from '@angular/material/autocomplete';
import { FinBadgesModule, FinBadgeStatus } from '@fincloud/ui/badges';
import {
  FIN_CUSTOM_MESSAGES,
  FinCustomMessagesConfig,
} from '@fincloud/ui/dropdown';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinLoaderModule } from '@fincloud/ui/loader';
import {
  FinPreventScrollDirective,
  FinScrollbarService,
} from '@fincloud/ui/scrollbar';
import { FinErrorSpace, FinSize } from '@fincloud/ui/types';
import {
  createBlockScrollStrategyFactory,
  FinAngularMaterialModule,
} from '@fincloud/utils/angular-material';
import { FinControlValueAccessor } from '@fincloud/utils/control-value-accessor';
import { FinInitialMessageDirective } from '../../directives/fin-initial-message.directive';
import { FinNoResultsMessageDirective } from '../../directives/fin-no-results-message.directive';
import { FinSearchAutocompleteOption } from '../../models/fin-search-autocomplete-option';
import { HighlightPipe } from '../../pipes/highlight.pipe';

/**
 * A search field.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-search--docs Storybook Reference}
 */

@Component({
  selector: 'fin-search',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FinAngularMaterialModule,
    FinLoaderModule,
    FinIconModule,
    MatAutocompleteModule,
    FinBadgesModule,
    HighlightPipe,
  ],
  templateUrl: './search.component.html',
  styleUrl: './search.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FinSearchComponent),
      multi: true,
    },
    {
      provide: MAT_AUTOCOMPLETE_SCROLL_STRATEGY,
      deps: [Overlay],
      useFactory: createBlockScrollStrategyFactory,
    },
  ],
  hostDirectives: [
    {
      directive: FinPreventScrollDirective,
    },
  ],
  host: {
    class: 'fin-search',
  },
})
export class FinSearchComponent extends FinControlValueAccessor {
  /** The placeholder for this control. */
  @Input() placeholder = this.finCustomMessages.searchPlaceholder;

  /** Show/hide loader in the suffix of the input component.*/
  @Input({ transform: booleanAttribute }) showLoader = false;

  /** Show the number of results in the suffix of the input component.*/
  @Input() numberOfResults: number | null = null;

  /** Enables the autocomplete option. */
  @Input({ transform: booleanAttribute }) autocomplete = false;

  /** All of the defined select options. */
  @Input() options: FinSearchAutocompleteOption[] = [];

  /** A custom property name which holds the label. */
  @Input() labelPropertyName = 'label';

  /** A custom property name which holds the value. */
  @Input() valuePropertyName = 'value';

  /** Defines the size of the input field. */
  @Input() size: FinSize.M | FinSize.L = FinSize.M;

  /** Whether the form field should reserve space for error. */
  @Input() dynamicErrorSpace: FinErrorSpace = 'dynamic';

  /** Event emitted when the input value has been changed. */
  @Output() inputChange = new EventEmitter<string>();

  @Output() inputFocus = new EventEmitter<void>();

  /** Event emitted when the autocomplete option value has been changed. */
  @Output() autoCompleteOptionChange = new EventEmitter<string>();

  /** Event emitted when the enter button is pressed. */
  @Output() pressEnter = new EventEmitter<string>();

  @ViewChild(MatAutocompleteTrigger)
  protected matAutocompleteTrigger?: MatAutocompleteTrigger;

  @ContentChild(FinInitialMessageDirective)
  protected finInitialMessageDirective!: FinInitialMessageDirective;

  @ContentChild(FinNoResultsMessageDirective)
  protected finNoResultsMessageDirective!: FinNoResultsMessageDirective;

  protected sizes = FinSize;
  protected badgeType = FinBadgeStatus;
  protected customMessages!: FinCustomMessagesConfig;

  constructor(
    injector: Injector,
    private finScrollbarService: FinScrollbarService,

    @Inject(FIN_CUSTOM_MESSAGES)
    private finCustomMessages: FinCustomMessagesConfig,
  ) {
    super(injector);
    this.customMessages = this.finCustomMessages;
  }

  protected trackByValue(
    _: number,
    option: FinSearchAutocompleteOption,
  ): string {
    return option[
      this.valuePropertyName as keyof FinSearchAutocompleteOption
    ] as string;
  }

  protected displayFn = (controlValue: string) => {
    let selectedOption: FinSearchAutocompleteOption | undefined;

    if (controlValue) {
      selectedOption = this.options.find(
        (option) => option[this.valuePropertyName] === controlValue,
      );
    }
    return selectedOption
      ? (selectedOption[this.labelPropertyName] as string)
      : '';
  };

  protected onInputChange() {
    this.inputChange.emit(this.control.value);
  }

  protected onFocus() {
    this.inputFocus.emit();
  }

  protected onAutocompleteOptionChange(event: MatAutocompleteSelectedEvent) {
    this.autoCompleteOptionChange.emit(event.option.value);
  }

  protected onAutocompleteOpened(): void {
    this.finScrollbarService.disable();
  }

  protected onAutocompleteClosed(): void {
    this.finScrollbarService.enable();
  }

  protected clearField(event: Event) {
    event.stopPropagation();
    this.control.reset('');
    this.onInputChange();
  }

  protected onPressEnter() {
    this.pressEnter.emit(this.control.value);
    this.matAutocompleteTrigger?.closePanel();
  }
}
