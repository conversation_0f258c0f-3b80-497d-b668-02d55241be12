import {
  Component,
  EventEmitter,
  Injector,
  Input,
  Output,
  Type,
  ViewChild,
} from '@angular/core';
import {
  ControlContainer,
  ControlValueAccessor,
  FormControlDirective,
  UntypedFormControl,
} from '@angular/forms';

/**
 * This class implements the ControlValueAccessor interface to provide a bridge between Angular forms API and a native element in the DOM.
 * @template ControlValueAccessor
 */
@Component({ template: `` })
// eslint-disable-next-line @angular-eslint/component-class-suffix
export class FinControlValueAccessor implements ControlValueAccessor {
  /**
   * A reference to the FormControlDirective.
   */
  @ViewChild(FormControlDirective, { static: true })
  formControlDirective!: FormControlDirective;

  /**
   * The form control instance.
   */
  @Input() formControl!: UntypedFormControl;

  /**
   * The name of the form control.
   */
  @Input() formControlName!: string;

  /**
   * Event emitter for blur events.
   */
  @Output() blurred = new EventEmitter<any>();
  /**
   * Event emitter for focus events.
   */
  @Output() focused = new EventEmitter<any>();

  /**
   * A flag indicating whether the control is disabled.
   */
  isDisabled = false;

  /**
   * Returns the form control instance or the control container.
   */
  get control() {
    return (
      this.formControl ||
      this.controlContainer?.control?.get(this.formControlName)
    );
  }

  /**
   * Returns the control container.
   */
  get controlContainer() {
    if (this.injector.get(ControlContainer, undefined, { optional: true })) {
      return this.injector.get<ControlContainer>(
        ControlContainer as Type<ControlContainer>,
      );
    }

    return {} as ControlContainer;
  }

  /**
   * Class constructor.
   * @param {Injector} injector - The Angular injector.
   */
  constructor(private injector: Injector) {}

  /**
   * Registers a handler function that is called when the control is touched.
   * @param {() => void} fn - The function to be called on touch.
   */
  registerOnTouched(fn: () => void): void {
    if (!this.formControlDirective) return;
    this.formControlDirective.valueAccessor?.registerOnTouched(fn);
  }

  /**
   * Registers a handler function that is called when the control's value changes in the UI.
   * @param {() => void} fn - The function to be called on change.
   */
  registerOnChange(fn: () => void): void {
    if (!this.formControlDirective) return;
    this.formControlDirective.valueAccessor?.registerOnChange(fn);
  }

  /**
   * Writes a new value to the element.
   * @param {any} obj - The new value for the element.
   */
  writeValue(obj: any): void {
    if (!this.formControlDirective) return;
    this.formControlDirective.valueAccessor?.writeValue(obj);
  }

  /**
   * Disables the control. Part of the ControlValueAccessor interface.
   * @param {boolean} isDisabled - The disabled state.
   */
  setDisabledState(isDisabled: boolean): void {
    if (!this.formControlDirective) return;
    this.isDisabled = isDisabled;
    typeof this.formControlDirective.valueAccessor?.setDisabledState ===
      'function' &&
      this.formControlDirective.valueAccessor.setDisabledState(isDisabled);
  }

  /**
   * Emits the blur event.
   * @param {Event} event - The blur event.
   */
  blur(event: Event): void {
    const value = (event.target as any).value;
    this.blurred.emit(value);
  }

  /**
   * Emits the focus event.
   * @param {Event} event - The focus event.
   */
  focus(event: Event): void {
    const value = (event.target as any).value;
    this.focused.emit(value);
  }
}
