//TODO use lodash-es or implement own helpers
function neitherNullNorUndefined<T>(value: T | undefined | null): boolean {
  return value !== undefined && value !== null;
}

export function IgnoresNull<T>(): (
  target: T,
  key: string,
  descriptor: PropertyDescriptor
) => void {
  return function decorator<T>(
    target: T,
    key: string,
    descriptor: PropertyDescriptor
  ) {
    const original = descriptor.set;
    descriptor.set = function (...arg): void {
      if (original && arg.every(neitherNullNorUndefined)) {
        original.apply(this, arg);
      }
    };
  };
}
