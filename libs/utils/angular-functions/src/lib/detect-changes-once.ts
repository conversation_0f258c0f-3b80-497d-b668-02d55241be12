import { ChangeDetectorRef } from '@angular/core';

/**
 * Temporarily detaches the change detector, performs change detection once, and then reattaches it.
 * This avoids continuous change detection cycles in cases where it's only needed once.
 *
 * @param {ChangeDetectorRef} cdr - The Angular ChangeDetectorRef instance used to control change detection.
 */
export function detectChangesOnce(cdr: ChangeDetectorRef): void {
  cdr.detach();
  cdr.detectChanges();
  cdr.reattach();
}
