import { getZoneUnPatchedApi } from '../lib/zone-unpatched';

/**
 * Instance of the native JavaScript Promise constructor that is not patched by <PERSON><PERSON>'s zone.js.
 * Using this unpatched Promise avoids triggering Angular's change detection mechanism unnecessarily.
 * This can be beneficial in scenarios where you need to perform operations in a non-Angular-controlled context,
 * or where performance is critical and you want to reduce the change detection cycles.
 *
 * @example
 * // Usage example:
 * import { PromiseUnpatched } from '@fincloud/utils/unpatched-api';
 *
 * const nativePromise = new PromiseUnpatched((resolve, reject) => {
 *   // This code does not trigger Angular's change detection
 *   setTimeout(() => {
 *     resolve('Resolved without triggering change detection');
 *   }, 1000);
 * });
 *
 * nativePromise.then(result => console.log(result));
 *
 * @description
 * It is useful in situations where you want to perform background tasks or handling promises that should not impact
 * the Angular application's UI state. Since Angular's change detection remains unaffected, it reduces potential
 * performance overhead.
 */
/* eslint-disable-next-line @typescript-eslint/naming-convention */
export const PromiseUnpatched: PromiseConstructor = getZoneUnPatchedApi(
  'Promise' as keyof Window,
);
