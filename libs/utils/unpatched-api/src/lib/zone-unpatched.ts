/**
 * Retrieves the original unpatched version of a specified global API from Angular's zone.js.
 * This function allows access to the native implementations of APIs like `setTimeout`, `setInterval`, `Promise`, etc.,
 * bypassing the zone.js patching that integrates them with Angular's change detection mechanism.
 * This is particularly useful when you need to perform operations that should not trigger Angular's change detection,
 * thereby potentially improving the performance for non-UI related tasks.This function SHOULD NOT be used outside
 * of this library.
 *
 * @param {keyof Window} name - The name of the global API (key from the global `window` object) to retrieve.
 * @returns {any} The original unpatched API if it exists; otherwise, returns the possibly patched version.
 *
 * @example
 * // Example usage:
 * import { getZoneUnPatchedApi } from './zone-unpatched';
 *
 * const nativeSetTimeout = getZoneUnPatchedApi('setTimeout');
 * unpatchedSetTimeout(() => {
 *   console.log('This runs outside Angular\'s zone and does not trigger change detection.');
 * }, 1000);
 *
 * @description
 * When working within Angular applications, certain tasks can benefit from being executed outside of Angular's
 * change detection context to improve performance, especially for tasks not impacting UI or requiring UI updates.
 * This approach avoids the overhead of checks that are normally performed as a part of Angular's zone.js-driven
 * change detection processes.
 */
export function getZoneUnPatchedApi(name: keyof Window) {
  const unpatched = (window as any)['__zone_symbol__' + name];
  if (unpatched) {
    return unpatched;
  }
  // in case we try to get something that was never patched by zonejs
  return window[name];
}
