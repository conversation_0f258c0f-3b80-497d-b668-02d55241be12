import { Injectable } from '@angular/core';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';

@Injectable({
  providedIn: 'root',
})
export class FinIconRegistryService {
  constructor(
    private domSanitizer: DomSanitizer,
    private matIconRegistry: MatIconRegistry
  ) {}

  getSvgIcon(svgIconName: string, iconPath: string) {
    this.matIconRegistry.addSvgIcon(
      svgIconName,
      this.domSanitizer.bypassSecurityTrustResourceUrl(iconPath)
    );
  }
}
