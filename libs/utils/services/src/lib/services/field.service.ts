import { Injectable } from '@angular/core';
import { ValidationErrors } from '@angular/forms';
import { Observable, ReplaySubject, Subject, map } from 'rxjs';

@Injectable()
export class FinFieldService {
  value$ = new ReplaySubject<string>(1);
  controlErrors$ = new Subject<ValidationErrors | null>();

  private errorPriorities = {} as Record<string, number>;
  private _maxLength = 0;

  set maxLength(limit: number) {
    this._maxLength = limit > 0 ? limit : 0;
  }

  get maxLength() {
    return this._maxLength;
  }

  isInMaxLength(value: string): boolean {
    return this._maxLength > 0 && value.length < this._maxLength;
  }

  trimByMaxLength(value: string): string {
    if (this._maxLength > 0 && value.length > this._maxLength) {
      return value.slice(0, this._maxLength);
    }
    return value;
  }

  setErrorPriority(error: string, priority: number): void {
    this.errorPriorities = {
      ...this.errorPriorities,
      [error]: priority,
    };
  }

  getError(): Observable<string | null> {
    return this.controlErrors$.pipe(
      map((errors) => {
        let error = null;

        if (errors) {
          const errorKeys = Object.keys(errors);
          error = errorKeys[errorKeys.length - 1];
          if (Object.values(this.errorPriorities).some((el) => el > 1)) {
            error = this.sortErrors(errorKeys)[0];
          }

          return error;
        }

        return error;
      }),
    );
  }

  private sortErrors(errorKeys: string[]) {
    return errorKeys.sort(
      (a, b) => (this.errorPriorities[b] ?? 1) - (this.errorPriorities[a] ?? 1),
    );
  }
}
