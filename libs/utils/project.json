{"name": "utils", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/utils/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"tsConfig": "libs/utils/tsconfig.lib.json", "project": "libs/utils/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/utils/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/utils/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/utils/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}