import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import {
  MAT_RIPPLE_GLOBAL_OPTIONS,
  RippleGlobalOptions,
} from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTabsModule } from '@angular/material/tabs';
import { FIN_MAT_FORM_FIELD_DEFAULT_OPTIONS } from './utils/fin-mat-form-field-default-options';

@NgModule({
  imports: [CommonModule],
  exports: [MatInputModule, MatFormFieldModule, MatTabsModule],
  providers: [
    FIN_MAT_FORM_FIELD_DEFAULT_OPTIONS,
    {
      provide: MAT_RIPPLE_GLOBAL_OPTIONS,
      useValue: {
        disabled: true,
      } as RippleGlobalOptions,
    },
  ],
})
export class FinAngularMaterialModule {}
