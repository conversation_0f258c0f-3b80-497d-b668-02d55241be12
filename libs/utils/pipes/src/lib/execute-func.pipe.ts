import { Pipe, PipeTransform } from '@angular/core';

/*
 * Return value which will be returned by provided function
 * @param {value} value - value which will be provided as first parameter to the function
 * @param {func} func - function which will be invoke with provided params
 * @param {...args} ...args - rest(other) of the parameters needed which function is expected
 *
 * */
@Pipe({
  name: 'finExecuteFunc',
  standalone: true,
})
export class FinExecuteFuncPipe implements PipeTransform {
  transform<T, R, O>(
    value: T,
    func: (value: T, ...args: O[]) => R,
    ...args: O[]
  ) {
    return func(value, ...args);
  }
}
