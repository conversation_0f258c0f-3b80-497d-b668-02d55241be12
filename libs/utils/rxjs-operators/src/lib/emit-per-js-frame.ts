import { PromiseUnpatched } from '@fincloud/utils/unpatched-api';
import { Observable, UnaryFunction, debounce, pipe } from 'rxjs';

/**
 * Creates an RxJS operator that limits the rate of emissions from an observable sequence to at most once per JavaScript frame.
 * This is useful for throttling events that can occur very frequently (e.g., mouse move or scrolling),
 * and only the latest event in each frame is needed.
 *
 * This is achieved by using the `debounce` operator with a `PromiseUnpatched` which resolves immediately,
 * ensuring the emissions are deferred until the next JavaScript task execution.
 *
 * @template T The type of items in the observable sequence.
 * @returns {UnaryFunction<Observable<T>, Observable<T>>} An RxJS unary function (operator) that can be used
 * with the `pipe` method of an Observable to incorporate the frame-based emission logic.
 *
 * @example
 * // Usage with an RxJS Observable:
 * import { fromEvent } from 'rxjs';
 * import { emitPerJsFrame } from '@fincloud/utils/rxjs-operators';
 *
 * const mouseMove$ = fromEvent(document, 'mousemove').pipe(
 *   emitPerJsFrame()
 * );
 *
 * mouseMove$.subscribe(event => {
 *   console.log(event); // Logs the MouseEvent object at most once per JavaScript frame
 * });
 */
export function emitPerJsFrame<T>(): UnaryFunction<
  Observable<T>,
  Observable<T>
> {
  return pipe(debounce(() => PromiseUnpatched.resolve()));
}
