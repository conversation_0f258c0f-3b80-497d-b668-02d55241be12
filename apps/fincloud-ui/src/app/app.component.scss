.box {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #0000;
  border-radius: 8px;
  background:
    linear-gradient(#131219, #131219) padding-box,
    linear-gradient(var(--angle), #070707, #687aff) border-box;
  animation: 8s rotate linear infinite;
}

@keyframes rotate {
  to {
    --angle: 360deg;
  }
}

@property --angle {
  syntax: '<angle>';
  initial-value: 0deg;
  inherits: false;
}
