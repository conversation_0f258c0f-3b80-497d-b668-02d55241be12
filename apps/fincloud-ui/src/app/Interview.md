# Interview

## Questions

1. RxJS

- Subjects
- switchMap, mergeMap - when and why
- forkJoin
- async pipe or not
- refactoring
  1. shareReplay
  2. subscription within operator
  3. subscription within subscription
  4. Difference between rxjs subjects and observables. Is each new subscriber create own instance of the stream ? if yes , to which , if no to which ?
  5. There is an observable with value that I need to use in my component.ts, which is also used with my component.html via | async as an input to child component . How I can have same plain value to my component.ts ? If locally subscribe in the component.ts , why (pros/cons) ?

2. NgRX

- explain redux pattern
- selectors - how do you use feature selectors in selectors?
- do you have effect which listen for page action and dispatch page action
- derived state, do you keep state clean and use selectors to derive state?
- what is a side effect?

3. Change detection

- what triggers change detection?
- what is OnPush?
- how does OnPush work?

8. Dependency injection
   - difference between (Injection tokens) provider and services
   - Injection hierarchy - provideIn: root, provide in module, provide in component
9. Forms
   - setValue vs patchValue
   - have you created a custom validator or async validator.
10. Directives
11. Guards
12. Pipes

- pure vs impure
- difference between function and pipe in the template

16. Component's template
1. If we have 3 <ng-content></ng-content> within an element and we apply 3 child components thru projection, in which order they will be applied ? What if one of the child component appears late (surrounded with `@if | async`)
   example:

```html
<app-parent>
  <app-first></app-first>
  @if(someCondition$ | async) {
  <app-second></app-second>
  }
  <app-third></app-third>
</app-parent>
```

    2. If we have multiple <ng-content></ng-content> projections, but we want certain child component to be in specific <ng-content></ng-content> , how can we achieve that ?

17. Design patterns - where and why they were used?
18. TS Generics
19. Hoisting
20. Event-loop
21. Feature modules
22. Standalone components
23. Context guards

## Code snippets

**_RxJs_**

Level: **MID**

- missing async pipe
- missing unsubscribe
- better name
- missing interfaces
- dispatch within selector subscription

```ts
@Component({
  selector: 'app-cars',
  standalone: true,
  template: `
    @for (row of rows) {
      {{ row.name }}
    }
    ...
    @for (row of rows) {
      {{ row.color }}
    }
  `,
})
export class App implements OnInit {
  rows = [];
  userId;

  constructor(private store: Store) {
    this.store.select(selectUserId).subscribe((userId) => {
      this.userId = userId;
    });

    this.store.select(selectCountryId).subscribe((countryId) => {
      this.store.dispatch(loadCars({ userId: this.userId, countryId }));
    });

    this.store.select(selectCars).subscribe((cars) => {
      cars = cars.map((car) => {
        return { color: car.color, name: car.name };
      });

      this.rows = cars;
    });
  }

  ngOnInit() {}
}
```

Level: **SENIOR**

- missing async pipe
- missing unsubscribe
- better name
- missing interfaces
- dispatch within selector subscription

```ts
@Component({
  selector: 'app-cars',
  standalone: true,
  template: `
    @for (row of rows) {
      {{ row.name }}
    }
    ...
    @for (row of rows) {
      {{ row.color }}
    }

    @for (licensePlate of licensePlates$ | async) {
      {{ licensePlate }}
    }
  `,
})
export class App implements OnInit {
  rows = [];
  userId;
  licensePlates$ = new BehaviorSubject<string[]>([]);

  constructor(private store: Store) {
    this.store.select(selectUserId).subscribe((userId) => {
      this.userId = userId;
    });

    this.store.select(selectCountryId).pipe(tap(this.loadCars)).subscribe();

    this.store.select(selectCars).subscribe((cars) => {
      this.licensePlates$.next(cars.map((car) => car.licensePlate));

      cars = cars.map((car) => {
        return { color: car.color, name: car.name };
      });

      this.rows = cars;
    });
  }

  ngOnInit() {}

  loadCars(countryId) {
    this.store.dispatch(loadCars({ userId: this.userId, countryId }));
  }
}
```
