{"name": "fincloud-ui", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "apps/fincloud-ui/src", "tags": [], "targets": {"build": {"executor": "@angular-devkit/build-angular:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/fincloud-ui", "index": "apps/fincloud-ui/src/index.html", "browser": "apps/fincloud-ui/src/main.ts", "polyfills": ["apps/fincloud-ui/src/polyfills.ts"], "tsConfig": "apps/fincloud-ui/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["apps/fincloud-ui/src/favicon.ico", "apps/fincloud-ui/src/assets", {"glob": "**/*", "input": "libs/ui/assets/fonts/", "output": "assets/fonts"}], "styles": ["libs/ui/styles/src/lib/vendors/material/material-theme.scss", "apps/fincloud-ui/src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "1mb", "maximumError": "1mb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "fincloud-ui:build:production"}, "development": {"buildTarget": "fincloud-ui:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "fincloud-ui:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/fincloud-ui/jest.config.ts"}}}}