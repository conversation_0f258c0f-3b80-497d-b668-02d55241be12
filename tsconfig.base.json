{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@fincloud/ui": ["libs/ui/src/index.ts"], "@fincloud/ui/actions-menu": ["libs/ui/actions-menu/src/index.ts"], "@fincloud/ui/ai-shadow-effect": ["libs/ui/ai-shadow-effect/src/index.ts"], "@fincloud/ui/ai-suggestion": ["libs/ui/ai-suggestion/src/index.ts"], "@fincloud/ui/animations": ["libs/ui/animations/src/index.ts"], "@fincloud/ui/avatar-default": ["libs/ui/avatar-default/src/index.ts"], "@fincloud/ui/avatar-participants": ["libs/ui/avatar-participants/src/index.ts"], "@fincloud/ui/badges": ["libs/ui/badges/src/index.ts"], "@fincloud/ui/breadcrumb": ["libs/ui/breadcrumb/src/index.ts"], "@fincloud/ui/button": ["libs/ui/button/src/index.ts"], "@fincloud/ui/card-label": ["libs/ui/card-label/src/index.ts"], "@fincloud/ui/charts": ["libs/ui/charts/src/index.ts"], "@fincloud/ui/checkbox": ["libs/ui/checkbox/src/index.ts"], "@fincloud/ui/container": ["libs/ui/container/src/index.ts"], "@fincloud/ui/date-picker": ["libs/ui/date-picker/src/index.ts"], "@fincloud/ui/directory": ["libs/ui/directory/src/index.ts"], "@fincloud/ui/document": ["libs/ui/document/src/index.ts"], "@fincloud/ui/document-classification": ["libs/ui/document-classification/src/index.ts"], "@fincloud/ui/drag-drop": ["libs/ui/drag-drop/src/index.ts"], "@fincloud/ui/dropdown": ["libs/ui/dropdown/src/index.ts"], "@fincloud/ui/empty-state": ["libs/ui/empty-state/src/index.ts"], "@fincloud/ui/expansion-panel": ["libs/ui/expansion-panel/src/index.ts"], "@fincloud/ui/field-message": ["libs/ui/field-message/src/index.ts"], "@fincloud/ui/file-upload": ["libs/ui/file-upload/src/index.ts"], "@fincloud/ui/filter-tabs": ["libs/ui/filter-tabs/src/index.ts"], "@fincloud/ui/header-and-footer": ["libs/ui/header-and-footer/src/index.ts"], "@fincloud/ui/icon": ["libs/ui/icon/src/index.ts"], "@fincloud/ui/input": ["libs/ui/input/src/index.ts"], "@fincloud/ui/loader": ["libs/ui/loader/src/index.ts"], "@fincloud/ui/menu-item": ["libs/ui/menu-item/src/index.ts"], "@fincloud/ui/modal": ["libs/ui/modal/src/index.ts"], "@fincloud/ui/observers": ["libs/ui/observers/src/index.ts"], "@fincloud/ui/paginator": ["libs/ui/paginator/src/index.ts"], "@fincloud/ui/percentage-loader": ["libs/ui/percentage-loader/src/index.ts"], "@fincloud/ui/progress-bar": ["libs/ui/progress-bar/src/index.ts"], "@fincloud/ui/radio": ["libs/ui/radio/src/index.ts"], "@fincloud/ui/range-slider": ["libs/ui/range-slider/src/index.ts"], "@fincloud/ui/scrollbar": ["libs/ui/scrollbar/src/index.ts"], "@fincloud/ui/search": ["libs/ui/search/src/index.ts"], "@fincloud/ui/separators": ["libs/ui/separators/src/index.ts"], "@fincloud/ui/side-panel": ["libs/ui/side-panel/src/index.ts"], "@fincloud/ui/slide-toggle": ["libs/ui/slide-toggle/src/index.ts"], "@fincloud/ui/snippets": ["libs/ui/snippets/src/index.ts"], "@fincloud/ui/split-button": ["libs/ui/split-button/src/index.ts"], "@fincloud/ui/stepper-field": ["libs/ui/stepper-field/src/index.ts"], "@fincloud/ui/styles": ["libs/ui/styles/src/index.ts"], "@fincloud/ui/switch-toggle": ["libs/ui/switch-toggle/src/index.ts"], "@fincloud/ui/table": ["libs/ui/table/src/index.ts"], "@fincloud/ui/tabs": ["libs/ui/tabs/src/index.ts"], "@fincloud/ui/text-area": ["libs/ui/text-area/src/index.ts"], "@fincloud/ui/toast": ["libs/ui/toast/src/index.ts"], "@fincloud/ui/toolbar": ["libs/ui/toolbar/src/index.ts"], "@fincloud/ui/tooltip": ["libs/ui/tooltip/src/index.ts"], "@fincloud/ui/tree-menu": ["libs/ui/tree-menu/src/index.ts"], "@fincloud/ui/truncate-text": ["libs/ui/truncate-text/src/index.ts"], "@fincloud/ui/types": ["libs/ui/types/src/index.ts"], "@fincloud/ui/warning-message": ["libs/ui/warning-message/src/index.ts"], "@fincloud/ui/zoom-bar": ["libs/ui/zoom-bar/src/index.ts"], "@fincloud/utils": ["libs/utils/src/index.ts"], "@fincloud/utils/angular-functions": ["libs/utils/angular-functions/src/index.ts"], "@fincloud/utils/angular-material": ["libs/utils/angular-material/src/index.ts"], "@fincloud/utils/control-value-accessor": ["libs/utils/control-value-accessor/src/index.ts"], "@fincloud/utils/decorators": ["libs/utils/decorators/src/index.ts"], "@fincloud/utils/functions": ["libs/utils/functions/src/index.ts"], "@fincloud/utils/pipes": ["libs/utils/pipes/src/index.ts"], "@fincloud/utils/rxjs-operators": ["libs/utils/rxjs-operators/src/index.ts"], "@fincloud/utils/services": ["libs/utils/services/src/index.ts"], "@fincloud/utils/storybook": ["libs/utils/storybook/src/index.ts"], "@fincloud/utils/styles": ["libs/utils/styles/src/index.ts"], "@fincloud/utils/unpatched-api": ["libs/utils/unpatched-api/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}